import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import "eslint-plugin-only-warn";
import prettierConfig from "eslint-config-prettier";
import unusedImports from "eslint-plugin-unused-imports";
import importPlugin from "eslint-plugin-import";

export default [
	// START: Default configuration
	{ files: ["**/*.{js,mjs,cjs,ts,jsx,tsx}"] },
	{ languageOptions: { globals: { ...globals.browser, ...globals.node } } },
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	pluginReact.configs.flat.recommended,
	prettierConfig,
	importPlugin.flatConfigs.typescript,
	// END: Default configuration
	{
		plugins: {
			// To auto-remove unused imports
			"unused-imports": unusedImports,
			import: importPlugin,
		},
		rules: {
			// unused-imports plugins auto removes unused imports on eslint fix (commit)
			"unused-imports/no-unused-imports": "warn",
			"unused-imports/no-unused-vars": "off",
			// css property from emotioncss gets flagged as unknown property
			"react/no-unknown-property": ["error", { ignore: ["css"] }],
			// console.log is a warning. Use console.info.
			"no-console": ["warn"],
			// Prevent relative imports between different packages (use pigbot-core/foo instead of ../foo)
			"import/no-relative-packages": "warn",
		},
		settings: {
			react: {
				version: "detect", // Automatically detect the React version
			},
		},
	},
	{
		ignores: [
			// Ignore generated files
			"packages/frontend/src/gql",
			"packages/proto/server",
			"packages/proto/web",
			// Ignore dev files
			"**/dev/**",
		],
	},
];
