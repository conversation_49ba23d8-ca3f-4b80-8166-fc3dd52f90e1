name: Beta build

on:
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: self-hosted
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}
    steps:
      - uses: actions/checkout@v4

      - name: Get current timestamp in numeric format
        id: timestamp
        run: |
          echo "TIMESTAMP=$(date -u +'%Y%m%d%H%M%S')" >> "$GITHUB_OUTPUT"

      - name: Login to docker registry
        uses: docker/login-action@v3
        with:
          registry: registry.cloudfarms.online
          username: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_USERNAME }}
          password: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_PASSWORD }}

      - name: Build and push backend Docker image
        run: |
          docker build . --file Dockerfile-packages --target backend --tag registry.cloudfarms.online/pigbot-backend-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
          docker push registry.cloudfarms.online/pigbot-backend-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}

      - name: Build and push worker Docker image
        run: |
          docker build . --file Dockerfile-packages --target worker --tag registry.cloudfarms.online/pigbot-worker-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
          docker push registry.cloudfarms.online/pigbot-worker-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}

      - name: Build and push frontend Docker image
        run: |
          docker build . --file Dockerfile-packages --target frontend --tag registry.cloudfarms.online/pigbot-frontend-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }} --build-arg BUILD_VERSION=${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }} --build-arg FEDERATION_MODULE_NAME=virtualAssistantBeta --build-arg BACKEND_BASE_URL=/pigbot-beta/backend
          docker push registry.cloudfarms.online/pigbot-frontend-beta:${{ steps.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
