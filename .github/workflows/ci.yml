name: CI

on:
  push:
    branches: ["main"]
  workflow_dispatch:

jobs:
  timestamp:
    runs-on: ubuntu-latest
    outputs:
      TIMESTAMP: ${{ steps.timestamp.outputs.TIMESTAMP }}
    steps:
      - name: Get current timestamp in numeric format
        id: timestamp
        run: |
          echo "TIMESTAMP=$(date -u +'%Y%m%d%H%M%S')" >> "$GITHUB_OUTPUT"

  hasura:
    runs-on: self-hosted
    needs:
      - timestamp
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}
    steps:
      - uses: actions/checkout@v4
      - name: Login to docker registry
        uses: docker/login-action@v3
        with:
          registry: registry.cloudfarms.online
          username: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_USERNAME }}
          password: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_PASSWORD }}
      - name: Build the Docker image
        run: docker build . --file Dockerfile-hasura --tag registry.cloudfarms.online/pigbot-hasura:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
      - name: Push the Docker image
        run: docker push registry.cloudfarms.online/pigbot-hasura:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}

  backend:
    runs-on: self-hosted
    needs:
      - timestamp
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}
    steps:
      - uses: actions/checkout@v4
      - name: Login to docker registry
        uses: docker/login-action@v3
        with:
          registry: registry.cloudfarms.online
          username: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_USERNAME }}
          password: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_PASSWORD }}
      - name: Build the Docker image
        run: docker build . --file Dockerfile-packages --target backend --tag registry.cloudfarms.online/pigbot-backend:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
      - name: Push the Docker image
        run: docker push registry.cloudfarms.online/pigbot-backend:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}

  worker:
    runs-on: self-hosted
    needs:
      - timestamp
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}
    steps:
      - uses: actions/checkout@v4
      - name: Login to docker registry
        uses: docker/login-action@v3
        with:
          registry: registry.cloudfarms.online
          username: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_USERNAME }}
          password: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_PASSWORD }}
      - name: Build the Docker image
        run: docker build . --file Dockerfile-packages --target worker --tag registry.cloudfarms.online/pigbot-worker:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
      - name: Push the Docker image
        run: docker push registry.cloudfarms.online/pigbot-worker:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}

  frontend:
    runs-on: self-hosted
    needs:
      - timestamp
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}
    steps:
      - uses: actions/checkout@v4
      - name: Login to docker registry
        uses: docker/login-action@v3
        with:
          registry: registry.cloudfarms.online
          username: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_USERNAME }}
          password: ${{ secrets.DOCKER_REGISTRY_CLOUDFARMS_ONLINE_PASSWORD }}
      - name: Build the Docker image
        run: docker build . --file Dockerfile-packages --target frontend --tag registry.cloudfarms.online/pigbot-frontend:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }} --build-arg BUILD_VERSION=${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
      - name: Push the Docker image
        run: docker push registry.cloudfarms.online/pigbot-frontend:${{ needs.timestamp.outputs.TIMESTAMP }}-${{ github.sha }}
