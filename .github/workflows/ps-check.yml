name: PR Check

on:
  pull_request:
  workflow_dispatch:

jobs:
  check:
    runs-on: ubuntu-latest
    env:
      DOCKER_AUTH_CONFIG: ${{ secrets.DOCKER_AUTH_CONFIG }}

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9

      - name: Install dependencies
        run: pnpm install

      - name: Run checks
        run: |
          mkdir -p ~/.docker
          echo "$DOCKER_AUTH_CONFIG" > ~/.docker/config.json
          pnpm run check-all
