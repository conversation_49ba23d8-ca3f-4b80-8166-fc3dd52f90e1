services:
  ## HASURA START ----------------------------------------------
  postgres:
    image: postgres:17
    restart: always
    ports:
      - "6543:5432"
    volumes:
      - db_data-17:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: postgrespassword

  graphql-engine:
    image: hasura/graphql-engine:v2.40.0
    depends_on:
      - postgres
    ports:
      - "8080:8080"
    restart: always
    environment:
      ## postgres database to store Hasura metadata
      HASURA_GRAPHQL_METADATA_DATABASE_URL: **************************************************/postgres
      ## this env var can be used to add the above postgres database to Hasura as a data source. this can be removed/updated based on your needs
      PG_DATABASE_URL: **************************************************/postgres
      ## enable the console served by server
      HASURA_GRAPHQL_ENABLE_CONSOLE: "false" # set to "false" to disable console
      ## enable debugging mode. It is recommended to disable this in production
      HASURA_GRAPHQL_DEV_MODE: "true"
      HASURA_GRAPHQL_ENABLED_LOG_TYPES: startup, http-log, webhook-log, websocket-log, query-log
      ## uncomment next line to run console offline (i.e load console assets from server instead of CDN)
      # HASURA_GRAPHQL_CONSOLE_ASSETS_DIR: /srv/console-assets
      ## uncomment next line to set an admin secret
      HASURA_GRAPHQL_ADMIN_SECRET: myadminsecretkey
      HASURA_GRAPHQL_JWT_SECRET: '{
        "type": "HS256",
        "key": "3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R",
        "claims_map": {
          "x-hasura-allowed-roles": [
            "user",
            "power-user",
            "backoffice"
          ],
          "x-hasura-default-role": {"path": "$.role"},
          "x-hasura-user-id": {"path": "$.userId"},
          "x-hasura-root-id": {"path": "$.rootId"},
          "x-hasura-farm-id": {"path": "$.farmId"}
        }
      }'

  ## HASURA END ----------------------------------------------

  redis:
    image: redis:7.2.5
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  ## TEMPORAL START --------------------------------------------
  postgresql:
    container_name: temporal-postgresql
    environment:
      POSTGRES_PASSWORD: temporal
      POSTGRES_USER: temporal
    image: postgres:${POSTGRESQL_VERSION}
    networks:
      - temporal-network
    expose:
      - 5432
    volumes:
      - temporal-pg-125:/var/lib/postgresql/data
  temporal:
    container_name: temporal
    depends_on:
      - postgresql
    environment:
      - DB=postgres12
      - DB_PORT=5432
      - POSTGRES_USER=temporal
      - POSTGRES_PWD=temporal
      - POSTGRES_SEEDS=postgresql
      - DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development-sql.yaml
      - JWT_SECRET=3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R
    image: registry.cloudfarms.online/temporal/auto-setup:sha-47d3921
    networks:
      - temporal-network
    ports:
      - 7233:7233
    volumes:
      - ./temporal-dynamicconfig:/etc/temporal/config/dynamicconfig
  temporal-admin-tools:
    container_name: temporal-admin-tools
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CLI_ADDRESS=temporal:7233
    image: temporalio/admin-tools:1.25.2-tctl-1.18.1-cli-1.1.1
    networks:
      - temporal-network
    stdin_open: true
    tty: true
  temporal-ui:
    container_name: temporal-ui
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000
    image: temporalio/ui:2.29.2
    networks:
      - temporal-network
    ports:
      - 8233:8080

## TEMPORAL END --------------------------------------------

volumes:
  db_data-17: # version 17 of postgres
  redis_data:
  temporal-pg-125: # -125 means version 1.25 of temporal which was incompatible with old database data
networks:
  temporal-network:
    driver: bridge
    name: temporal-network