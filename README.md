# Virtual Farm Assistant

**`node` installation:**

```bash
nvm install --lts # Install the latest LTS version of node
nvm use --lts # Use the latest LTS version of node
corepack enable pnpm # Enable pnpm as the package manager
```

```bash
pnpm install
```

**Start everything:**

```bash
pnpm run start
```

**Serving for PigMan during development:**

Run frontend in serve mode:

```bash
pnpm run serve
```

This enables the module federation plugin that breaks HMR and also builds the app in production mode.

## Running things individually

````bash

**Start 3rd party dependencies via docker:**

```bash
pnpm run deps
````

This will also apply hasura migrations and metadata and start hasura console.

**In two separate terminals run:**

```bash
cd packages/backend
pnpm run start
```

```bash
cd packages/frontend
pnpm run start
```

or to run frontend in serve mode:

```bash
cd packages/frontend
pnpm run serve
```

**Running hasura console**

To see the data in DB, easiest is to use hasura conosole.
It should be running on http://localhost:9695/, whenever your app is running.
If not, try starting 3rd party dependencies via docker again.
