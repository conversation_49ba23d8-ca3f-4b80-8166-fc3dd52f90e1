import { downloadAllArticles } from "pigbot-worker/src/dixa/DixaPullWorkflow";
import { TEMPORAL } from "pigbot-core/src/temporal/TemporalKeys";
import { PIGBOT_WORKER_QUEUE, startOrAwaitWorkflow } from "pigbot-core/src/temporal/temporal";
import Config from "pigbot-core/src/Config";

export async function reloadDixaArticles() {
	const errors = await startOrAwaitWorkflow(PIGBOT_WORKER_QUEUE, TEMPORAL.DIXA_ARTICLES_WORKFLOW, downloadAllArticles, [
		Config.OPENAI_ASSISTANT_ID,
	]);
	if (errors.length > 10) {
		throw new Error(errors.slice(0, 10).join("\n") + "... and more"); // Truncating errors for readability.
	} else if (errors.length > 0) {
		throw new Error(errors.join("\n"));
	}
}
