import { createRun, createThread, getAssistant, sendMessageToThread, updateAssistant } from "pigbot-core/src/llm/ChatGptClient";
import {
	Annotation,
	ChatbotMessageParams,
	FilesNotReady,
	FilesReady,
	OpenAiIdElevioId,
	SupportChatbotResponseChunk,
} from "pigbot-core/src/supportbot/SupportChatbotTypes";
import { WebClient } from "@slack/web-api";
import { convertGFMToSlack } from "../utils/gfmToSlack";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import path from "node:path";
import logger from "pigbot-core/src/logger";
import { UserContext } from "pigbot-core/src/UserContext";
import { cachePromise } from "pigbot-core/src/Cache";
import Config from "pigbot-core/src/Config";
import { getCacheString, getElevioLink } from "pigbot-core/src/supportbot/SupportChatbotUtils";
import { renderPromptTemplate, TemplateOverrides } from "pigbot-core/src/prompt-template/PromptTemplate";
import { SystemHoldingId } from "pigbot-core/src/HoldingId";

export async function numberOfFiles(): Promise<FilesNotReady | FilesReady> {
	const filesMap = await getFilesMap();
	if (filesMap === undefined) {
		return { type: "filesNotReady" };
	} else {
		return { type: "filesReady", numberOfFiles: filesMap.length };
	}
}

async function getFilesMap(): Promise<OpenAiIdElevioId[] | undefined> {
	const cache = await cachePromise;
	return await cache.get<OpenAiIdElevioId[]>(getCacheString(Config.OPENAI_ASSISTANT_ID));
}

export async function* supportChatbotService(
	params: ChatbotMessageParams,
	userContext: UserContext,
	templateOverrides?: TemplateOverrides,
): AsyncGenerator<SupportChatbotResponseChunk> {
	const filesMap = await getFilesMap();

	yield { type: "queryStarted" };
	const activeTemplate = await renderPromptTemplate({
		promptId: {
			promptType: "support-chatbot-template",
			holdingId: SystemHoldingId,
			templateOverrides: templateOverrides,
		},
		optional: false,
	});

	const currentAssistant = await getAssistant(Config.OPENAI_ASSISTANT_ID);

	if (activeTemplate.template.template !== currentAssistant.instructions) {
		await updateAssistant(Config.OPENAI_ASSISTANT_ID, { instructions: activeTemplate.template.template });
	}

	const threadId = params.threadId ? params.threadId : await createThread();
	yield { type: "threadId", threadId };

	await sendMessageToThread(threadId, params.message);

	const run = createRun(threadId, Config.OPENAI_ASSISTANT_ID);

	let aiMessageAccumulator = "";
	const annotationAccumulator: Annotation[] = [];

	for await (const chunk of run) {
		if (chunk.event === "thread.message.delta") {
			const message = chunk.data.delta.content;
			const messagesAndAnnotations = message
				?.map((content) => {
					if (content.type === "text") {
						//context.text may contain annotations: []
						let annotations: Annotation[] = [];
						if (content.text?.annotations) {
							annotations = content.text?.annotations
								.filter((ann) => ann.type === "file_citation")
								.map((annotation) => {
									if (annotation.file_citation !== undefined) {
										const article = filesMap?.find(({ openAiId }) => openAiId === annotation.file_citation?.file_id);
										return {
											index: annotation.index,
											fileId: annotation.file_citation?.file_id,
											startIndex: annotation.start_index,
											endIndex: annotation.end_index,
											elevioLink: article ? getElevioLink(article.elevioId) : undefined,
											title: article?.title,
										} as Annotation;
									}
								})
								.filter((value) => value !== undefined);
						}
						return { text: annotations.length > 0 ? undefined : content.text?.value, annotations: annotations }; //The text is ommited, if real annotations are send, because chatgpt is sending weird text. But the first message contains empty array for annotations for some reason.
					}
				})
				.filter((value) => value !== undefined);
			if (messagesAndAnnotations) {
				for (const { text, annotations } of messagesAndAnnotations) {
					if (text !== undefined) {
						aiMessageAccumulator += text;
						yield text;
					}

					annotations.forEach((annotation) => {
						if (annotation.elevioLink !== null && !annotationAccumulator.some((a) => a.elevioLink === annotation.elevioLink)) {
							annotationAccumulator.push(annotation);
						}
					});
				}
			}
		}
	}

	if (annotationAccumulator.length > 0) {
		const annotationText = renderTemplateFile(path.resolve(__dirname, "annotations.hbs"), {
			userContext,
			annotations: annotationAccumulator,
		});
		aiMessageAccumulator += annotationText; //annotations should also be send to slack
		yield { type: "annotationSummary", annotationsSummary: annotationText };
	}
	yield { type: "queryEnded" };
	if (Config.SLACK_BOT_TOKEN !== undefined && params.sendToSlack)
		await sendMessageToSlack(params.message, aiMessageAccumulator, threadId, userContext);
}

async function sendMessageToSlack(userMessage: string, aiReply: string, threadId: string, userContext: UserContext) {
	const cache = await cachePromise;
	const slackThreadId = await cache.get<string>(threadId);
	try {
		const slackClient = new WebClient(Config.SLACK_BOT_TOKEN!);

		const aiReplyConverted = convertGFMToSlack(aiReply);

		const slackResponse = await slackClient.chat.postMessage({
			channel: "#support-chatbot-conversations",
			text: renderTemplateFile(path.resolve(__dirname, "slack-chatbot.hbs"), {
				userContext,
				userQuestion: userMessage,
				aiReply: aiReplyConverted,
			}),
			thread_ts: slackThreadId,
		});

		await cache.set(threadId, slackResponse.ts, Config.SUPPORT_CHATBOT_THREAD_CACHE_TTL);
	} catch (error) {
		logger.error("Error sending message to Slack: ", error);
	}
}
