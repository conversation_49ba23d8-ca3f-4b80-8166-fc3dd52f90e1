import "pigbot-core/test/jest.setup";
import { SupportChatbotResponseChunk } from "pigbot-core/src/supportbot/SupportChatbotTypes";
import { TestData } from "pigbot-core/test/TestData";

//maybe we should do test for thread. Ask for one answer, remember threadId, and ask another question. Possibly ask for something about CF, and test also annotations.

test(
	"getChatbotResponse",
	async () => {
		const { supportChatbotService } = await import("./SupportChatbotService");
		const response = supportChatbotService({ message: "test", sendToSlack: false }, TestData.testUserContext);

		const result: SupportChatbotResponseChunk[] = [];
		for await (const chunk of response) {
			result.push(chunk);
		}

		// This code checks, if the service has returned all the states it should (we can not check the annotations, they are usually not present in these test queries
		expect(result).toEqual(
			expect.arrayContaining([
				expect.objectContaining({ type: "queryStarted" }),
				expect.objectContaining({ type: "threadId" }),
				expect.objectContaining({ type: "queryEnded" }),
			]),
		);

		// This code checks, if at least some answer came from AI
		expect(result.some((chunk) => typeof chunk === "string")).toBe(true);
	},
	2 * 60 * 1000,
);
