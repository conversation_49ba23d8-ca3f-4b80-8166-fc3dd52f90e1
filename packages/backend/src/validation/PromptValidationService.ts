import { sql } from "pigbot-core/src/database";
import { TemplateOverrides } from "pigbot-core/src/prompt-template/PromptTemplate";
import { AllFeedback, PersistedResponseData } from "../feedback/FeedbackService";
import { analyzeEfficiencyReport } from "pigbot-core/src/eff-report/EfficiencyReportService";
import { extractTextFromAsyncGenerator, extractTextFromStream } from "pigbot-core/src/utils/extractTextFromStream";
import { guessEnvFromRootId } from "pigbot-core/src/UserContext";
import { findSection, Section, splitMarkdownV2, splitMarkdownV3 } from "pigbot-core/src/feedback/splitMarkdown";
import { match, P } from "ts-pattern";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import path from "node:path";
import { ENTIRE_RESPONSE_KEY, GOOD_RESPONSE } from "pigbot-core/src/Constants";
import { structuredCompletionChatGpt } from "pigbot-core/src/llm/ChatGptClient";
import { z } from "zod";

/**
 * Validates the efficiency report summarizations.
 * Returns a list of all feedback and comments inside the feedback. For each comment, there is a resolution status.
 */
export async function validateEffReportSummarizations(templateOverrides: TemplateOverrides, idxOffset: number = 0) {
	// Read all feedbacks from the database
	const feedbacksRows = await sql<
		{
			response: PersistedResponseData;
			farm_id: number;
			root_id: number;
			feedback: AllFeedback;
			id: string;
			summary: string;
			farm_name: string;
			created_at: string;
			author_name: string;
		}[]
	>`SELECT response,
					 farm_id,
					 root_id,
					 feedback,
					 id,
					 summary,
					 farm_name,
					 created_at,
					 author_name
		FROM response_feedback
		ORDER BY created_at DESC
	`;

	const validations = await Promise.all(
		feedbacksRows.map(async (feedbackRow) => {
			// TODO Read from DB - this is just a guess. We need to start storing this in the DB first.
			const env = guessEnvFromRootId(feedbackRow.root_id);

			// Generate new responses for the report for which the feedback was given
			const newResponses = await Promise.all(
				[0, 1, 2].map(async (idx) => {
					const stream = analyzeEfficiencyReport({
						request: {
							report: feedbackRow.response.reportData,
							idx: idx + idxOffset,
						},
						templateOverrides,
						farmHoldingId: {
							env,
							rootId: feedbackRow.root_id,
							farmId: feedbackRow.farm_id,
						},
					});

					const newResponse = await extractTextFromAsyncGenerator(stream);
					return newResponse;
				}),
			);

			const originalResponse = match(feedbackRow.response)
				.with({ version: P._ }, (v) => extractTextFromStream(v.internalResponseStreamArray))
				.with({ responseText: P.string }, (v) => v.responseText)
				.exhaustive();

			const sections: Section[] = match(feedbackRow.response)
				.with({ version: "vfa-v3" }, () => splitMarkdownV3(originalResponse))
				.otherwise(() => splitMarkdownV2(originalResponse)); //split is the same for v2 and v1

			// A list of feedbacks for each section
			const sectionFeedbacks = Object.entries(feedbackRow.feedback).map(([sectionId, feedback]) => {
				const sectionText =
					sectionId === ENTIRE_RESPONSE_KEY ? "This feedback is for the entire response" : findSection(sectionId, sections);
				if (sectionText === undefined) throw new Error(`Section with id ${sectionId} not found`);
				return {
					sectionId,
					sectionText,
					feedback: feedback === GOOD_RESPONSE ? "This is correct." : feedback,
				};
			});

			const prompt = renderTemplateFile(path.resolve(__dirname, "validation.hbs"), {
				newResponses,
				originalResponse,
				sectionFeedbacks,
			});

			// How the feedback was resolved in new responses
			const { response: validationPromptResponses } = await structuredCompletionChatGpt({
				// For some feedbacks the AI says the feedback was resolved one time and unresolved the next time.
				// Multiple responses are generated to detect this unambiguity.
				n: 3,
				zodObject: z.object({
					feedbackResolutions: z.array(
						z.object({
							sectionId: z.string(),
							resolution: z
								.enum(["fully-resolved", "not-resolved", "unknown"])
								.describe(
									"Whether the feedback has been resolved at least in some of the new summarizations.\n" +
										"Positive feedback is considered resolved if the section is contained in some of the new summarizations. " +
										"It doesn't have to be written exactly the same way, but the message should be the same.\n" +
										"Feedback that suggests changes is resolved if the changes have been included in some of the new summarizations.\n" +
										"Feedback that criticizes the summarization is resolved if the critique no longer applies to new summarizations.\n" +
										"Resolution is 'unknown' if is is not perfectly clear what the feedback meant in context of the original summarization.",
								),
							explanation: z.string().describe("In depth explanation of the resolution."),
						}),
					),
				}),
				objectName: "feedbackResolutions",
				messages: [
					{
						role: "user",
						content: prompt,
					},
				],
				metricData: {
					activity: "automated-prompt-engineering",
					action: "validation",
				},
			});

			return {
				feedbackId: feedbackRow.id,
				summary: feedbackRow.summary,
				farmName: feedbackRow.farm_name,
				createdAt: feedbackRow.created_at,
				authorName: feedbackRow.author_name,
				// This goes through all responses to see if some resolutions are ambiguous.
				// Resolution of ambiguous responses is set to unknown.
				feedbackResolutions: validationPromptResponses.reduce(
					(acc, res) => {
						return res.feedbackResolutions.reduce((acc, r) => {
							const sectionText =
								r.sectionId === ENTIRE_RESPONSE_KEY ? "This feedback is for the entire response" : findSection(r.sectionId, sections);
							if (sectionText === undefined) throw new Error(`Section with id ${r.sectionId} not found`);

							const currentValidation = acc[r.sectionId];

							if (currentValidation === undefined) {
								// This is the first time feedback for a sections is encountered.
								// It is added to the result record.
								acc[r.sectionId] = {
									...r,
									sectionText,
									feedback: feedbackRow.feedback[r.sectionId],
								};
							} else if (currentValidation.resolution !== r.resolution) {
								// If the resolution varies, it is changed to unknown.
								currentValidation.resolution = "unknown";
								currentValidation.explanation = "Multiple resolutions";
							}

							return acc;
						}, acc);
					},
					{} as Record<
						string,
						{
							sectionId: string;
							resolution: "unknown" | "fully-resolved" | "not-resolved";
							explanation: string;
							sectionText: string;
							feedback: string;
						}
					>,
				),
			};
		}),
	);

	return validations;
}

/**
 * Compares the active prompts with the new prompts and returns a list of feedbacks that were broken in the new prompts.
 * @param templateOverrides
 */
export async function compareValidations(templateOverrides: TemplateOverrides) {
	const [activePromptsValidations, newPromptsValidations] = await Promise.all([
		await validateEffReportSummarizations({}),
		await validateEffReportSummarizations(templateOverrides, 100),
	]);

	// Count statistics that are shown on the UI
	const countResolutions = (validations: typeof activePromptsValidations) => {
		return validations.reduce(
			(acc, validation) => {
				Object.values(validation.feedbackResolutions).forEach((resolution) => {
					acc[resolution.resolution] = (acc[resolution.resolution] || 0) + 1;
				});
				return acc;
			},
			{
				"fully-resolved": 0,
				"not-resolved": 0,
				unknown: 0,
			} as Record<"fully-resolved" | "not-resolved" | "unknown", number>,
		);
	};

	const activeResolutionsCount = countResolutions(activePromptsValidations);
	const newResolutionsCount = countResolutions(newPromptsValidations);

	const brokenValidations = newPromptsValidations.flatMap((newValidation) => {
		const oldValidation = activePromptsValidations.find((v) => v.feedbackId === newValidation.feedbackId)!;

		const brokenResolutions = Object.entries(newValidation.feedbackResolutions).flatMap(([sectionId, newResolution]) => {
			if (newResolution.resolution === "not-resolved") {
				const oldResolution = oldValidation.feedbackResolutions[sectionId];

				if (oldResolution.resolution === "fully-resolved") {
					return [
						{
							sectionId,
							sectionText: oldResolution.sectionText,
							feedback: oldResolution.feedback,
							oldExplanation: oldResolution.explanation,
							newExplanation: newResolution.explanation,
						},
					];
				}
			}
			return [];
		});

		if (brokenResolutions.length > 0) {
			return [
				{
					feedbackId: newValidation.feedbackId,
					farmName: newValidation.farmName,
					createdAt: newValidation.createdAt,
					authorName: newValidation.authorName,
					summary: newValidation.summary,
					brokenResolutions,
				},
			];
		} else {
			return [];
		}
	});

	return {
		brokenValidations,
		activeResolutionsCount,
		newResolutionsCount,
	};
}
