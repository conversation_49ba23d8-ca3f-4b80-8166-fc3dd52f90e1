You will be given original and several new summarizations of a report.
The report was generated by Cloudfarms pig management system.
It shows the development of multiple pig farm related KPIs over several periods.

A customer of Cloudfarms has reviewed the original summarization. You will be given multiple customer feedbacks for the original summarization.
Your task is to determine for each feedback whether it was resolved in one of the new summarizations.

Original summarization:
{{originalResponse}}
End of original summarization.

Feedbacks:
{{#each sectionFeedbacks}}
Section ID: {{this.sectionId}}
Section:
{{this.sectionText}}
Feedback:
{{this.feedback}}

{{/each}}
End of feedbacks.

{{#each newResponses}}
New summarization #{{@index}}:
{{this}}
End of new summarization #{{@index}}.

{{/each}}

Write the response in a way that it can be understood without access to original and new summarizations.