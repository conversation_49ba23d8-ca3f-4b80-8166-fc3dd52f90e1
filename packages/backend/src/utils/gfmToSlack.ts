/* eslint-disable no-useless-escape */

// Generated by Claude
export function convertGFMToSlack(gfm: string): string {
	let slack = gfm;

	// Convert headers
	slack = slack.replace(/^(#{1,6})\s+(.+)$/gm, (_, hashes, text) => {
		// Remove any existing bold markers within headers
		text = text.replace(/\*\*/g, "");
		return `*${text}*`;
	});

	// Convert bold (after headers to avoid conflicts)
	slack = slack.replace(/\*\*(.+?)\*\*/g, "*$1*");

	// Convert italic
	slack = slack.replace(/\_(.+?)\_/g, "_$1_");

	// Convert strikethrough
	slack = slack.replace(/~~(.+?)~~/g, "~$1~");

	// Convert inline code
	slack = slack.replace(/`([^`\n]+)`/g, "`$1`");

	// Convert code blocks
	slack = slack.replace(/```([\s\S]+?)```/g, (_, code) => {
		return "```" + code.trim() + "```";
	});

	// Convert links
	slack = slack.replace(/\[([^\]]+)\]\(([^\)]+)\)/g, "<$2|$1>");

	// Convert unordered lists
	slack = slack.replace(/^[\*\-]\s+(.+)$/gm, "• $1");

	// Convert ordered lists
	slack = slack.replace(/^(\d+)\.\s+(.+)$/gm, (_, number, text) => `${number}. ${text}`);

	// Convert blockquotes
	slack = slack.replace(/^>\s+(.+)$/gm, "> $1");

	// Slack weirdly renders apostrophe as&#x27; instead of just '.
	// This replaces it with a similar looking character that Slack renders correctly.
	slack = slack.replace(/(?<!')'(?!')/g, "’");

	return slack;
}
