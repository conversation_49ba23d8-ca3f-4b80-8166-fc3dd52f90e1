import { expect, jest, test } from "@jest/globals";
import "pigbot-core/test/jest.setup";
import { EFFICIENCY_REPORT_TYPE } from "pigbot-core/src/Constants";
import { TestData } from "pigbot-core/test/TestData";
import { DefaultTestLength, mockTemporal, parseResponseStream } from "pigbot-core/test/jest.setup";

test(
	"saveFeedback",
	async () => {
		mockTemporal();

		// Modules must be loaded in the test otherwise they would initialize with incorrect process.env
		const { getEfficiencyReportSummary } = await import("pigbot-core/src/eff-report/EfficiencyReportService");
		const { sql } = await import("pigbot-core/src/database");

		const FeedbackService = await import("./FeedbackService");
		const { saveFeedback } = FeedbackService;

		// Don't call this as it keeps running in the background
		jest.spyOn(FeedbackService, "summarizeFeedback").mockImplementation(async () => {});

		const response = getEfficiencyReportSummary(TestData.request, TestData.testUserContext);
		const { responseId } = await parseResponseStream(response);

		await saveFeedback({ responseId: responseId, sectionId: "1", feedback: "1 A" }, TestData.testUserContext);
		expect(FeedbackService.summarizeFeedback).toHaveBeenCalled();

		await saveFeedback({ responseId: responseId, sectionId: "2", feedback: "2 A" }, TestData.testUserContext);
		expect(FeedbackService.summarizeFeedback).toHaveBeenCalled();

		expect(
			(
				await sql<never[]>`SELECT *
													 FROM response_feedback
													 WHERE id = ${responseId}`
			)[0],
		).toMatchObject({
			id: responseId,
			farm_id: TestData.testUserContext.farmId,
			root_id: TestData.testUserContext.rootId,
			response: {
				version: "vfa-v3",
				internalResponseStreamArray: expect.any(Array),
				reportData: TestData.request.report,
			},
			type: EFFICIENCY_REPORT_TYPE,
			feedback: {
				"1": "1 A",
				"2": "2 A",
			},
			summary: expect.any(String),
			author_id: TestData.testUserContext.userId,
			author_name: TestData.testUserContext.name,
		});

		await saveFeedback({ responseId: responseId, sectionId: "1", feedback: "1 B" }, TestData.testUserContext);

		expect(
			(
				await sql<never[]>`SELECT feedback
													 FROM response_feedback
													 WHERE id = ${responseId}`
			)[0],
		).toMatchObject({
			feedback: {
				"1": "1 B",
				"2": "2 A",
			},
		});
	},
	DefaultTestLength,
);

test(
	"deleteFeedback",
	async () => {
		mockTemporal();
		const { getEfficiencyReportSummary } = await import("pigbot-core/src/eff-report/EfficiencyReportService");
		const { sql } = await import("pigbot-core/src/database");

		const FeedbackService = await import("./FeedbackService");
		const { saveFeedback, deleteFeedback } = FeedbackService;

		// Don't call this as it keeps running in the background
		jest.spyOn(FeedbackService, "summarizeFeedback").mockImplementation(async () => {});

		const response = getEfficiencyReportSummary(TestData.request, TestData.testUserContext);

		const { responseId } = await parseResponseStream(response);

		await saveFeedback({ responseId: responseId, sectionId: "1", feedback: "1 A" }, TestData.testUserContext);
		await saveFeedback({ responseId: responseId, sectionId: "2", feedback: "2 A" }, TestData.testUserContext);

		await saveFeedback({ responseId: responseId, sectionId: "1", feedback: "1 B" }, TestData.testUserContext);

		await saveFeedback({ responseId: responseId, sectionId: "3", feedback: "3 A" }, TestData.testUserContext);

		await deleteFeedback({ responseId: responseId, sectionId: "2" }, TestData.testUserContext);

		function getFeedback() {
			return sql<never[]>`SELECT feedback
													FROM response_feedback
													WHERE id = ${responseId}`;
		}

		expect((await getFeedback())[0]).toMatchObject({
			feedback: {
				"1": "1 B",
				"3": "3 A",
			},
		});

		await deleteFeedback({ responseId: responseId, sectionId: "1" }, TestData.testUserContext);

		expect((await getFeedback())[0]).toMatchObject({
			feedback: {
				"3": "3 A",
			},
		});

		await deleteFeedback({ responseId: responseId, sectionId: "3" }, TestData.testUserContext);

		expect(await getFeedback()).toHaveLength(0);
	},
	DefaultTestLength,
);

test(
	"summarizeFeedback",
	async () => {
		mockTemporal();

		const { getEfficiencyReportSummary } = await import("pigbot-core/src/eff-report/EfficiencyReportService");
		const FeedbackService = await import("./FeedbackService");
		const { saveFeedback, summarizeFeedback } = FeedbackService;
		const { sql } = await import("pigbot-core/src/database");

		const response = getEfficiencyReportSummary(TestData.request, TestData.testUserContext);

		const { responseId } = await parseResponseStream(response);

		// Don't call this as it keeps running in the background
		jest.spyOn(FeedbackService, "summarizeFeedback").mockImplementationOnce(async () => {});

		await saveFeedback({ responseId: responseId, sectionId: "1", feedback: "1 A" }, TestData.testUserContext);

		await summarizeFeedback(
			responseId,
			{
				"1": "Lorem ipsum",
			},
			TestData.testUserContext,
		);

		const [{ summary }] = await sql<{ summary: string }[]>`SELECT summary
																													 FROM response_feedback
																													 WHERE id = ${responseId}`;

		expect(summary).not.toBe("1 A");
	},
	DefaultTestLength,
);
