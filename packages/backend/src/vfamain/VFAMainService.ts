import { sql } from "pigbot-core/src/database";
import { UserContext } from "pigbot-core/src/UserContext";
import { formatDateToISODate, getDatePeriods, Period } from "pigbot-core/src/vfamain/Periods";
import { PIGBOT_WORKER_QUEUE, startOrAwaitWorkflow } from "pigbot-core/src/temporal/temporal";
import { GetFilterParams } from "pigbot-core/src/cf-link/GetFilterParams";
import { GetReport } from "pigbot-core/src/cf-link/GetReport";
import { GetEfficiencyReportSummary, getEfficiencyReportSummary } from "pigbot-core/src/eff-report/EfficiencyReportService";
import { Period as PeriodLink } from "pigbot-core/src/cf-link/CfLinkActivities";
import { cachePromise, hash } from "pigbot-core/src/Cache";
import { EfficiencyReportData } from "pigbot-core/src/EfficiencyReportData";

export type GetLatestEfficiencyReportSummaryRequest = {
	locationId: number | undefined;
	idx: number;
	period: Period;
};

export async function* getLatestAnalysis({ period, locationId, idx }: GetLatestEfficiencyReportSummaryRequest, userContext: UserContext) {
	const effReportParams = {
		userContext: userContext,
		periods: getDatePeriods(period),
		locationId: locationId,
	};

	const cache = await cachePromise;
	const cachedReport = await cache.get<EfficiencyReportData>(`latest-eff-report-${hash(effReportParams)}`);

	let latestEfficiencyReport;
	if (cachedReport) {
		latestEfficiencyReport = cachedReport;
	} else {
		latestEfficiencyReport = await getLatestEfficiencyReport(effReportParams);
		await cache.set(`latest-eff-report-${hash(effReportParams)}`, latestEfficiencyReport, 24 * 60 * 60 * 1000); // Cache for 1 day
	}

	const request = {
		report: latestEfficiencyReport,
		idx,
	} as GetEfficiencyReportSummary;

	yield* getEfficiencyReportSummary(request, userContext);
}

export async function getLatestEfficiencyReport({
	userContext,
	periods,
	locationId,
}: {
	userContext: UserContext;
	periods: PeriodLink[];
	locationId: number | undefined;
}) {
	return await startOrAwaitWorkflow(
		PIGBOT_WORKER_QUEUE,
		`getReport-${userContext.farmId}-${userContext.env}`,
		GetReport,
		[userContext, periods, userContext.countryCode, locationId],
		"10m",
	);
}

type SourceDoc = {
	file_data: Uint8Array;
	file_name: string;
};

export async function getPdfData(documentName: string) {
	const [row] = await sql<SourceDoc[]>`
		SELECT file_data, file_name FROM source_doc WHERE file_name = ${documentName}
	`;
	if (!row) {
		throw new Error(`PDF document '${documentName}' not found`);
	}

	// Convert the Uint8Array to Base64 for easier transmission
	const base64Data = Buffer.from(row.file_data).toString("base64");

	return base64Data;
}

export type SowFarmLocation = {
	id: number;
	name: string;
};

export type SowFarmLocations = SowFarmLocation[] | null;

export async function getSowFarmLocationsAndPeriodInfo(userContext: UserContext): Promise<SowFarmLocationsAndPeriodInfo> {
	// The locations in Cloudfarms have from and to dates.
	// The function GetFilterParams also checks for the location validation.
	// The current condition is that the location must be valid in the time of report running.
	const period = { from: formatDateToISODate(new Date()), to: formatDateToISODate(new Date()) };
	const { locations, specificServingGroupLength } = await startOrAwaitWorkflow(
		PIGBOT_WORKER_QUEUE,
		`getFilterParams-${userContext.farmId}-${userContext.env}`,
		GetFilterParams,
		[userContext, period, "en"],
		"10m",
	);

	if (locations.length <= 1) {
		return { sowFarmLocations: null, periodLength: specificServingGroupLength };
	} else {
		return { sowFarmLocations: locations, periodLength: specificServingGroupLength };
	}
}

export type SowFarmLocationsAndPeriodInfo = {
	sowFarmLocations: SowFarmLocations;
	periodLength: number | null;
};
