import jwt from "jsonwebtoken";
import { NextFunction, Request, Response } from "express";
import { UserContext, userContextSchema } from "pigbot-core/src/UserContext";
import Config from "pigbot-core/src/Config";

export interface AuthenticatedRequest extends Request {
	userContext?: UserContext;
}

const authenticateJWT = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
	const authHeader = req.headers.authorization;
	const JWT_SECRET = Config.JWT_SECRET;

	if (authHeader) {
		const token = authHeader.split(" ")[1];

		jwt.verify(token, JWT_SECRET, (err: unknown, payload: unknown) => {
			if (err) {
				return res.sendStatus(403);
			}

			req.userContext = userContextSchema.parse(payload);

			next();
		});
	} else {
		res.sendStatus(401);
	}
};

export default authenticateJWT;
