import { efficiencyReportData } from "./EfficiencyReportData";
import type { UserContext } from "../src/UserContext";

// We want to scope this in TestData namespace to avoid conflicts
// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace TestData {
	export const request = {
		idx: 0,
		report: efficiencyReportData,
	};

	export const testUserContext: UserContext = {
		name: "name",
		// Ids come from the pigman test database. Useful for integration testing.
		userId: 1,
		rootId: 4,
		farmId: 3,
		farmName: "farm",
		role: "backoffice",
		countryCode: "US",
		rootName: "root",
		env: "test",
	};
}
