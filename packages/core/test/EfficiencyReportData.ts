import { EfficiencyReportData } from "../src/EfficiencyReportData";

export const efficiencyReportData: EfficiencyReportData = {
	periods: [
		{ to: "2024-12-31", from: "2024-12-01" },
		{ to: "2024-11-30", from: "2024-11-01" },
		{
			to: "2024-10-31",
			from: "2024-10-01",
		},
		{ to: "2024-09-30", from: "2024-09-01" },
		{ to: "2024-12-31", from: "2024-09-01" },
	],
	language: "en",
	sections: [
		{
			kpis: [
				{
					code: "SOW_PARITY_AVG_NUMBER",
					label: "Average parity number [№]",
					periodValues: [
						{ value: 1.6, goalOpt: null },
						{ value: 1.5, goalOpt: null },
						{
							value: 1.4,
							goalOpt: null,
						},
						{ value: 1.3, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SERVICE_EVENTS_TOTAL",
					label: "Service events [№]",
					periodValues: [
						{ value: 1960, goalOpt: null },
						{ value: 1725, goalOpt: null },
						{
							value: 1813,
							goalOpt: null,
						},
						{ value: 1813, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "MAIDENGILT_FINAL_AMOUNT",
					label: "Young breeding animals [№]",
					periodValues: [
						{ value: 205, goalOpt: null },
						{ value: 197, goalOpt: null },
						{
							value: 151,
							goalOpt: null,
						},
						{ value: 224, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "PAR0_FINAL_AMOUNT",
					label: "Gilts at period end [№]",
					periodValues: [
						{ value: 786, goalOpt: null },
						{ value: 857, goalOpt: null },
						{
							value: 919,
							goalOpt: null,
						},
						{ value: 858, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_FINAL_AMOUNT",
					label: "Sows at period end [№]",
					periodValues: [
						{ value: 3180, goalOpt: null },
						{ value: 3110, goalOpt: null },
						{
							value: 3127,
							goalOpt: null,
						},
						{ value: 3155, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "BOAR_FINAL_AMOUNT",
					label: "Boars at period end [№]",
					periodValues: [
						{ value: 14, goalOpt: null },
						{ value: 14, goalOpt: null },
						{ value: 14, goalOpt: null },
						{
							value: 8,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
			],
			label: "Status at the end of the period",
		},
		{
			kpis: [
				{
					code: "AVERAGE_SOW_AMOUNT",
					label: "Avg. number of active sows [№]",
					periodValues: [
						{ value: 3936.87, goalOpt: null },
						{ value: 4013.83, goalOpt: null },
						{
							value: 4040.32,
							goalOpt: null,
						},
						{ value: 4001.57, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_PIGLETS_SOW_YEAR_US",
					label: "Weaned piglets / sow / year (based on avg. sows)",
					periodValues: [
						{ value: 33.5, goalOpt: null },
						{ value: 34.1, goalOpt: null },
						{
							value: 32.6,
							goalOpt: null,
						},
						{ value: 33.7, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "LIVEBORNSOWYEAR_US",
					label: "Liveborn/sow/year (based on avg. sows)",
					periodValues: [
						{ value: 38.11, goalOpt: null },
						{ value: 37.84, goalOpt: null },
						{
							value: 37.86,
							goalOpt: null,
						},
						{ value: 36.74, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "LITTER_SOW_YEAR",
					label: "Litter / sow / year [№]",
					periodValues: [
						{ value: 2.45, goalOpt: null },
						{ value: 2.46, goalOpt: null },
						{
							value: 2.4,
							goalOpt: null,
						},
						{ value: 2.34, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
			],
			label: "Sow statistics (per year)",
		},
		{
			kpis: [
				{
					code: "CALCULATED_PREWEANING_MORTALITY",
					label: "Pre-wean mortality [%]",
					periodValues: [
						{ value: 11.5, goalOpt: 12 },
						{ value: 10.4, goalOpt: 12 },
						{
							value: 11.5,
							goalOpt: 12,
						},
						{ value: 11.7, goalOpt: 12 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "AVG_AGE_OF_DEAD_PIGLETS_US",
					label: "Avg. age of dead piglets",
					periodValues: [
						{ value: 4.8, goalOpt: null },
						{ value: 4.8, goalOpt: null },
						{
							value: 4.6,
							goalOpt: null,
						},
						{ value: 5.2, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "PIGLET_DEAD_RATIO",
					label: "Dead piglets before weaning [%]",
					periodValues: [
						{ value: 11.58, goalOpt: null },
						{ value: 10.3, goalOpt: null },
						{
							value: 10.89,
							goalOpt: null,
						},
						{ value: 10.93, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_AMOUNT",
					label: "Dead piglets before weaning [№]",
					periodValues: [
						{ value: 1474, goalOpt: null },
						{ value: 1285, goalOpt: null },
						{
							value: 1414,
							goalOpt: null,
						},
						{ value: 1320, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "NURSERY_SOWS",
					label: "Nursery sows [%]",
					periodValues: [
						{ value: 2.73, goalOpt: null },
						{ value: 3.77, goalOpt: null },
						{
							value: 4.62,
							goalOpt: null,
						},
						{ value: 4.66, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_NURSE_SOWS",
					label: "Nurse sows [№]",
					periodValues: [
						{ value: 24, goalOpt: null },
						{ value: 41, goalOpt: null },
						{ value: 38, goalOpt: null },
						{
							value: 32,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "LACTATION_DAYS",
					label: "Lactation period [days]",
					periodValues: [
						{ value: 20.8, goalOpt: 21 },
						{ value: 20.8, goalOpt: 21 },
						{
							value: 21.1,
							goalOpt: 21,
						},
						{ value: 20.9, goalOpt: 21 },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_PIGLETS_PER_LITTER",
					label: "Weaned piglets per litter [№]",
					periodValues: [
						{ value: 13.65, goalOpt: 13.2 },
						{ value: 13.77, goalOpt: 13.2 },
						{
							value: 13.86,
							goalOpt: 13.2,
						},
						{ value: 13.88, goalOpt: 13.2 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "WEANED_PIGLETS_PER_WEANING",
					label: "Weaned piglets per weaning [№]",
					periodValues: [
						{ value: 13.3, goalOpt: null },
						{ value: 13.3, goalOpt: null },
						{
							value: 13.2,
							goalOpt: null,
						},
						{ value: 13.2, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "PARTIAL_WEANED_PIGLETS",
					label: "Partial weaned pigs [№]",
					periodValues: [
						{ value: 313, goalOpt: null },
						{ value: 458, goalOpt: null },
						{
							value: 549,
							goalOpt: null,
						},
						{ value: 552, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_PIGLETS_PER_WEEK",
					label: "Weaned piglets / week [№]",
					periodValues: [
						{ value: 2527, goalOpt: 2113 },
						{ value: 2622, goalOpt: 2113 },
						{
							value: 2522,
							goalOpt: 2113,
						},
						{ value: 2585, goalOpt: 2113 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "WEANED_PIGLETS",
					label: "Weaned piglets",
					periodValues: [
						{ value: 11192, goalOpt: null },
						{ value: 11237, goalOpt: null },
						{
							value: 11171,
							goalOpt: null,
						},
						{ value: 11078, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "PAR1_MUMMIFICATED_BORNS_PER_LITTER",
					label: "Parity 1 mummificated borns/litter [№]",
					periodValues: [
						{ value: 0.3, goalOpt: null },
						{ value: 0.4, goalOpt: null },
						{
							value: 0.4,
							goalOpt: null,
						},
						{ value: 0.6, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "MUMMIFIED_BORNS_PER_LITTER",
					label: "Mummified borns per litter [№]",
					periodValues: [
						{ value: 0.36, goalOpt: 0.2 },
						{ value: 0.36, goalOpt: 0.2 },
						{
							value: 0.43,
							goalOpt: 0.2,
						},
						{ value: 0.43, goalOpt: 0.2 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "DEAD_BORNS_RATIO",
					label: "Stillborns [%]",
					periodValues: [
						{ value: 1.9, goalOpt: null },
						{ value: 2.1, goalOpt: null },
						{
							value: 2.3,
							goalOpt: null,
						},
						{ value: 3, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR1_DEAD_BORNS_PER_LITTER",
					label: "Parity 1 stillborns/litter [№]",
					periodValues: [
						{ value: 0.4, goalOpt: null },
						{ value: 0.3, goalOpt: null },
						{
							value: 0.4,
							goalOpt: null,
						},
						{ value: 0.6, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "DEAD_BORNS_PER_LITTER",
					label: "Stillborn per litter [№]",
					periodValues: [
						{ value: 0.31, goalOpt: 0.8 },
						{ value: 0.34, goalOpt: 0.8 },
						{
							value: 0.38,
							goalOpt: 0.8,
						},
						{ value: 0.51, goalOpt: 0.8 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR1_LIVE_BORNS_PER_LITTER",
					label: "Parity 1 liveborns/litter [№]",
					periodValues: [
						{ value: 15.4, goalOpt: 15 },
						{ value: 15.7, goalOpt: 15 },
						{
							value: 15.6,
							goalOpt: 15,
						},
						{ value: 15.7, goalOpt: 15 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "LIVE_BORNS_PER_LITTER",
					label: "Liveborn per litter [№]",
					periodValues: [
						{ value: 15.53, goalOpt: 15 },
						{ value: 15.36, goalOpt: 15 },
						{
							value: 15.74,
							goalOpt: 15,
						},
						{ value: 15.7, goalOpt: 15 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "PAR1_TOTAL_BORN_PER_LITTER",
					label: "Parity 1 total born / litter [№]",
					periodValues: [
						{ value: 16.08, goalOpt: 16 },
						{ value: 16.38, goalOpt: 16 },
						{
							value: 16.4,
							goalOpt: 16,
						},
						{ value: 16.86, goalOpt: 16 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "TOTAL_BORN_PER_LITTER",
					label: "Total born / litter [№]",
					periodValues: [
						{ value: 16.2, goalOpt: 16 },
						{ value: 16.06, goalOpt: 16 },
						{
							value: 16.54,
							goalOpt: 16,
						},
						{ value: 16.64, goalOpt: 16 },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_SOWS_LIFE_BORNS_PER_LITTER",
					label: "Weaned sows liveborn / litter [№]",
					periodValues: [
						{ value: 15.5, goalOpt: null },
						{ value: 15.5, goalOpt: null },
						{
							value: 15.6,
							goalOpt: null,
						},
						{ value: 15.6, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_SOWS",
					label: "Litters weaned [№]",
					periodValues: [
						{ value: 820, goalOpt: null },
						{ value: 816, goalOpt: null },
						{
							value: 806,
							goalOpt: null,
						},
						{ value: 798, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANINGS",
					label: "Weanings [№]",
					periodValues: [
						{ value: 843, goalOpt: null },
						{ value: 848, goalOpt: null },
						{
							value: 845,
							goalOpt: null,
						},
						{ value: 837, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "GESTATING_DAYS",
					label: "Pregnancy duration [days]",
					periodValues: [
						{ value: 116, goalOpt: null },
						{ value: 115, goalOpt: null },
						{
							value: 115,
							goalOpt: null,
						},
						{ value: 115, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR1_FARROWINGS",
					label: "Parity 1 farrowings [№]",
					periodValues: [
						{ value: 227, goalOpt: null },
						{ value: 197, goalOpt: null },
						{
							value: 186,
							goalOpt: null,
						},
						{ value: 170, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "FARROWINGS",
					label: "Farrowings [№]",
					periodValues: [
						{ value: 820, goalOpt: null },
						{ value: 812, goalOpt: null },
						{
							value: 825,
							goalOpt: null,
						},
						{ value: 769, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
			],
			label: "Litter results",
		},
		{
			kpis: [
				{
					code: "REPLACEMENT_RATE_US_INCL_BOUGHT_GILTS",
					label: "Replacement rate incl. bought gilts",
					periodValues: [
						{ value: 46.73, goalOpt: 60 },
						{ value: 41.17, goalOpt: 60 },
						{
							value: 70.15,
							goalOpt: 60,
						},
						{ value: 75.81, goalOpt: 60 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "REPLACEMENT_RATE_US",
					label: "Replacement rate",
					periodValues: [
						{ value: 46.73, goalOpt: 60 },
						{ value: 41.17, goalOpt: 60 },
						{
							value: 70.15,
							goalOpt: 60,
						},
						{ value: 75.81, goalOpt: 60 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR0SOW_REMOVE_RATE",
					label: "Sows remove rate [%/year]",
					periodValues: [
						{ value: 50.2, goalOpt: 55 },
						{ value: 67.3, goalOpt: 55 },
						{ value: 64.1, goalOpt: 55 },
						{
							value: 76,
							goalOpt: 55,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "FEMALES_SOLDYEAR",
					label: "Females sold/year [%]",
					periodValues: [
						{ value: 33.58, goalOpt: null },
						{ value: 42.9, goalOpt: null },
						{
							value: 46.58,
							goalOpt: null,
						},
						{ value: 53.99, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR0SOW_CULLING_RATE",
					label: "Sows culling rate [%/year]",
					periodValues: [
						{ value: 30.51, goalOpt: 40 },
						{ value: 45.16, goalOpt: 40 },
						{
							value: 48.96,
							goalOpt: 40,
						},
						{ value: 57.46, goalOpt: 40 },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "FEMALES_DEADYEAR",
					label: "Females dead/year [%]",
					periodValues: [
						{ value: 19.31, goalOpt: 15.2 },
						{ value: 23.61, goalOpt: 15.2 },
						{
							value: 14.7,
							goalOpt: 15.2,
						},
						{ value: 18.46, goalOpt: 15.2 },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PAR0SOW_DEAD_RATE",
					label: "Sows mortality rate [%/year]",
					periodValues: [
						{ value: 19.74, goalOpt: 15 },
						{ value: 22.13, goalOpt: 15 },
						{
							value: 15.15,
							goalOpt: 15,
						},
						{ value: 18.55, goalOpt: 15 },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOWS_DEAD_SOLD_RATIO",
					label: "Dead sows ratio [%]",
					periodValues: [
						{ value: 39.3, goalOpt: null },
						{ value: 32.9, goalOpt: null },
						{
							value: 23.6,
							goalOpt: null,
						},
						{ value: 24.4, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "SOWS_DEAD_AMOUNT",
					label: "Dead sows and gilts [№]",
					periodValues: [
						{ value: 66, goalOpt: null },
						{ value: 73, goalOpt: null },
						{ value: 52, goalOpt: null },
						{
							value: 61,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: false,
				},
				{
					code: "SOWS_SOLD_AMOUNT",
					label: "Sold sows and gilts [№]",
					periodValues: [
						{ value: 102, goalOpt: null },
						{ value: 149, goalOpt: null },
						{
							value: 168,
							goalOpt: null,
						},
						{ value: 189, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "CONCEPTION_RATE_US",
					label: "Conception rate",
					periodValues: [
						{ value: 96.6, goalOpt: 95 },
						{ value: 92.5, goalOpt: 95 },
						{
							value: 92.9,
							goalOpt: 95,
						},
						{ value: 92.2, goalOpt: 95 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "FARROWING_RATE",
					label: "Farrowing rate [%]",
					periodValues: [
						{ value: 92.27, goalOpt: 92 },
						{ value: 92.02, goalOpt: 92 },
						{
							value: 93.3,
							goalOpt: 92,
						},
						{ value: 90.83, goalOpt: 92 },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "REPEAT_SERVINGS_RATIO",
					label: "Returner rate [%]",
					periodValues: [
						{ value: 3.33, goalOpt: 5 },
						{ value: 4.8, goalOpt: 5 },
						{ value: 3.88, goalOpt: 5 },
						{
							value: 2.92,
							goalOpt: 5,
						},
					],
					isMoreBetterOpt: false,
				},
				{
					code: "WEANING2SERVING_DAYS",
					label: "Days from weaning to 1st service",
					periodValues: [
						{ value: 4.9, goalOpt: 5.5 },
						{ value: 6.1, goalOpt: 5.5 },
						{ value: 6.8, goalOpt: 5.5 },
						{
							value: 7.7,
							goalOpt: 5.5,
						},
					],
					isMoreBetterOpt: false,
				},
				{
					code: "NPD_PER_LITTER",
					label: "Non-productive days per litter",
					periodValues: [
						{ value: 12.1, goalOpt: null },
						{ value: 12.3, goalOpt: null },
						{
							value: 12.2,
							goalOpt: null,
						},
						{ value: 14.9, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "SOWS_FAILED_CYCLES_ABORTION_PCT",
					label: "Abortions [%]",
					periodValues: [
						{ value: 0.08, goalOpt: null },
						{ value: 0.07, goalOpt: null },
						{
							value: 0.02,
							goalOpt: null,
						},
						{ value: 0.12, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "WEANED_SOWS_PER_WEEK",
					label: "Weaned sows / week [№]",
					periodValues: [
						{ value: 185, goalOpt: null },
						{ value: 190, goalOpt: null },
						{
							value: 182,
							goalOpt: null,
						},
						{ value: 186, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "FARROWINGS_PER_WEEK",
					label: "Farrowings / week [№]",
					periodValues: [
						{ value: 185, goalOpt: null },
						{ value: 189, goalOpt: null },
						{
							value: 186,
							goalOpt: null,
						},
						{ value: 179, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "SOWS_BRED_WITHIN_7_DAYS_US",
					label: "% sows bred within 7 days",
					periodValues: [
						{ value: 95.4, goalOpt: null },
						{ value: 89.3, goalOpt: null },
						{
							value: 89.1,
							goalOpt: null,
						},
						{ value: 83, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "SERVINGS_PER_WEEK",
					label: "Services / week [№]",
					periodValues: [
						{ value: 217, goalOpt: null },
						{ value: 199, goalOpt: null },
						{
							value: 204,
							goalOpt: null,
						},
						{ value: 208, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "REPEAT_SERVINGS",
					label: "Repeat services [№]",
					periodValues: [
						{ value: 32, goalOpt: null },
						{ value: 41, goalOpt: null },
						{ value: 35, goalOpt: null },
						{
							value: 26,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_SERVINGS",
					label: "Sows served [№]",
					periodValues: [
						{ value: 763, goalOpt: null },
						{ value: 670, goalOpt: null },
						{
							value: 614,
							goalOpt: null,
						},
						{ value: 593, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "GILTS_OF_ALL_FIRST_SERVICES_US",
					label: "% gilts of all first services",
					periodValues: [
						{ value: 18, goalOpt: null },
						{ value: 17.6, goalOpt: null },
						{
							value: 29.2,
							goalOpt: null,
						},
						{ value: 31.4, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "GILT_SERVINGS",
					label: "Gilts served [№]",
					periodValues: [
						{ value: 167, goalOpt: null },
						{ value: 143, goalOpt: null },
						{
							value: 253,
							goalOpt: null,
						},
						{ value: 271, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
				{
					code: "MATINGS_PER_SERVICE_US",
					label: "Matings per service",
					periodValues: [
						{ value: 2.04, goalOpt: 2.2 },
						{ value: 2.02, goalOpt: 2.2 },
						{
							value: 2.01,
							goalOpt: 2.2,
						},
						{ value: 2.04, goalOpt: 2.2 },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SERVINGS",
					label: "Services [№]",
					periodValues: [
						{ value: 962, goalOpt: null },
						{ value: 854, goalOpt: null },
						{
							value: 902,
							goalOpt: null,
						},
						{ value: 890, goalOpt: null },
					],
					isMoreBetterOpt: true,
				},
			],
			label: "Reproduction",
		},
		{
			kpis: [
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Starve-out/Wasting",
					periodValues: [
						{ value: 0.06, goalOpt: null },
						{ value: 0.06, goalOpt: null },
						{
							value: 0.14,
							goalOpt: null,
						},
						{ value: 0, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Small Non-Viable",
					periodValues: [
						{ value: 3.46, goalOpt: null },
						{ value: 3.01, goalOpt: null },
						{
							value: 4.15,
							goalOpt: null,
						},
						{ value: 4.12, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Scours",
					periodValues: [
						{ value: 0.36, goalOpt: null },
						{ value: 0.18, goalOpt: null },
						{
							value: 0.07,
							goalOpt: null,
						},
						{ value: 0.17, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Rupture",
					periodValues: [
						{ value: 1.08, goalOpt: null },
						{ value: 0.58, goalOpt: null },
						{
							value: 0.65,
							goalOpt: null,
						},
						{ value: 0.61, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Laid On",
					periodValues: [
						{ value: 5.38, goalOpt: null },
						{ value: 5.57, goalOpt: null },
						{
							value: 4.38,
							goalOpt: null,
						},
						{ value: 4.65, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Injured/Deformed",
					periodValues: [
						{ value: 1.19, goalOpt: null },
						{ value: 0.88, goalOpt: null },
						{
							value: 1.47,
							goalOpt: null,
						},
						{ value: 1.36, goalOpt: null },
					],
					isMoreBetterOpt: false,
				},
				{
					code: "PIGLET_DEAD_RATIO<DeathReason>",
					label: "Dead piglets before weaning [%] per death reason - Health",
					periodValues: [
						{ value: 0.04, goalOpt: null },
						{ value: 0, goalOpt: null },
						{ value: 0, goalOpt: null },
						{
							value: 0,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: false,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Sudden Death",
					periodValues: [
						{ value: 8.2, goalOpt: null },
						{ value: 20.9, goalOpt: null },
						{
							value: 18.37,
							goalOpt: null,
						},
						{ value: 7.55, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Prolapse",
					periodValues: [
						{ value: 55.74, goalOpt: null },
						{ value: 55.22, goalOpt: null },
						{
							value: 51.02,
							goalOpt: null,
						},
						{ value: 49.06, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Not On Farm",
					periodValues: [
						{ value: 4.92, goalOpt: null },
						{ value: 0, goalOpt: null },
						{ value: 0, goalOpt: null },
						{
							value: 0,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Injured/Lame",
					periodValues: [
						{ value: 22.95, goalOpt: null },
						{ value: 11.94, goalOpt: null },
						{
							value: 16.33,
							goalOpt: null,
						},
						{ value: 32.08, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Health",
					periodValues: [
						{ value: 1.64, goalOpt: null },
						{ value: 2.99, goalOpt: null },
						{
							value: 2.04,
							goalOpt: null,
						},
						{ value: 1.89, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Farrowing Complication/Abortion",
					periodValues: [
						{ value: 3.28, goalOpt: null },
						{ value: 4.48, goalOpt: null },
						{
							value: 4.08,
							goalOpt: null,
						},
						{ value: 3.77, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT_PCT<DeathReason>",
					label: "Dead sows [%] per death reason - Body Condition",
					periodValues: [
						{ value: 3.28, goalOpt: null },
						{ value: 4.48, goalOpt: null },
						{
							value: 2.04,
							goalOpt: null,
						},
						{ value: 5.66, goalOpt: null },
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Sudden Death",
					periodValues: [
						{ value: 5, goalOpt: null },
						{ value: 14, goalOpt: null },
						{ value: 9, goalOpt: null },
						{
							value: 4,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Prolapse",
					periodValues: [
						{ value: 34, goalOpt: null },
						{ value: 37, goalOpt: null },
						{ value: 25, goalOpt: null },
						{
							value: 26,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Not On Farm",
					periodValues: [
						{ value: 3, goalOpt: null },
						{ value: 0, goalOpt: null },
						{ value: 0, goalOpt: null },
						{
							value: 0,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Injured/Lame",
					periodValues: [
						{ value: 14, goalOpt: null },
						{ value: 8, goalOpt: null },
						{ value: 8, goalOpt: null },
						{
							value: 17,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Health",
					periodValues: [
						{ value: 1, goalOpt: null },
						{ value: 2, goalOpt: null },
						{ value: 1, goalOpt: null },
						{
							value: 1,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Farrowing Complication/Abortion",
					periodValues: [
						{ value: 2, goalOpt: null },
						{ value: 3, goalOpt: null },
						{ value: 2, goalOpt: null },
						{
							value: 2,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
				{
					code: "SOW_DEAD_AMOUNT<DeathReason>",
					label: "Dead sows [№] per death reason - Body Condition",
					periodValues: [
						{ value: 2, goalOpt: null },
						{ value: 3, goalOpt: null },
						{ value: 1, goalOpt: null },
						{
							value: 3,
							goalOpt: null,
						},
					],
					isMoreBetterOpt: null,
				},
			],
			label: "Indicators per death reason",
		},
	],
	monthNames: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
	reportName: "Breeding animals",
	reportContext: { breeds: null, useCommonWeight: false, reportDate: "2021-06-01" },
};
