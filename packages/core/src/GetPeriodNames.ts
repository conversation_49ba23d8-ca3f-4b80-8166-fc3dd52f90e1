import { EfficiencyReportData } from "./EfficiencyReportData";
import moment from "moment";

export type PeriodNaming = {
	graphItemLabel: string;
	graphDescLabel: string | undefined;
	textName: string;
};

function getMonth(data: EfficiencyReportData, from: string, to: string): string | null {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	const isFullMonth = (): boolean => {
		const startOfMonth = fromMoment.clone().startOf('month');
		const endOfMonth = fromMoment.clone().endOf('month');
		return fromMoment.isSame(startOfMonth, 'day') && toMoment.isSame(endOfMonth, 'day');
	};

	if (fromMoment.isSame(toMoment, 'month') && isFullMonth()) {
		return `${data.monthNames[fromMoment.month()]} ${fromMoment.year()}`;
	}

	return null;
}

export type PeriodNames = {
	periodNames: { [p: number]: string };
	periodsMatchMonths: boolean;
};

export function getPeriodNames(data: EfficiencyReportData): PeriodNames {
	// The last period is summary period, so we ignore it
	return data.periods.slice(0, -1).reduce(
		(acc, period, idx) => {
			const month = getMonth(data, period.from, period.to);

			acc.periodNames[idx] = month ? month : `Period ${idx + 1}`;
			acc.periodsMatchMonths = month != null && acc.periodsMatchMonths;

			return acc;
		},
		{
			periodNames: {} as { [key: number]: string },
			periodsMatchMonths: true as boolean,
		},
	);
}

/**
 * Get month names for a given language code using moment.js
 */
function getMonthNames(langCode: string, format: "long" | "short" = "long"): string[] {
	// Set moment locale
	const originalLocale = moment.locale();
	moment.locale(langCode);

	const monthList = [...Array(12).keys()]; // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]

	const getMonthName = (monthIndex: number) => {
		const monthMoment = moment().month(monthIndex);
		return format === "short" ? monthMoment.format("MMM") : monthMoment.format("MMMM");
	};

	const result = monthList.map(getMonthName);

	// Restore original locale
	moment.locale(originalLocale);

	return result;
}

/**
 * Format a date using the given language code and custom format (short format for graphItemLabel)
 */
function formatDateByLanguage(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		if (dateFormat === "t") {
			// Use moment to format dates
			const localizedMoment = dateMoment.clone().locale(langCode);
			const format = showYear ? "MMM D, YYYY" : "MMM D";
			return localizedMoment.format(format);
		} else {
			// Use Intl.DateTimeFormat for short format
			const options: Intl.DateTimeFormatOptions = {
				month: 'short',
				day: 'numeric'
			};

			if (showYear) {
				options.year = 'numeric';
			}

			const formatter = new Intl.DateTimeFormat(langCode, options);
			return formatter.format(dateMoment.toDate());
		}
	} catch (error) {
		return dateStr;
	}
}

/**
 * Format a date using the given language code and custom format (long format for textName)
 */
function formatDateByLanguageLong(dateStr: string, langCode: string, dateFormat: string, showYear: boolean = true): string {
	try {
		const dateMoment = moment(dateStr);

		if (!dateMoment.isValid()) {
			return dateStr;
		}

		// Clone the moment and set locale
		const localizedMoment = dateMoment.clone().locale(langCode);

		// Use custom format if provided, otherwise fall back to default long format
		let format = dateFormat;
		if (!format) {
			format = showYear ? "MMMM D, YYYY" : "MMMM D";
		} else if (!showYear) {
			// Remove year from custom format if showYear is false
			format = format.replace(/,?\s*Y+/g, '');
		}

		return localizedMoment.format(format);
	} catch (error) {
		return dateStr;
	}
}

/**
 * Calculate the number of days between two ISO date strings using moment.js
 */
function calculateDaysBetween(from: string, to: string): number {
	const fromMoment = moment(from);
	const toMoment = moment(to);
	return toMoment.diff(fromMoment, 'days') + 1; // +1 to include both start and end dates
}

/**
 * Check if a period spans exactly one calendar month using moment.js
 */
function isCalendarMonth(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of the month and to is the last day of the same month
	const startOfMonth = fromMoment.clone().startOf('month');
	const endOfMonth = fromMoment.clone().endOf('month');

	return fromMoment.isSame(startOfMonth, 'day') &&
		   toMoment.isSame(endOfMonth, 'day') &&
		   fromMoment.isSame(toMoment, 'month');
}

/**
 * Check if a period spans a range of calendar months using moment.js
 */
function isCalendarMonthRange(from: string, to: string): boolean {
	const fromMoment = moment(from);
	const toMoment = moment(to);

	// Check if from is the first day of a month
	const startOfFromMonth = fromMoment.clone().startOf('month');
	if (!fromMoment.isSame(startOfFromMonth, 'day')) {
		return false;
	}

	// Check if to is the last day of a month
	const endOfToMonth = toMoment.clone().endOf('month');
	if (!toMoment.isSame(endOfToMonth, 'day')) {
		return false;
	}

	// Check if it spans multiple months
	return !fromMoment.isSame(toMoment, 'month');
}

/**
 * Determine the type of periods and generate appropriate naming
 */
export function getPeriodNamings(periods: {from: string, to: string}[], langCode: string, dateFormat: string): PeriodNaming[] {
	if (periods.length === 0) {
		return [];
	}

	// Determine period type by checking the first period
	const firstPeriod = periods[0];

	if (isCalendarMonth(firstPeriod.from, firstPeriod.to)) {
		// Type A: Calendar month periods
		const shortMonthNames = getMonthNames(langCode, "short");
		const longMonthNames = getMonthNames(langCode, "long");

		return periods.map(period => {
			const fromMoment = moment(period.from);
			const shortMonthName = shortMonthNames[fromMoment.month()];
			const longMonthName = longMonthNames[fromMoment.month()];

			return {
				graphItemLabel: shortMonthName,
				graphDescLabel: undefined,
				textName: longMonthName,
			};
		});
	} else if (isCalendarMonthRange(firstPeriod.from, firstPeriod.to)) {
		// Type B: Range of calendar months periods
		const shortMonthNames = getMonthNames(langCode, "short");
		const longMonthNames = getMonthNames(langCode, "long");

		return periods.map(period => {
			const fromMoment = moment(period.from);
			const toMoment = moment(period.to);

			const firstShortMonth = shortMonthNames[fromMoment.month()];
			const lastShortMonth = shortMonthNames[toMoment.month()];
			const shortLabel = `${firstShortMonth}-${lastShortMonth}`;

			const firstLongMonth = longMonthNames[fromMoment.month()];
			const lastLongMonth = longMonthNames[toMoment.month()];
			const longLabel = `${firstLongMonth}-${lastLongMonth}`;

			return {
				graphItemLabel: shortLabel,
				graphDescLabel: undefined,
				textName: longLabel,
			};
		});
	} else {
		// Type C: Same length periods
		const periodLength = calculateDaysBetween(firstPeriod.from, firstPeriod.to);

		// Determine period description (plural form for graphDescLabel, singular for textName)
		const periodDescSingular = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
		const periodDescPlural = periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
		const periodDescWithLabelPlural = `${periodDescPlural} periods`;
		const periodDescWithLabelSingular = `${periodDescSingular} period`;

		// Find the latest period to determine if we need to show years
		const latestPeriodMoment = moment.max(periods.map(p => moment(p.to)));

		return periods.map((period, index) => {
			const periodMoment = moment(period.to);
			const yearDiff = latestPeriodMoment.year() - periodMoment.year();
			const showYear = yearDiff > 1;

			const endDateShort = formatDateByLanguage(period.to, langCode, dateFormat, showYear);
			const endDateLong = formatDateByLanguageLong(period.to, langCode, dateFormat, showYear);
			const isLastPeriod = index === periods.length - 1;

			const textName = isLastPeriod
				? `Latest ${periodDescWithLabelSingular}`
				: `${periodDescWithLabelSingular} ending ${endDateLong}`;

			return {
				graphItemLabel: endDateShort,
				graphDescLabel: periodDescWithLabelPlural,
				textName: textName,
			};
		});
	}
}
