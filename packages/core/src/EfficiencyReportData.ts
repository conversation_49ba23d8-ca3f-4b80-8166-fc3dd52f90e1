export type EfficiencyReportData = {
	periods: Period[];
	sections: Section[];
	reportName: string;
	monthNames: string[];
	language: string;
	reportContext: ReportContext;
};

export type ReportContext = {
	useCommonWeight: boolean | null;
	breeds: string | null;
	reportDate: string | null;
};

export type Period = {
	from: string;
	to: string;
};

export type Section = {
	label: string;
	kpis: Kpi[];
};

export type Kpi = {
	label: string;
	code: string;
	isMoreBetterOpt: boolean | null;
	description?: string;
	periodValues: PeriodValue[];
};

export type PeriodValue = {
	value: number | null;
	goalOpt: number | null;
};

export type EfficiencyReportRecord = {
	report_data: EfficiencyReportData;
	created_at: string;
};
