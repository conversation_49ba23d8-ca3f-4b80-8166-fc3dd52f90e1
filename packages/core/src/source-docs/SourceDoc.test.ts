import "../../test/jest.setup";
import { beforeEach, describe, expect } from "@jest/globals";
import { SystemHoldingId } from "../HoldingId";

describe("SourceDoc", () => {
	beforeEach(async () => {
		const { sql } = await import("../database");

		await sql`
			INSERT INTO source (id, name, root_id, env)
			VALUES (1, 'segesManual', ${SystemHoldingId.rootId}, ${SystemHoldingId.env}),
						 (2, 'SOPs', 123, 'am');
		`;

		await sql`
			INSERT INTO source_doc (id, source_id, file_name, file_data)
			VALUES (1, 1, 'file1', 'file data 1'),
						 (2, 2, 'file2', 'file data 2');
		`;

		await sql`
			INSERT INTO source_doc_page (source_doc_id, languages, relevant_for, content, compressed, idx)
			VALUES (1, '{en}', '{a}', 'content 1', 'compressed 1', 1),
						 (1, '{en}', '{b}', 'content 2', 'compressed 2', 2),
						 (2, '{en}', '{a}', 'content 1', 'compressed 1', 1),
						 (2, '{en}', '{a,b}', 'content 2', 'compressed 2', 2),
						 (2, '{sk}', '{b,c}', 'content 3', 'compressed 3', 3),
						 (2, '{sk}', '{c,d}', 'content 4', 'compressed 4', 4),
						 (2, '{hu}', '{d,e}', 'content 5', 'compressed 5', 5);
		`;
	});

	test("getFullLanguages", async () => {
		const module = await import("./SourceDoc");

		const response1 = await module.getFullLanguages(module.SegesManualIdentifier);
		expect(response1).toStrictEqual(["en"]);

		const response2 = await module.getFullLanguages({ sourceName: "SOPs", holdingId: { rootId: 123, env: "am" } });
		expect(response2).toStrictEqual(["en", "sk"]);
	});

	test("getRelevantSourcePages", async () => {
		const module = await import("./SourceDoc");

		const response1 = await module.getSourceContent({
			sourceId: module.SegesManualIdentifier,
			userLanguage: "en",
			relevantFor: ["a"],
		});
		expect(response1).toMatchSnapshot();

		const response2 = await module.getSourceContent({
			sourceId: { sourceName: "SOPs", holdingId: { rootId: 123, env: "am" } },
			userLanguage: "en",
			relevantFor: ["a"],
		});
		expect(response2).toMatchSnapshot();

		const response3 = await module.getSourceContent({
			sourceId: { sourceName: "SOPs", holdingId: { rootId: 123, env: "am" } },
			userLanguage: "hu",
			relevantFor: undefined,
		});
		expect(response3).toMatchSnapshot();
	});

	test("getSources", async () => {
		const module = await import("./SourceDoc");

		const settings = await import("../VFASettings");
		const getSettingsSpy = jest.spyOn(settings, "getSettings");

		getSettingsSpy.mockResolvedValue({ useSegesManual: true, useSouthWestVetsSOPs: true });

		// with seges
		const response1 = await module.getSources({
			userLanguage: "en",
			holdingId: { rootId: 123, env: "am" },
			relevantFor: ["a", "b"],
		});

		expect(response1).toMatchSnapshot();

		//without seges
		getSettingsSpy.mockResolvedValue({ useSegesManual: false, useSouthWestVetsSOPs: false });

		const response2 = await module.getSources({
			userLanguage: "en",
			holdingId: { rootId: 123, env: "am" },
			relevantFor: ["a", "b"],
		});

		expect(response2).toMatchSnapshot();
	});
});
