// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SourceDoc getRelevantSourcePages 1`] = `
"Document: **file1**
Page: **1**

compressed 1

"
`;

exports[`SourceDoc getRelevantSourcePages 2`] = `
"Document: **file2**
Page: **1**

compressed 1

Document: **file2**
Page: **2**

compressed 2

"
`;

exports[`SourceDoc getRelevantSourcePages 3`] = `
"Document: **file2**
Page: **1**

compressed 1

Document: **file2**
Page: **2**

compressed 2

Document: **file2**
Page: **5**

compressed 5

"
`;

exports[`SourceDoc getSources 1`] = `
{
  "SOPs": "Document: **file2**
Page: **1**

compressed 1

Document: **file2**
Page: **2**

compressed 2

",
  "segesManual": "Document: **file1**
Page: **1**

compressed 1

Document: **file1**
Page: **2**

compressed 2

",
  "southWestVetsSOPs": undefined,
}
`;

exports[`SourceDoc getSources 2`] = `
{
  "SOPs": "Document: **file2**
Page: **1**

compressed 1

Document: **file2**
Page: **2**

compressed 2

",
  "segesManual": undefined,
  "southWestVetsSOPs": undefined,
}
`;
