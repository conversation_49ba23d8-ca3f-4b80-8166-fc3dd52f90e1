import { sql } from "../database";
import { renderTemplateFile } from "../utils/renderTemplateFile";
import path from "path";
import { getSettings } from "../VFASettings";
import { HoldingId, SystemHoldingId } from "../HoldingId";

export type SourceIdentifier = {
	sourceName: string;
	holdingId: HoldingId;
};

export const SegesManualIdentifier: SourceIdentifier = {
	sourceName: "segesManual",
	holdingId: SystemHoldingId,
};

export const SouthWestVetsSOPsIdentifier: SourceIdentifier = {
	sourceName: "southWestVetsSOPs",
	holdingId: SystemHoldingId,
};

/**
 * Some documentations are multilingual. This returns the full languages
 * and ignores small random languages detected by the AI.
 *
 * Any language that has 10% less pages that the largest language is ignored.
 */
export async function getFullLanguages({ sourceName, holdingId }: SourceIdentifier) {
	const rows = await sql<[{ language: string; row_count: number }]>`
		SELECT unnest(source_doc_page.languages) AS language, COUNT(*) AS row_count
		FROM source
					 JOIN source_doc on source.id = source_doc.source_id
					 JOIN source_doc_page on source_doc.id = source_doc_page.source_doc_id
		WHERE name = ${sourceName}
			AND root_id = ${holdingId.rootId}
			AND env = ${holdingId.env}
		GROUP BY language
		ORDER BY row_count DESC;`;

	const values = Object.values(rows.map((row) => row.row_count));
	const maxValue = Math.max(...values);
	const threshold = maxValue * 0.9; // 10% lower than the highest number

	return rows.filter(({ row_count }) => row_count >= threshold).map(({ language }) => language);
}

/**
 * Selects the best matching language for the user from the available languages.
 * @param userLanguage The language of the user
 * @param availableLanguages The list of available languages
 * @param defaultLanguage The default language to use if no match is found
 */
export function selectLanguage(userLanguage: string, availableLanguages: string[], defaultLanguage: string): string {
	// Normalize user language and default language to lowercase
	const normalizedUserLanguage = userLanguage.toLowerCase();
	const normalizedDefaultLanguage = defaultLanguage.toLowerCase();

	// Check if the default language is available, if not use the first available language
	const fallbackLanguage = availableLanguages.includes(normalizedDefaultLanguage) ? normalizedDefaultLanguage : availableLanguages[0];

	// Check for exact match
	if (availableLanguages.includes(normalizedUserLanguage)) {
		return normalizedUserLanguage;
	}

	// TODO Language regions (variants) must be removed in the inputs. Then this code can be removed.

	// Check for language match without region (e.g., 'en' for 'en_US')
	const languageWithoutRegion = normalizedUserLanguage.split("_")[0];
	const matchWithoutRegion = availableLanguages.find(
		(lang) => lang.toLowerCase() === languageWithoutRegion || lang.toLowerCase().startsWith(languageWithoutRegion + "_"),
	);
	if (matchWithoutRegion) {
		return matchWithoutRegion;
	}

	// If no match found, return the fallback language
	return fallbackLanguage;
}

/**
 * Returns the content for the source in the best available language.
 * Only relevant pages are included if relevantFor is specified.
 */
export async function getSourceContent(params: { sourceId: SourceIdentifier; userLanguage: string; relevantFor: string[] | undefined }) {
	const sourceId = params.sourceId;

	const fullLanguages = await getFullLanguages(sourceId);

	if (fullLanguages.length === 0) {
		return undefined;
	}

	const bestFullLanguage = selectLanguage(params.userLanguage, fullLanguages, fullLanguages[0]);

	// Combine the user language and the best full language to ensure that the best content is always loaded
	const languages = Array.from(new Set([bestFullLanguage, params.userLanguage]));

	const relevancyCondition = params.relevantFor
		? sql`AND relevant_for &&
		${params.relevantFor}`
		: sql``;

	type Page = { name: string; compressed: string; idx: number; file_name: string };

	const pages = await sql<[Page]>`
		SELECT name, compressed, idx, file_name
		FROM source
					 JOIN source_doc ON source.id = source_doc.source_id
					 JOIN source_doc_page ON source_doc.id = source_doc_page.source_doc_id
		WHERE root_id = ${sourceId.holdingId.rootId}
			AND name = ${sourceId.sourceName}
			AND env = ${sourceId.holdingId.env}
			AND languages && ${languages}
			${relevancyCondition}
		ORDER BY source_doc.file_name, source_doc_page.idx`;

	return renderTemplateFile(path.resolve(__dirname, "source.hbs"), { pages });
}

/**
 * Returns SOPs and seges manual if enabled in the settings.
 */
export async function getSources(params: { userLanguage: string; holdingId: HoldingId; relevantFor: string[] | undefined }) {
	const settings = await getSettings(params.holdingId);

	return {
		SOPs: await getSourceContent({
			...params,
			sourceId: { sourceName: "SOPs", holdingId: params.holdingId },
		}),
		segesManual: settings.useSegesManual
			? await getSourceContent({
					...params,
					sourceId: SegesManualIdentifier,
				})
			: undefined,
		southWestVetsSOPs: settings.useSouthWestVetsSOPs
			? await getSourceContent({
					...params,
					sourceId: SouthWestVetsSOPsIdentifier,
				})
			: undefined,
	};
}
