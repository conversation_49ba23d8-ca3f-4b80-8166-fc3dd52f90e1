/**
 * Extracts text from a stream array.
 */
export function extractTextFromStream(streamArray: unknown[]) {
	return streamArray.reduce((acc: string, chunk) => (typeof chunk === "string" ? acc + chunk : acc), "");
}

/**
 * Extracts text from an async generator.
 * @param stream
 */
export async function extractTextFromAsyncGenerator(stream: AsyncGenerator<unknown>) {
	let result = "";
	for await (const chunk of stream) {
		if (typeof chunk === "string") {
			result += chunk;
		}
	}
	return result;
}
