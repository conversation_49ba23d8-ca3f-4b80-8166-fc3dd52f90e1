import Handlebars from "handlebars";

export const registerUniversalHelpers = () => {
	Handlebars.registerHelper("eq", (a, b) => a === b);
	Handlebars.registerHelper("not", (a) => !a);
	Handlebars.registerHelper("round", (a: number) => Math.round(a * 100) / 100);
	Handlebars.registerHelper("add", (a, b) => a + b);
};

/**
 * Standard handlebars options used in pigbot.
 */
export const PigbotHandlebarsOptions = {
	// No need to escape {{{}}} - this is only useful if handlebars is used in HTML
	noEscape: true,
	// Missing fields in context are errors - we want to fail fast.
	strict: true,
};

registerUniversalHelpers();

/**
 * Renders a handlebars template.
 */
export function renderHandlebarTemplate(template: string, context: unknown) {
	try {
		return Handlebars.compile(template, PigbotHandlebarsOptions)(context);
	} catch (e) {
		throw Object.assign(new Error(`Failed to render template:\n${template}\nWith context:\n${JSON.stringify(context, null, 2)}`), {
			cause: e,
		});
	}
}
