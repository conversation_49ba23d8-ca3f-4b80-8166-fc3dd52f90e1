[{"Name": "% gilts of all first services", "Description": "Percentage of gilts served compared to only first services. So the the repeated servings are not compared to.", "Period dependent": "", "Is more better": "", "Code": "GILTS_OF_ALL_FIRST_SERVICES_US", "Formula": "(GILT_SERVINGS/(SERVINGS-REPEAT_SERVINGS))*100"}, {"Name": "% sows bred within 7 days", "Description": "Percentage of sows serviced in the period within 7 days after previously final weaning compared to all first time services.", "Period dependent": "", "Is more better": "✓", "Code": "SOWS_BRED_WITHIN_7_DAYS_US", "Formula": "SERVED_WITHIN_7_DAYS/(SERVINGS - REPEAT_SERVINGS - GILT_SERVINGS)*100"}, {"Name": ">>> NPD (late abortion)", "Description": "The total number of non productive days from service to when the status of the sow is changed to empty due to the registration of late abortion.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_LATE_ABORTION_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_LATE_ABORTION / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_LATE_ABORTION / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (service to dead)", "Description": "The total number of non productive days from service to when status of the sow is changed to dead.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_SERVING2DEAD_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_SERVING2DEAD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_SERVING2DEAD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (service to exit)", "Description": "The total number of non productive days from service to when status of the sow is changed to exit.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_SERVING2EXIT_PER_LITTER", "Formula": "context$breed ? ((365 * (NPD_SERVING2DEAD + NPD_SERVING2SOLD) / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * (NPD_SERVING2DEAD + NPD_SERVING2SOLD) / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (service to repeat service)", "Description": "The total number of non productive days from service to repeat service.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_SERVING2SERVING_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_SERVING2SERVING / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_SERVING2SERVING / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (service to sold)", "Description": "The total number of non productive days from service to when status of the sow is changed to sold.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_SERVING2SOLD_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_SERVING2SOLD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_SERVING2SOLD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (weaning to dead)", "Description": "The total number of non productive days from last weaning of sow to when status of the sow is changed to dead.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_WEANING2DEAD_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_WEANING2DEAD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_WEANING2DEAD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (weaning to remove)", "Description": "The total number of non productive days from last weaning of sow to when status of the sow is changed to removed.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_WEANING2SOLD_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_WEANING2SOLD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_WEANING2SOLD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": ">>> NPD (weaning to service)", "Description": "The total number of non productive days from last weaning of sow to next service.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_WEANING2SERVING_PER_LITTER", "Formula": "context$breed ? ((365 * NPD_WEANING2SERVING / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * NPD_WEANING2SERVING / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": "1st weaned sows [№]", "Description": "The total number of sows that has been weaned for the first time during the period", "Period dependent": "✓", "Is more better": "", "Code": "WEANED_PAR1_SOWS", "Formula": "WEANED_PAR1_SOWS"}, {"Name": "1st weaning [%]", "Description": "The percentage of 1st weanings (Parity 1) compared to the total number of weanings.", "Period dependent": "", "Is more better": "", "Code": "WEANED_PAR1_SOWS_RATIO", "Formula": "100 * WEANED_PAR1_SOWS / WEANED_SOWS"}, {"Name": "30 days fertility for served in period [%]", "Description": "The percent of females served that stayed pregnant for 30 days", "Period dependent": "", "Is more better": "", "Code": "SOW_FERTILITY_RATE_FROM_PERIOD$30D", "Formula": "100 * FERTILITY_FROM_PERIOD_PREGNANT$30D / FERTILITY_FROM_PERIOD_SERVINGS$30D"}, {"Name": "Abortions [%]", "Description": "Total number of abortions divided with number of productive sows * 100", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_FAILED_CYCLES_ABORTION_PCT", "Formula": "(SOWS_FAILED_CYCLES_ABORTION / AVERAGE_SOW_AMOUNT) * 100"}, {"Name": "Abortions [№]", "Description": "Total number of abortions. This includes both abortions early and later in the gestation period.", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_FAILED_CYCLES_ABORTION", "Formula": "SOWS_EARLY_ABORTIONS + SOWS_LATE_ABORTIONS"}, {"Name": "Acc. lactation length", "Description": "The average lactation length minus lactation length of nurse sows", "Period dependent": "", "Is more better": "", "Code": "ACC_LACTATION_LENGTH", "Formula": "((100 - NURSERY_SOWS) / 100) * LACTATION_DAYS"}, {"Name": "Age at first service [days]", "Description": "The age of the gilt at the date of her first service", "Period dependent": "", "Is more better": "", "Code": "PAR0_FIRST_SERVING_AGE", "Formula": "PAR0_SERVING_AGE / PAR0_KNOWN_AGE_SERVINGS"}, {"Name": "Assisted farrowing [№]", "Description": "Number of farrowing with a \"Issue helped/Assisted\" checkmark in farrowing screen", "Period dependent": "✓", "Is more better": "", "Code": "ASSISTED_FARROWINGS", "Formula": "ASSISTED_FARROWINGS"}, {"Name": "Average age of sows at period end [days]", "Description": "The average age of the sows and maiden gilts at the end of the period", "Period dependent": "", "Is more better": "", "Code": "SOWPAR0_PERIOD_END_AVG_AGE", "Formula": "(SOW_PERIOD_END_TOTAL_AGE + PAR0_PERIOD_END_TOTAL_AGE) / (SOW_PERIOD_END_REAL_AMOUNT + PAR0_PERIOD_END_REAL_AMOUNT)"}, {"Name": "Average parity number [№]", "Description": "The average parity of the sows", "Period dependent": "", "Is more better": "", "Code": "SOW_PARITY_AVG_NUMBER", "Formula": "SOW_PARITY_NUMBERS / SOW_PARITY_SOW_AMOUNT"}, {"Name": "Average repeat interval [days]", "Description": "Number of services followed at least by one reservice. Used in avg. repeat interval", "Period dependent": "✓", "Is more better": "", "Code": "REPEAT_INTERVAL_AVG", "Formula": "REPEAT_INTERVAL_DAYS / REPEAT_INTERVAL_TOTAL"}, {"Name": "Average reproduction cycle number [№]", "Description": "The average number of sow reproduction cycles", "Period dependent": "", "Is more better": "", "Code": "SOW_REPRODUCTION_CYCLE_AVG_NUMBER", "Formula": "SOW_REPRODUCTION_CYCLE_NUMBERS / SOW_REPRODUCTION_CYCLE_SOW_AMOUNT"}, {"Name": "Avg. age of dead piglets", "Description": "Average age of dead piglets", "Period dependent": "", "Is more better": "", "Code": "AVG_AGE_OF_DEAD_PIGLETS_US", "Formula": "PIGL_DEAD_AGE_TOTAL/PIGLET_DEAD_AMOUNT"}, {"Name": "Avg. number of active sows [№]", "Description": "Average number of sows based on Feeding days", "Period dependent": "", "Is more better": "", "Code": "AVERAGE_SOW_AMOUNT", "Formula": "context$breed ? (SOWS_REAL_FEEDING_DAYS / DAYS_COUNT) : (SOWS_FEEDING_DAYS / DAYS_COUNT)"}, {"Name": "Avg. number of sows, gilts and YBA", "Description": "Average number of sows, inseminated gilts and maiden gilts based on Feeding days", "Period dependent": "", "Is more better": "", "Code": "AVG_NUMBER_OF_SOWS_GILTS_AND_YBA", "Formula": "SOW_GILT_AND_YBA_FEEDING_DAYS/DAYS_COUNT"}, {"Name": "Avg. piglets weaned/litter (>0 weaned pigs)", "Description": "The average number of piglets per litter for litters that weaned 1 or more", "Period dependent": "", "Is more better": "✓", "Code": "AVG_PIGLETS_WEANEDLITTER_US", "Formula": "WEANED_PIGLETS/(WEANED_SOWS-WEANED_SOWS_ZERO_PIGS)"}, {"Name": "Avg. piglets weaned/litter (excl. partial weaning)", "Description": "The average number of piglets per litter excluding partial weanings", "Period dependent": "", "Is more better": "✓", "Code": "AVG_PIGLETS_WEANEDLITTER_EXCL_PARTIAL_WEANING_US", "Formula": "(WEANED_PIGLETS - PARTIAL_WEANED_PIGLETS)/LITTERS_WEANED_US"}, {"Name": "Avg. weaning weight [kg]", "Description": "The average weight of weaned piglets", "Period dependent": "", "Is more better": "✓", "Code": "WEANING_WEIGHT", "Formula": "WEANING_TOTAL_WEIGHT / WEANED_PIGLETS"}, {"Name": "Boars at period end [№]", "Description": "Number of boars in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "BOAR_FINAL_AMOUNT", "Formula": "BOAR_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Breeding animals feed consumption [FU]", "Description": "Feed consumption of boars, sows, inseminated gilts and maiden gilts in feed units", "Period dependent": "✓", "Is more better": "", "Code": "BREED_FEED_CONSUMPTION_ENERGY", "Formula": "settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + PAR0_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + BOAR_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + GILT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOW_FEED_CONSUMPTION_ENERGY + PAR0_FEED_CONSUMPTION_ENERGY + BOAR_FEED_CONSUMPTION_ENERGY + GILT_FEED_CONSUMPTION_ENERGY"}, {"Name": "Breeding animals feed consumption [kg]", "Description": "Feed consumption of boars, sows, inseminated gilts and maiden gilts in kg", "Period dependent": "✓", "Is more better": "", "Code": "BREED_FEED_CONSUMPTION_WEIGHT", "Formula": "settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + PAR0_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + BOAR_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + GILT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOW_FEED_CONSUMPTION_WEIGHT + PAR0_FEED_CONSUMPTION_WEIGHT + BOAR_FEED_CONSUMPTION_WEIGHT + GILT_FEED_CONSUMPTION_WEIGHT"}, {"Name": "Breeding animals feed per produced fattener [FU]", "Description": "Feed consumption of boars, sows, inseminated gilts and maiden gilts per produced fattener in feed units", "Period dependent": "", "Is more better": "", "Code": "BREED_FEED_PER_PRODUCED_FATT_ENERGY", "Formula": "(settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + PAR0_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + BOAR_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + GILT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOW_FEED_CONSUMPTION_ENERGY + PAR0_FEED_CONSUMPTION_ENERGY + BOAR_FEED_CONSUMPTION_ENERGY + GILT_FEED_CONSUMPTION_ENERGY) / FATT_PRODUCED_PIGS"}, {"Name": "Breeding animals feed per produced fattener [kg]", "Description": "Feed consumption of boars, sows, inseminated gilts and maiden gilts per produced fattener in kg", "Period dependent": "", "Is more better": "", "Code": "BREED_FEED_PER_PRODUCED_FATT_WEIGHT", "Formula": "(settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + PAR0_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + BOAR_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + GILT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOW_FEED_CONSUMPTION_WEIGHT + PAR0_FEED_CONSUMPTION_WEIGHT + BOAR_FEED_CONSUMPTION_WEIGHT + GILT_FEED_CONSUMPTION_WEIGHT) / FATT_PRODUCED_PIGS"}, {"Name": "Breeding piglets per litter [№]", "Description": "The average number of live-born breeding piglets pr litter for the given period. It is calculated as live-born breeding piglets divided by live-born piglets.", "Period dependent": "", "Is more better": "✓", "Code": "BREED_BORNS_PER_LITTER", "Formula": "BREED_BORNS / FARROWINGS"}, {"Name": "Conception rate", "Description": "Percentage or succesful services compare to the total number of survices in the period", "Period dependent": "", "Is more better": "✓", "Code": "CONCEPTION_RATE_US", "Formula": "(SUCCESSFUL_SERVICES/SERVINGS)*100"}, {"Name": "Corrected piglet mortality [%]", "Description": "Liveborn in period against total weaned in period and then in %.", "Period dependent": "", "Is more better": "✕", "Code": "PIGLET_CORRECTED_MORTALITY", "Formula": "100 * (LIVE_BORNS_PER_LITTER - WEANED_PIGLETS_PER_LITTER) / LIVE_BORNS_PER_LITTER"}, {"Name": "Days from weaning to 1st service", "Description": "Average number of days from the weaning to the first service in the period", "Period dependent": "", "Is more better": "✕", "Code": "WEANING2SERVING_DAYS", "Formula": "WEANING2SERVING_DAYS / REGULAR_SERVINGS"}, {"Name": "Dead piglet <2 days old of liveborn %", "Description": "Percetage of piglets died within 2 days after farrowing compared to liveborn in the period", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_PIGLET_2_DAYS_OLD_OF_LIVEBORN_US", "Formula": "PIGL_DEAD_LT_2_DAYS_OLD_AMOUNT*100/LIVE_BORN_AMOUNT"}, {"Name": "Dead piglet >8 days old of liveborn %", "Description": "Percetage of piglets died after 8 days after farrowing compared to liveborn in the period", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_PIGLET_8_DAYS_OLD_OF_LIVEBORN_US", "Formula": "PIGL_DEAD_GT_8_DAYS_OLD_AMOUNT*100/LIVE_BORN_AMOUNT"}, {"Name": "Dead piglet 2-8 days old of liveborn %", "Description": "Percetage of piglets died between 2 and 8 days after farrowing compared to liveborn in the period", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_PIGLET_28_DAYS_OLD_OF_LIVEBORN_US", "Formula": "PIGL_DEAD_2_8_DAYS_OLD_AMOUNT*100/LIVE_BORN_AMOUNT"}, {"Name": "Dead piglets <2 days old", "Description": "Nubmber of dead piglets that died in period and where <2 days old", "Period dependent": "✓", "Is more better": "", "Code": "PIGL_DEAD_LT_2_DAYS_OLD_AMOUNT", "Formula": "PIGL_DEAD_LT_2_DAYS_OLD_AMOUNT"}, {"Name": "Dead piglets <2 days old of total loss %", "Description": "Percentage of dead piglets that died in period and were less than 2 days old compared to the total amount of dead piglets", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_PIGLETS_2_DAYS_OLD_OF_TOTAL_LOSS_US", "Formula": "PIGL_DEAD_LT_2_DAYS_OLD_AMOUNT*100/PIGLET_DEAD_AMOUNT"}, {"Name": "Dead piglets >8 days old", "Description": "Number of dead piglets that dies in period and where >8 days old", "Period dependent": "✓", "Is more better": "", "Code": "PIGL_DEAD_GT_8_DAYS_OLD_AMOUNT", "Formula": "PIGL_DEAD_GT_8_DAYS_OLD_AMOUNT"}, {"Name": "Dead piglets >8 days old of total loss %", "Description": "Percentage of dead piglets that died in period and were more than 8 days old compared to the total amount of dead piglets", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_PIGLETS_8_DAYS_OLD_OF_TOTAL_LOSS_US", "Formula": "PIGL_DEAD_GT_8_DAYS_OLD_AMOUNT*100/PIGLET_DEAD_AMOUNT"}, {"Name": "Dead piglets before weaning [%]", "Description": "Percentage of registered dead piglets compared to the total number of liveborn piglets in the period", "Period dependent": "", "Is more better": "✕", "Code": "PIGLET_DEAD_RATIO", "Formula": "100 * PIGL_DEAD_ANIMALS_AMOUNT / LIVE_BORNS"}, {"Name": "Dead piglets before weaning [№]", "Description": "Number of registered dead piglets", "Period dependent": "✓", "Is more better": "✕", "Code": "PIGLET_DEAD_AMOUNT", "Formula": "PIGL_DEAD_ANIMALS_AMOUNT"}, {"Name": "Dead piglets from 2-8 days old", "Description": "Number of dead piglets that died in period and where between 2 and 8 days old", "Period dependent": "✓", "Is more better": "", "Code": "PIGL_DEAD_2_8_DAYS_OLD_AMOUNT", "Formula": "PIGL_DEAD_2_8_DAYS_OLD_AMOUNT"}, {"Name": "Dead served gilts [№]", "Description": "Total number of dead inseminated gilts in period", "Period dependent": "✓", "Is more better": "", "Code": "PAR0_DEAD_AMOUNT", "Formula": "PAR0_DEAD_IND_ANIMALS_AMOUNT"}, {"Name": "Dead sows [№]", "Description": "Total number of dead sows in period", "Period dependent": "✓", "Is more better": "", "Code": "SOW_DEAD_AMOUNT", "Formula": "SOW_DEAD_IND_ANIMALS_AMOUNT"}, {"Name": "Dead sows and gilts [№]", "Description": "Total number of dead sows and inseminated gilts in period", "Period dependent": "✓", "Is more better": "✕", "Code": "SOWS_DEAD_AMOUNT", "Formula": "SOW_DEAD_IND_ANIMALS_AMOUNT + PAR0_DEAD_IND_ANIMALS_AMOUNT"}, {"Name": "Dead sows ratio [%]", "Description": "Percentage of dead sows in relation to the total number of dead sows and inseminated gilts in period", "Period dependent": "", "Is more better": "✕", "Code": "SOWS_DEAD_SOLD_RATIO", "Formula": "100 * SOWS_DEAD_AMOUNT / (SOWS_SOLD_AMOUNT + SOWS_DEAD_AMOUNT)"}, {"Name": "Dead weaned piglets [№]", "Description": "The number of dead piglets in period", "Period dependent": "✓", "Is more better": "✕", "Code": "WEANED_PIGLET_DEAD_AMOUNT", "Formula": "PIGL_DEAD_GROUP_ANIMALS_AMOUNT"}, {"Name": "Early abortions [№]", "Description": "The number of early abortions in period", "Period dependent": "", "Is more better": "", "Code": "SOWS_EARLY_ABORTIONS", "Formula": "EARLY_ABORTIONS"}, {"Name": "Empty sows (>60 days, no reserved) [%]", "Description": "The percent of open sows (>60 days, no reserved)", "Period dependent": "✓", "Is more better": "", "Code": "FAILED_CYCLE_SCAN_EMPTY_PCT", "Formula": "FAILED_CYCLE_SCAN_EMPTY / SERVINGS * 100"}, {"Name": "Empty sows (>60 days, no reserved) [№]", "Description": "The number of open sows (>60 days, no reserved)", "Period dependent": "✓", "Is more better": "", "Code": "FAILED_CYCLE_SCAN_EMPTY", "Formula": "FAILED_CYCLE_SCAN_EMPTY"}, {"Name": "Exit age of sows [days]", "Description": "The average age of sows, inseminated gilts, and maiden gilts when they exit the farm", "Period dependent": "", "Is more better": "", "Code": "SOWPAR0_EXIT_AVG_AGE", "Formula": "(SOW_SOLD_IND_ANIMALS_TOTAL_AGE + PAR0_SOLD_IND_ANIMALS_TOTAL_AGE + SOW_DEAD_IND_ANIMALS_TOTAL_AGE + PAR0_DEAD_IND_ANIMALS_TOTAL_AGE) / (SOW_SOLD_IND_ANIMALS_AMOUNT + PAR0_SOLD_IND_ANIMALS_AMOUNT + SOW_DEAD_IND_ANIMALS_AMOUNT + PAR0_DEAD_IND_ANIMALS_AMOUNT)"}, {"Name": "Farrowing index", "Description": "The number of litters a sow has in a year", "Period dependent": "", "Is more better": "", "Code": "FARROWING_INDEX", "Formula": "365.25/FARROWING_INTERVAL_AVG"}, {"Name": "Farrowing pens [№]", "Description": "Number of farrowing pens specified in the locations' settings", "Period dependent": "", "Is more better": "", "Code": "FARROWING_PENS", "Formula": "FARROWING_PENS"}, {"Name": "Farrowing rate [%]", "Description": "It is how many % of the farrowings are successful compared to the number of Servings. The farrowing rate KPI can only be used for periods, which is older than the pregnancy period, so that all of the servings should be “closed” already (either farrowed or failed).", "Period dependent": "", "Is more better": "✓", "Code": "FARROWING_RATE", "Formula": "100 * FARROWING_RATE_FARROWINGS / FARROWING_RATE_SERVINGS"}, {"Name": "Farrowings [№]", "Description": "The total number of farrowed sows in the period.", "Period dependent": "✓", "Is more better": "✓", "Code": "FARROWINGS", "Formula": "FARROWINGS"}, {"Name": "Farrowings / farrowing pen / year [№]", "Description": "The total number of farrowings per farrowing pen", "Period dependent": "", "Is more better": "", "Code": "FARROWING_FARROWING_PEN_YEAR", "Formula": "(FARROWINGS / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Farrowings / week [№]", "Description": "The total number of farrowed sows per week", "Period dependent": "", "Is more better": "✓", "Code": "FARROWINGS_PER_WEEK", "Formula": "7 * FARROWINGS / DAYS_COUNT"}, {"Name": "Farrowings <7 liveborns [№]", "Description": "Number of farrowings with less than 7 liveborns", "Period dependent": "✓", "Is more better": "", "Code": "FARROWINGS_LESS_THAN_7_LIVEBORNS", "Formula": "FARROWINGS_LESS_THAN_7_LIVEBORNS"}, {"Name": "Farrowings <7 liveborns %", "Description": "Percentage of farrowings with less than 7 liveborns compared to total number of farrowings in period", "Period dependent": "", "Is more better": "✕", "Code": "FARROWINGS_7_LIVEBORNS_US", "Formula": "FARROWINGS_LESS_THAN_7_LIVEBORNS/FARROWINGS*100"}, {"Name": "Fatteners at period end [№]", "Description": "Number of fatteners in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "FATT_FINAL_AMOUNT", "Formula": "FATT_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Feed / breeding animal / year [FU]", "Description": "Feed per breeding animal (sow, insem gilt, maiden gilt & boar) per year in feed units", "Period dependent": "", "Is more better": "✕", "Code": "FEED_ENERGY_BREEDING_PIG_YEAR", "Formula": "(settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + PAR0_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + BOAR_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + GILT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOW_FEED_CONSUMPTION_ENERGY + PAR0_FEED_CONSUMPTION_ENERGY + BOAR_FEED_CONSUMPTION_ENERGY + GILT_FEED_CONSUMPTION_ENERGY) / (BREED_FEEDING_DAYS / 365)"}, {"Name": "Feed / breeding animal / year [kg]", "Description": "Feed per breeding animal (sow, insem gilt, maiden gilt & boar) per year in kg", "Period dependent": "", "Is more better": "✕", "Code": "FEED_WEIGHT_BREEDING_PIG_YEAR", "Formula": "(settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + PAR0_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + BOAR_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + GILT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOW_FEED_CONSUMPTION_WEIGHT + PAR0_FEED_CONSUMPTION_WEIGHT + BOAR_FEED_CONSUMPTION_WEIGHT + GILT_FEED_CONSUMPTION_WEIGHT) / (BREED_FEEDING_DAYS / 365)"}, {"Name": "Females dead/year [%]", "Description": "The percent of sows, insem gilts and maiden gilts dead per year", "Period dependent": "", "Is more better": "✕", "Code": "FEMALES_DEADYEAR", "Formula": "(SOWS_GILTS_DEAD_AMOUNT/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(365.25/DAYS_COUNT)*100"}, {"Name": "Females sold/year [%]", "Description": "The percent of sows, insem gilts and maiden gilts sold per year", "Period dependent": "", "Is more better": "✕", "Code": "FEMALES_SOLDYEAR", "Formula": "(SOWS_GILTS_SOLD_AMOUNT/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(365.25/DAYS_COUNT)*100"}, {"Name": "First heat marked maiden gilts [№]", "Description": "The number of maiden gilts with their first heat entered", "Period dependent": "✓", "Is more better": "", "Code": "GILT_FIRST_HEAT_MARKED", "Formula": "GILT_FIRST_HEAT_MARKED_IND_ANIMALS_AMOUNT"}, {"Name": "Gestation days at period end [days]", "Description": "The number of gestation days at the end of the period for all sows, insem gilts and maiden gilts", "Period dependent": "", "Is more better": "", "Code": "SOWS_PERIOD_END_GESTATION_DAYS", "Formula": "SOW_PERIOD_END_GESTATION_DAYS + PAR0_PERIOD_END_GESTATION_DAYS"}, {"Name": "Gestation days at period start [days]", "Description": "The number of gestation days at the start of the period for all sows, insem gilts and maiden gilts", "Period dependent": "", "Is more better": "", "Code": "SOWS_PERIOD_START_GESTATION_DAYS", "Formula": "SOW_PERIOD_START_GESTATION_DAYS + PAR0_PERIOD_START_GESTATION_DAYS"}, {"Name": "Gilt early abortions [№]", "Description": "The number of maiden gilts with early abortions", "Period dependent": "✓", "Is more better": "", "Code": "PAR0_EARLY_ABORTIONS", "Formula": "PAR0_EARLY_ABORTIONS"}, {"Name": "Gilt late abortions [№]", "Description": "The number of inseminated gilts and maiden gilts with late abortions", "Period dependent": "✓", "Is more better": "", "Code": "PAR1_LATE_ABORTIONS", "Formula": "PAR1_LATE_ABORTIONS + PAR0_SCAN_LATE_ABORTIONS"}, {"Name": "Gilts and sows at period end [№]", "Description": "Number of sows and inseminated gilts in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "PAR0SOW_FINAL_AMOUNT", "Formula": "SOW_FINAL_AMOUNT + PAR0_FINAL_AMOUNT"}, {"Name": "Gilts at period end [№]", "Description": "Number of inseminated gilts in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "PAR0_FINAL_AMOUNT", "Formula": "context$breed ? PAR0_PERIOD_END_REAL_AMOUNT : PAR0_PERIOD_END_SALDO_AMOUNT"}, {"Name": "<PERSON><PERSON> served [№]", "Description": "The total number of gilts served in the given period.", "Period dependent": "✓", "Is more better": "✓", "Code": "GILT_SERVINGS", "Formula": "GILT_SERVINGS"}, {"Name": "Ins. gilts and sows transferred out [№]", "Description": "The number of parity 0 gilts that were transferred out", "Period dependent": "✓", "Is more better": "", "Code": "PAR0SOW_TRANSFERRED_OUT_AMOUNT", "Formula": "PAR0SOW_TRANSFERRED_OUT_IND_ANIMALS_AMOUNT"}, {"Name": "Lactation period [days]", "Description": "Average lactation days calculated for weaned sows during the period. Sum of lactation days divided by the number of weaned sows. Nurse sows are not included in this calculation.", "Period dependent": "", "Is more better": "", "Code": "LACTATION_DAYS", "Formula": "SUM_LACTATION_DAYS / WEANED_SOWS"}, {"Name": "Late abortions [№]", "Description": "The number of late abortions in the period", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_LATE_ABORTIONS", "Formula": "LATE_ABORTIONS + SCAN_LATE_ABORTIONS"}, {"Name": "Litter / sow / year [№]", "Description": "The average number of litter pr. sow pr. year.", "Period dependent": "", "Is more better": "✓", "Code": "LITTER_SOW_YEAR", "Formula": "(settings$litter_sow_year_algorithm == 2 && isDefined(DAYS_COUNT)) ? SOW_FARROWING_BASED_LITTER_SOW_YEAR : (settings$litter_sow_year_algorithm == 1 ? SOW_NPD_BASED_LITTER_SOW_YEAR : SOW_CYCLE_BASED_LITTER_SOW_YEAR)"}, {"Name": "Litter reconciliation", "Description": "Liveborn piglets minus deads and weaned piglets in the period", "Period dependent": "", "Is more better": "✕", "Code": "LITTER_RECONCILIATION_US", "Formula": "LIVE_BORNS-(PIGLET_DEAD_AMOUNT+WEANED_PIGLETS_AMOUNT)"}, {"Name": "Litters weaned (>0 weaned pigs)", "Description": "The number of litters weaned (>0 weaned piglets)", "Period dependent": "", "Is more better": "", "Code": "LITTERS_WEANED_US", "Formula": "WEANED_SOWS-WEANED_SOWS_ZERO_PIGS"}, {"Name": "Litters weaned [№]", "Description": "Total number of weanings. Corresponds to the number of sows weaned in the period.", "Period dependent": "✓", "Is more better": "", "Code": "WEANED_SOWS", "Formula": "WEANED_SOWS"}, {"Name": "Live born avg.weight [kg]", "Description": "The average weight of liveborn piglets in kg", "Period dependent": "", "Is more better": "", "Code": "SOWS_LIVE_BORN_AVG_WEIGHT", "Formula": "LIVE_BORN_WEIGHT / LIVE_BORNS_WITH_WEIGHT"}, {"Name": "Live born weight per litter [kg]", "Description": "The average weight of liveborn piglets per litter in kg", "Period dependent": "", "Is more better": "", "Code": "SOWS_LIVE_BORN_WEIGHT_PER_LITTER", "Formula": "LIVE_BORN_WEIGHT / FARROWINGS_WITH_LIVE_BORN_WEIGHT"}, {"Name": "Live borns / sow / year [№]", "Description": "The average number of live born piglets pr. sow pr. year. It is calculated by multiplying the average number of litters pr sow pr year with the average number of live borns pr litter for the given period.", "Period dependent": "", "Is more better": "✓", "Code": "LIVE_BORNS_SOW_YEAR", "Formula": "LITTER_SOW_YEAR * LIVE_BORNS_PER_LITTER"}, {"Name": "Liveborn [№]", "Description": "The number of liveborn piglets", "Period dependent": "✓", "Is more better": "✓", "Code": "LIVE_BORNS", "Formula": "LIVE_BORNS"}, {"Name": "Liveborn per litter [№]", "Description": "The number of liveborn piglets per litter", "Period dependent": "", "Is more better": "✓", "Code": "LIVE_BORNS_PER_LITTER", "Formula": "LIVE_BORNS / FARROWINGS"}, {"Name": "Liveborn ratio [%]", "Description": "The percent of piglets born that were liveborn in the period", "Period dependent": "", "Is more better": "✓", "Code": "LIVE_BORNS_RATIO", "Formula": "100 * LIVE_BORNS / TOTAL_BORN_AMOUNT"}, {"Name": "Liveborn/sow/year (based on avg. sows)", "Description": "The number of liveborns per sow per year (based on avg. number of sows)", "Period dependent": "", "Is more better": "✓", "Code": "LIVEBORNSOWYEAR_US", "Formula": "(LIVE_BORNS/SOWS_AVERAGE_AMOUNT)*(365.25/DAYS_COUNT)"}, {"Name": "Lost piglets/farrowing [№]", "Description": "Liveborn per litter minus weaned pigs per litter.", "Period dependent": "", "Is more better": "✕", "Code": "LOST_PIGLETS_FARROWING", "Formula": "LIVE_BORNS_PER_LITTER - WEANED_PIGLETS_PER_LITTER"}, {"Name": "Lost piglets/farrowing incl. stillborn [№]", "Description": "Total born per litter minus weaned pigs per litter (totalborn is liveborn and stillborn).", "Period dependent": "", "Is more better": "✕", "Code": "LOST_PIGLETS_FARROWING_INCL_STILLBORN", "Formula": "LIVE_BORNS_PER_LITTER + DEAD_BORNS_PER_LITTER - WEANED_PIGLETS_PER_LITTER"}, {"Name": "Maiden gilts in first heat [№]", "Description": "The number of maiden gilts in their first heat", "Period dependent": "", "Is more better": "", "Code": "GILT_IN_FIRST_HEAT", "Formula": "GILT_IN_FIRST_HEAT_IND_ANIMALS_AMOUNT"}, {"Name": "Maiden gilts in second heat [№]", "Description": "The number of maiden gilts in their second heat", "Period dependent": "", "Is more better": "", "Code": "GILT_IN_SECOND_HEAT", "Formula": "GILT_IN_SECOND_HEAT_IND_ANIMALS_AMOUNT"}, {"Name": "Maiden gilts in sow locations [№]", "Description": "The number of maiden gilts in sow locations", "Period dependent": "", "Is more better": "", "Code": "GILT_IN_SOWS_FINAL_AMOUNT", "Formula": "GILT_IN_SOWS_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Maiden gilts in third heat [№]", "Description": "The number of maiden gilts in their third heat", "Period dependent": "", "Is more better": "", "Code": "GILT_IN_THIRD_HEAT", "Formula": "GILT_IN_THIRD_HEAT_IND_ANIMALS_AMOUNT"}, {"Name": "Matings per service", "Description": "The average number of matings per service", "Period dependent": "", "Is more better": "", "Code": "MATINGS_PER_SERVICE_US", "Formula": "SERVICE_EVENTS_TOTAL/SERVINGS"}, {"Name": "Mumificated borns [№]", "Description": "The number of mummies born", "Period dependent": "✓", "Is more better": "✕", "Code": "MUMIFICATED_BORNS", "Formula": "MUMIFICATED_BORNS"}, {"Name": "Mummified born ratio [%]", "Description": "The percent of piglets born that were mummified in the period", "Period dependent": "", "Is more better": "✕", "Code": "MUMMIFIED_BORNS_RATIO", "Formula": "100 * MUMIFICATED_BORNS / TOTAL_BORN_AMOUNT"}, {"Name": "Mummified borns per litter [№]", "Description": "The number of mummified piglets per litter", "Period dependent": "", "Is more better": "✕", "Code": "MUMMIFIED_BORNS_PER_LITTER", "Formula": "MUMIFICATED_BORNS / FARROWINGS"}, {"Name": "Negative pregnancy check [№]", "Description": "Number of sows and gilts with a negative pregnancy check in the period, but where the code does not have a mark for Abortion or Heat.", "Period dependent": "✓", "Is more better": "✕", "Code": "NEGATIVE_PREGNANCY_CHECK", "Formula": "NEGATIVE_PREGNANCY_CHECK"}, {"Name": "Non-productive days per litter", "Description": "The average number of non-productive days (NPD) for the period divided by the number of litters in the period. Non-productive days (NPD) are defined as days where the sow is in the following state: Empty, service to dead, service to exit, service to repeat service, service to sold, service to dead, weaning to dead, weaning to service, weaning to removed.", "Period dependent": "", "Is more better": "✕", "Code": "NPD_PER_LITTER", "Formula": "context$breed ? ((365 * SOWS_NPD / SOWS_REAL_FEEDING_DAYS) / LITTER_SOW_YEAR) : ((365 * SOWS_NPD / SOWS_FEEDING_DAYS) / LITTER_SOW_YEAR)"}, {"Name": "NPD / sow / year (based on avg. sows)", "Description": "The average non-productive days per sow per year based on the average number of sows", "Period dependent": "", "Is more better": "✕", "Code": "NPD_SOW_YEAR_US", "Formula": "(SOWS_NPD/SOWS_AVERAGE_AMOUNT)*(365.25/DAYS_COUNT)"}, {"Name": "Number of consecutive farrows [№]", "Description": "Number of consecutive farrowings pairs in period. Parity 2 sows and higher that farrowed in the period --> how many farrowings and previously farrowings pair.", "Period dependent": "✓", "Is more better": "", "Code": "COUNT_BETWEEN_FARROWINGS", "Formula": "COUNT_BETWEEN_FARROWINGS"}, {"Name": "Nurse sows [№]", "Description": "Number of weanings in the period which has a check mark in Nursery column", "Period dependent": "✓", "Is more better": "", "Code": "WEANED_NURSE_SOWS", "Formula": "WEANED_NURSE_SOWS"}, {"Name": "Nursery sows [%]", "Description": "The percent of weaned sows in the period that were made into nurse sows", "Period dependent": "", "Is more better": "", "Code": "NURSERY_SOWS", "Formula": "((WEANED_PIGLETS_PER_LITTER - WEANED_PIGLETS_PER_WEANING) / WEANED_PIGLETS_PER_LITTER) * 100"}, {"Name": "Observed heat [№]", "Description": "Number of sows with a heat mark in the period. Number of sows with a negative pregnancy check in the period and to that added a heat reason which is marked for Heat in Illness type screen", "Period dependent": "✓", "Is more better": "", "Code": "OBSERVED_HEAT", "Formula": "OBSERVED_HEAT"}, {"Name": "On-going nurse sows [№]", "Description": "Number of nursery weanings in the period where the sow isn't final weaned yet", "Period dependent": "✓", "Is more better": "", "Code": "ON_GOING_NURSE_SOWS", "Formula": "ON_GOING_NURSE_SOWS"}, {"Name": "Parity 1 farrowings [№]", "Description": "The number of finished farrowings of sows with parity 1 in the given period", "Period dependent": "✓", "Is more better": "✓", "Code": "PAR1_FARROWINGS", "Formula": "PAR1_FARROWINGS"}, {"Name": "Parity 1 live born avg.weight [kg]", "Description": "The average weight (kg) of liveborn piglets for all parity 1 farrowings in the period", "Period dependent": "", "Is more better": "", "Code": "PAR1_LIVE_BORN_AVG_WEIGHT", "Formula": "PAR1_LIVE_BORN_WEIGHT / PAR1_LIVE_BORNS_WITH_WEIGHT"}, {"Name": "Parity 1 live born weight per litter [kg]", "Description": "The average weight (kg) per litter of liveborn piglets for all parity 1 farrowings in the period", "Period dependent": "", "Is more better": "", "Code": "PAR1_LIVE_BORN_WEIGHT_PER_LITTER", "Formula": "PAR1_LIVE_BORN_WEIGHT / PAR1_FARROWINGS_WITH_LIVE_BORN_WEIGHT"}, {"Name": "Parity 1 liveborns [№]", "Description": "The number of liveborn piglets from the finished farrowings with parity 1 during the set period.", "Period dependent": "✓", "Is more better": "✓", "Code": "PAR1_LIVE_BORNS", "Formula": "PAR1_LIVE_BORNS"}, {"Name": "Parity 1 liveborns/litter [№]", "Description": "The average number of liveborn piglets per litter for parity 1 farrowings finished in the given period.", "Period dependent": "", "Is more better": "✓", "Code": "PAR1_LIVE_BORNS_PER_LITTER", "Formula": "PAR1_LIVE_BORNS / PAR1_FARROWINGS"}, {"Name": "Parity 1 mumificated borns [№]", "Description": "The number of mummified piglets from the finished farrowings with parity 1 during the set period.", "Period dependent": "✓", "Is more better": "✕", "Code": "PAR1_MUMIFICATED_BORNS", "Formula": "PAR1_MUMIFICATED_BORNS"}, {"Name": "Parity 1 mummificated borns/litter [№]", "Description": "The average number of mummified piglets per litter for parity 1 farrowings finished in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "PAR1_MUMMIFICATED_BORNS_PER_LITTER", "Formula": "PAR1_MUMIFICATED_BORNS / PAR1_FARROWINGS"}, {"Name": "Parity 1 stillborns [№]", "Description": "The number of stillborn piglets from the finished farrowings with parity 1 during the set period.", "Period dependent": "✓", "Is more better": "✕", "Code": "PAR1_DEAD_BORNS", "Formula": "PAR1_DEAD_BORNS"}, {"Name": "Parity 1 stillborns/litter [№]", "Description": "The average number of stillborn piglets per litter for parity 1 farrowings finished in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "PAR1_DEAD_BORNS_PER_LITTER", "Formula": "PAR1_DEAD_BORNS / PAR1_FARROWINGS"}, {"Name": "Parity 1 total born [№]", "Description": "Count of live borns, dead borns and mummified borns for parity 1 sows.", "Period dependent": "✓", "Is more better": "✓", "Code": "PARITY_1_TOTAL_BORN", "Formula": "PAR1_LIVE_BORNS + PAR1_DEAD_BORNS + PAR1_MUMIFICATED_BORNS"}, {"Name": "Parity 1 total born / litter [№]", "Description": "Count of live borns, dead borns and mummified borns per farrowings for parity 1 sows.", "Period dependent": "", "Is more better": "✓", "Code": "PAR1_TOTAL_BORN_PER_LITTER", "Formula": "PARITY_1_TOTAL_BORN / PAR1_FARROWINGS"}, {"Name": "Partial weaned pigs [№]", "Description": "Number of pigs weaned at weanings marked as \"Nursery\" in the period", "Period dependent": "✓", "Is more better": "", "Code": "PARTIAL_WEANED_PIGLETS", "Formula": "PARTIAL_WEANED_PIGLETS"}, {"Name": "Piglets [№]", "Description": "The current number of piglets in the period", "Period dependent": "", "Is more better": "", "Code": "PIGLET_FINAL_AMOUNT", "Formula": "PIGL_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Piglets for breeding [№]", "Description": "The total number of live-born piglets which have been selected for breeding in the period.", "Period dependent": "✓", "Is more better": "✓", "Code": "BREED_BORNS", "Formula": "BREED_BORNS"}, {"Name": "Pigs sold/transferred [№]", "Description": "The number of weaned pigs sold, transferred or reclassified to another inhabitant type (i.e. weaners, YBA, etc)", "Period dependent": "✓", "Is more better": "", "Code": "WEAN_EXIT_AMOUNT", "Formula": "WEAN_SOLD_AMOUNT + WEAN_RECLASSIFIED_OUT_AMOUNT + WEAN_TRANSFERRED_OUT_AMOUNT"}, {"Name": "Pre-wean mortality [%]", "Description": "It is based on the ratio between the liveborn and weaned piglets. Weaned piglets are counted in the chosen period and liveborn piglets are counted from the sows weaned in the chosen period. This KPI (pre-wean mortality) is better for a continuous flow production. It can provide bad/incorrect values however, when the piglets are exchanged between sows, when there are nursery sows. The less you exchange piglets between sows, the more accurate the number will be.", "Period dependent": "", "Is more better": "✕", "Code": "CALCULATED_PREWEANING_MORTALITY", "Formula": "100 * (REGULAR_WEANING_LIFE_BORNS - WEANED_PIGLETS) / REGULAR_WEANING_LIFE_BORNS"}, {"Name": "Pre-weaning mortality incl. nurse piglets", "Description": "It is based on the ratio between the liveborn and weaned piglets including nurse piglets. Weaned piglets are counted in the chosen period and liveborn piglets are counted from the sows weaned in the chosen period. This KPI (pre-wean mortality) is better for a continuous flow production. It can provide bad/incorrect values however, when the piglets are exchanged between sows, when there are nursery sows. The less you exchange piglets between sows, the more accurate the number will be.", "Period dependent": "", "Is more better": "✕", "Code": "PREWEANING_MORTALITY_INCL_NURSE_PIGLETS_US", "Formula": "((WEANED_SOWS_LIFE_BORNS-WEANED_PIGLETS_AMOUNT)/WEANED_SOWS_LIFE_BORNS)*100"}, {"Name": "Pregnancy at 17 weeks [%]", "Description": "Shows how many % of the sows served during the same week, are still pregnant when they are in week 17 of the pregnancy.", "Period dependent": "", "Is more better": "✓", "Code": "PREGNANCY_RATIO$17", "Formula": "100 * PREGNANCY_RATIO_PREGNANT$17 / PREGNANCY_RATIO_SERVINGS$17"}, {"Name": "Pregnancy duration [days]", "Description": "The average number of days of pregnancies for farrowed sows in the period (where farrowing was started). The duration is counted from first serving date to date where farrowing was started", "Period dependent": "", "Is more better": "✕", "Code": "GESTATING_DAYS", "Formula": "SUM_GESTATING_DAYS / FARROWINGS"}, {"Name": "Pregnancy in week 6 [%]", "Description": "Shows how many % of the sows served during the same week, are still pregnant when they are in week 6 of the pregnancy.", "Period dependent": "", "Is more better": "✓", "Code": "PREGNANCY_RATIO$6", "Formula": "100 * PREGNANCY_RATIO_PREGNANT$6 / PREGNANCY_RATIO_SERVINGS$6"}, {"Name": "Produced piglets [№]", "Description": "The number of piglets produced in the period", "Period dependent": "", "Is more better": "", "Code": "PIGL_PRODUCED_PIGS", "Formula": "settings$sow_production_with_nursery ? NURSERY_PRODUCED_PIGS : SUCKLING_PRODUCED_PIGS"}, {"Name": "Produced piglets / farrowing pen / year [kg]", "Description": "The weight of piglets produced per farrowing pen per year in kg", "Period dependent": "", "Is more better": "", "Code": "PIGL_PRODUCED_PIG_FARROWING_PEN_YEAR", "Formula": "(settings$sow_production_with_nursery ? WEAN_EXIT_AVG_WEIGHT : WEANING_WEIGHT) * (PIGL_PRODUCED_PIGS / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Regular lactation period [days]", "Description": "Average lactation days calculated for weaned sows during the period that are within your set minimum and maximum lactation length days. This also excludes all nurse sows even if it is the final wean for a nurse sow.", "Period dependent": "", "Is more better": "", "Code": "REGULAR_LACTATION_DAYS", "Formula": "SUM_REGULAR_LACTATION_DAYS / REGULAR_WEANED_SOWS"}, {"Name": "Repeat services [№]", "Description": "The number of repeated inseminations that have been made during the given period.", "Period dependent": "✓", "Is more better": "", "Code": "REPEAT_SERVINGS", "Formula": "REPEAT_SERVINGS"}, {"Name": "Replacement rate", "Description": "The percent of servings that belong to gilts in the period", "Period dependent": "", "Is more better": "✕", "Code": "REPLACEMENT_RATE_US", "Formula": "(GILT_SERVINGS/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(365.25/DAYS_COUNT)*100"}, {"Name": "Replacement rate at serving [%]", "Description": "The percent of servings that belong to gilts in the period based on sow feeding days", "Period dependent": "", "Is more better": "", "Code": "SOWS_SERVING_BASED_REPLACEMENT_RATE", "Formula": "100 * (365 * GILT_SERVINGS / SOWS_FEEDING_DAYS)"}, {"Name": "Replacement rate at weaning [%]", "Description": "The percent of weanings that belong to parity 1 sows in the period", "Period dependent": "", "Is more better": "", "Code": "SOWS_WEANING_BASED_REPLACEMENT_RATE", "Formula": "100 * LITTER_SOW_YEAR * WEANED_PAR1_SOWS / WEANED_SOWS"}, {"Name": "Replacement rate incl. bought gilts", "Description": "The percent of servings that belong to gilts in the period including any purchased gilts", "Period dependent": "", "Is more better": "✕", "Code": "REPLACEMENT_RATE_US_INCL_BOUGHT_GILTS", "Formula": "((GILT_SERVINGS+PAR0_BOUGHT_AMOUNT)/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(365.25/DAYS_COUNT)*100"}, {"Name": "Returner rate [%]", "Description": "The percentages of all servings were repeat servings during the given period.", "Period dependent": "", "Is more better": "✕", "Code": "REPEAT_SERVINGS_RATIO", "Formula": "100 * REPEAT_SERVINGS / SERVINGS"}, {"Name": "Second heat marked maiden gilts [№]", "Description": "The number of maiden gilts with their second heat entered", "Period dependent": "✓", "Is more better": "", "Code": "GILT_SECOND_HEAT_MARKED", "Formula": "GILT_SECOND_HEAT_MARKED_IND_ANIMALS_AMOUNT"}, {"Name": "Service events [№]", "Description": "Total number of service events in a period", "Period dependent": "✓", "Is more better": "", "Code": "SERVICE_EVENTS_TOTAL", "Formula": "SERVICE_EVENTS_TOTAL"}, {"Name": "Services [№]", "Description": "The number of servings in the period", "Period dependent": "✓", "Is more better": "✓", "Code": "SERVINGS", "Formula": "SERVINGS"}, {"Name": "Services / week [№]", "Description": "The number of services per week", "Period dependent": "", "Is more better": "✓", "Code": "SERVINGS_PER_WEEK", "Formula": "7 * SERVINGS / DAYS_COUNT"}, {"Name": "Sold fattener / farrowing pen / year [kg]", "Description": "The average weight of sold fatteners per farrowing pen per year", "Period dependent": "", "Is more better": "", "Code": "FATT_SOLD_TOTAL_WEIGHT_FARROWING_PEN_YEAR", "Formula": "(FATT_SOLD_TOTAL_WEIGHT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Sold piglets [№]", "Description": "The number of sold piglets in a period", "Period dependent": "✓", "Is more better": "", "Code": "PIGLET_SOLD_AMOUNT", "Formula": "PIGL_SOLD_GROUP_ANIMALS_AMOUNT + PIGL_SOLD_IND_ANIMALS_AMOUNT"}, {"Name": "Sold sows and gilts [№]", "Description": "The number of sold sows, inseminated gilts and maiden gilts in a period", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_SOLD_AMOUNT", "Formula": "SOW_SOLD_IND_ANIMALS_AMOUNT + PAR0_SOLD_IND_ANIMALS_AMOUNT"}, {"Name": "Sold weaner / farrowing pen / year [kg]", "Description": "The average weight of sold weaners per farrowing pen per year", "Period dependent": "", "Is more better": "", "Code": "WEAN_SOLD_TOTAL_WEIGHT_FARROWING_PEN_YEAR", "Formula": "(WEAN_SOLD_TOTAL_WEIGHT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Sow and ins. gilt feeding days [days]", "Description": "The number of feeding days for sows, inseminated gilts, and maiden gilts", "Period dependent": "✓", "Is more better": "✓", "Code": "SOWS_FEEDING_DAYS", "Formula": "SOW_FEEDING_DAYS + PAR0_FEEDING_DAYS"}, {"Name": "Sow animal unit [AU]", "Description": "", "Period dependent": "✓", "Is more better": "", "Code": "SOW_ANIMAL_UNITS", "Formula": "(SOWS_FEEDING_DAYS / 365) / 4.3 + (GILT_FEEDING_DAYS/2750)"}, {"Name": "Sow feed / sow / year [FU]", "Description": "Sow feed consumption per sow per year in feed units", "Period dependent": "", "Is more better": "", "Code": "SOW_FEED_ENERGY_SOW_YEAR", "Formula": "context$breed ? ((settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOWS_FEED_CONSUMPTION_ENERGY) / (SOWS_REAL_FEEDING_DAYS / 365)) * (SOWS_REAL_FEEDING_DAYS / TOTAL_SOWS_REAL_FEEDING_DAYS) : (settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOWS_FEED_CONSUMPTION_ENERGY) / (SOWS_FEEDING_DAYS / 365)"}, {"Name": "Sow feed / sow / year [kg]", "Description": "Sow feed consumption per sow per year in kg", "Period dependent": "", "Is more better": "", "Code": "SOW_FEED_WEIGHT_SOW_YEAR", "Formula": "context$breed ? ((settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOWS_FEED_CONSUMPTION_WEIGHT) / (SOWS_REAL_FEEDING_DAYS / 365)) * (SOWS_REAL_FEEDING_DAYS / TOTAL_SOWS_REAL_FEEDING_DAYS) : (settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOWS_FEED_CONSUMPTION_WEIGHT) / (SOWS_FEEDING_DAYS / 365)"}, {"Name": "Sow feed / transferred weaner [FU]", "Description": "Sow feed consumption per transferred/sold weaner in feed units", "Period dependent": "", "Is more better": "✕", "Code": "SOW_FEED_ENERGY_TRANSFERRED_WEANERS", "Formula": "(settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOWS_FEED_CONSUMPTION_ENERGY) / (WEAN_SOLD_AMOUNT + WEAN_RECLASSIFIED_OUT_AMOUNT + WEAN_TRANSFERRED_OUT_AMOUNT)"}, {"Name": "Sow feed / transferred weaner [kg]", "Description": "Sow feed consumption per transferred/sold weaner in kg", "Period dependent": "", "Is more better": "✕", "Code": "SOW_FEED_WEIGHT_TRANSFERRED_WEANERS", "Formula": "(settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOWS_FEED_CONSUMPTION_WEIGHT) / (WEAN_SOLD_AMOUNT + WEAN_RECLASSIFIED_OUT_AMOUNT + WEAN_TRANSFERRED_OUT_AMOUNT)"}, {"Name": "Sow feed / weaned piglet [FU]", "Description": "Sow feed consumption per weaned piglet in feed units", "Period dependent": "", "Is more better": "✕", "Code": "SOW_FEED_ENERGY_WEANED_PIGLETS", "Formula": "(WEANED_PIGLETS / TOTAL_WEANED_PIGLETS) * ((settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOWS_FEED_CONSUMPTION_ENERGY) / WEANED_PIGLETS)"}, {"Name": "Sow feed / weaned piglet [kg]", "Description": "Sow feed consumption per weaned piglet in kg", "Period dependent": "", "Is more better": "✕", "Code": "SOW_FEED_WEIGHT_WEANED_PIGLETS", "Formula": "(WEANED_PIGLETS / TOTAL_WEANED_PIGLETS) * ((settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOWS_FEED_CONSUMPTION_WEIGHT) / WEANED_PIGLETS)"}, {"Name": "Sow feed consumption [FU]", "Description": "Sow, inseminated gilt, and maiden gilt feed consumption in feed units", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_FEED_CONSUMPTION_ENERGY", "Formula": "settings$include_batches_in_feed_cons ? SOW_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + PAR0_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOW_FEED_CONSUMPTION_ENERGY + PAR0_FEED_CONSUMPTION_ENERGY"}, {"Name": "Sow feed consumption [kg]", "Description": "Sow, inseminated gilt, and maiden gilt feed consumption in kg", "Period dependent": "✓", "Is more better": "", "Code": "SOWS_FEED_CONSUMPTION_WEIGHT", "Formula": "SOW_FEED_CONSUMPTION_WEIGHT + PAR0_FEED_CONSUMPTION_WEIGHT"}, {"Name": "Sow feed energy / produced piglet [FU]", "Description": "Sow feed consumption per produced piglet in feed units", "Period dependent": "", "Is more better": "", "Code": "SOW_FEED_ENERGY_PIGL_PRODUCED_PIGS", "Formula": "(settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : SOWS_FEED_CONSUMPTION_ENERGY) / PIGL_PRODUCED_PIGS"}, {"Name": "Sow feed weight / produced piglet [kg]", "Description": "Sow feed consumption per produced piglet in kg", "Period dependent": "", "Is more better": "", "Code": "SOW_FEED_WEIGHT_PIGL_PRODUCED_PIGS", "Formula": "(settings$include_batches_in_feed_cons ? SOWS_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : SOWS_FEED_CONSUMPTION_WEIGHT) / PIGL_PRODUCED_PIGS"}, {"Name": "Sow, gilt and YBA at period end", "Description": "Number of sows, inseminated gilts and breeding animals in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "SOW_GILT_AND_YBA_AT_PERIOD_END", "Formula": "PAR0SOW_FINAL_AMOUNT+GILT_FINAL_AMOUNT"}, {"Name": "Sows added/gilts entered", "Description": "The number of maiden gilts served plus the number of maiden gilts purchased", "Period dependent": "", "Is more better": "", "Code": "SOWS_ADDEDGILTS_ENTERED_US", "Formula": "GILT_SERVINGS+PAR0SOW_BOUGHT_AMOUNT"}, {"Name": "Sows at period end [№]", "Description": "Number of sows in inventory at the end of the chosen period", "Period dependent": "", "Is more better": "", "Code": "SOW_FINAL_AMOUNT", "Formula": "context$breed ? SOW_PERIOD_END_REAL_AMOUNT : SOW_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Sows bred by 7 days [№]", "Description": "Sows serviced in period within 7 days after previously final weaning. Count of how many sows are served within 7 days after their previously final weaning", "Period dependent": "✓", "Is more better": "", "Code": "SERVED_WITHIN_7_DAYS", "Formula": "SERVED_WITHIN_7_DAYS"}, {"Name": "Sows culling rate [%/year]", "Description": "The percent of sows sold per year", "Period dependent": "", "Is more better": "", "Code": "PAR0SOW_CULLING_RATE", "Formula": "(SOWS_SOLD_AMOUNT / DAYS_COUNT) * 365 * 100 / AVERAGE_SOW_AMOUNT"}, {"Name": "Sows mortality rate [%/year]", "Description": "The percent of sows that died per year", "Period dependent": "", "Is more better": "", "Code": "PAR0SOW_DEAD_RATE", "Formula": "(SOWS_DEAD_AMOUNT / DAYS_COUNT) * 365 * 100 / AVERAGE_SOW_AMOUNT"}, {"Name": "Sows remove rate [%/year]", "Description": "The percent of sows that died or was sold per year", "Period dependent": "", "Is more better": "", "Code": "PAR0SOW_REMOVE_RATE", "Formula": "((SOWS_SOLD_AMOUNT + SOWS_DEAD_AMOUNT) / DAYS_COUNT) * 365 * 100 / AVERAGE_SOW_AMOUNT"}, {"Name": "Sows served [№]", "Description": "The number of sows that were served", "Period dependent": "✓", "Is more better": "✓", "Code": "SOW_SERVINGS", "Formula": "REGULAR_SERVINGS"}, {"Name": "Sows weaned with 0 pigs [№]", "Description": "Number of sows weaned with 0 weaned pigs.", "Period dependent": "✓", "Is more better": "", "Code": "WEANED_SOWS_ZERO_PIGS", "Formula": "WEANED_SOWS_ZERO_PIGS"}, {"Name": "Stillborn [№]", "Description": "Total number of still-born piglets for the farrowings in the given period.", "Period dependent": "✓", "Is more better": "✕", "Code": "DEAD_BORNS", "Formula": "DEAD_BORNS"}, {"Name": "Stillborn per litter [№]", "Description": "The average number of still-born piglets per litter in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_BORNS_PER_LITTER", "Formula": "DEAD_BORNS / FARROWINGS"}, {"Name": "Stillborns [%]", "Description": "Percent of still-born piglets for the farrowings in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "DEAD_BORNS_RATIO", "Formula": "100 * DEAD_BORNS / TOTAL_BORN_AMOUNT"}, {"Name": "Suckling days / farrowing pen / year", "Description": "The number of suckling days per farrowing pen per year", "Period dependent": "", "Is more better": "", "Code": "SUCKLING_DAYS_FARROWING_PEN_YEAR", "Formula": "(FARROWINGS / DAYS_COUNT) * 365 * LACTATION_DAYS / FARROWING_PENS"}, {"Name": "Third heat marked maiden gilts [№]", "Description": "The number of maiden gilts with their third heat entered", "Period dependent": "✓", "Is more better": "", "Code": "GILT_THIRD_HEAT_MARKED", "Formula": "GILT_THIRD_HEAT_MARKED_IND_ANIMALS_AMOUNT"}, {"Name": "Total born [№]", "Description": "Total number of born piglets. Includes liveborn, still born and mummified piglets.", "Period dependent": "✓", "Is more better": "", "Code": "TOTAL_BORN_AMOUNT", "Formula": "LIVE_BORNS + DEAD_BORNS + MUMIFICATED_BORNS"}, {"Name": "Total born / litter [№]", "Description": "The number of total born piglets per litter for the farrowings in the given period.", "Period dependent": "", "Is more better": "", "Code": "TOTAL_BORN_PER_LITTER", "Formula": "TOTAL_BORN_AMOUNT / FARROWINGS"}, {"Name": "Total dead piglets [№]", "Description": "The total number of dead piglets", "Period dependent": "✓", "Is more better": "✕", "Code": "TOTAL_DEAD_PIGLETS", "Formula": "DEAD_BORNS + PIGLET_DEAD_AMOUNT"}, {"Name": "Total dead piglets per litter [№]", "Description": "The average number of dead piglets per litter", "Period dependent": "", "Is more better": "✕", "Code": "TOTAL_DEAD_PIGLETS_PER_LITTER", "Formula": "TOTAL_DEAD_PIGLETS / FARROWINGS"}, {"Name": "Total feed conversion [FU/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_CONVERSION_RATIO_ENERGY", "Formula": "(settings$include_batches_in_feed_cons ? ALL_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : ALL_FEED_CONSUMPTION_ENERGY)/ALL_TOTAL_WEIGHT_GAIN"}, {"Name": "Total feed conversion [kg/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_CONVERSION_RATIO_WEIGHT", "Formula": "(settings$include_batches_in_feed_cons ? ALL_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : ALL_FEED_CONSUMPTION_WEIGHT)/ALL_TOTAL_WEIGHT_GAIN"}, {"Name": "Total feed conversion without dead [FU/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_CONVERSION_RATIO_WITHOUT_DEAD_ENERGY", "Formula": "(settings$include_batches_in_feed_cons ? ALL_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : ALL_FEED_CONSUMPTION_ENERGY)/ALL_TOTAL_WEIGHT_GAIN_WITHOUT_DEAD"}, {"Name": "Total feed conversion without dead [kg/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_CONVERSION_RATIO_WITHOUT_DEAD_WEIGHT", "Formula": "ALL_FEED_CONSUMPTION_WEIGHT/ALL_TOTAL_WEIGHT_GAIN_WITHOUT_DEAD"}, {"Name": "Total feed per sold liveweight [FU/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_PER_SOLD_LIVEWEIGHT_ENERGY", "Formula": "ALL_FEED_CONSUMPTION_ENERGY / ALL_SOLD_TOTAL_WEIGHT"}, {"Name": "Total feed per sold liveweight [kg/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "ALL_FEED_PER_SOLD_LIVEWEIGHT_WEIGHT", "Formula": "ALL_FEED_CONSUMPTION_WEIGHT / ALL_SOLD_TOTAL_WEIGHT"}, {"Name": "Transferred fattener / farrowing pen / year [№]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "FATT_REARED_FARROWING_PEN_YEAR", "Formula": "(FATT_EXIT_AMOUNT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Transferred fattener / sow / year [№]", "Description": "", "Period dependent": "", "Is more better": "✓", "Code": "TRANSFERRED_FATTENERS_SOW_YEAR", "Formula": "(FATT_SOLD_AMOUNT + FATT_RECLASSIFIED_OUT_AMOUNT + FATT_TRANSFERRED_OUT_AMOUNT) / (SOWS_FEEDING_DAYS / 365)"}, {"Name": "Transferred weaner / farrowing pen / year [№]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEAN_REARED_FARROWING_PEN_YEAR", "Formula": "(WEAN_EXIT_AMOUNT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Transferred weaner / sow / year [№]", "Description": "", "Period dependent": "", "Is more better": "✓", "Code": "TRANSFERRED_WEANERS_SOW_YEAR", "Formula": "(WEAN_SOLD_AMOUNT + WEAN_RECLASSIFIED_OUT_AMOUNT + WEAN_TRANSFERRED_OUT_AMOUNT) / (SOWS_FEEDING_DAYS / 365)"}, {"Name": "Vital liveborn piglets [%]", "Description": "Percent of vital live born piglets for the farrowings in the given period.", "Period dependent": "", "Is more better": "✓", "Code": "VITAL_LIVE_BORNS_RATIO", "Formula": "100 * (LIVE_BORNS - WEAK_BORNS) / LIVE_BORNS"}, {"Name": "Weak borns [%]", "Description": "Percent of weak born piglets for the farrowings in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "WEAK_BORNS_RATIO", "Formula": "100 * WEAK_BORNS / LIVE_BORNS"}, {"Name": "Weak borns [№]", "Description": "Total number of weak born piglets for the farrowings in the given period.", "Period dependent": "✓", "Is more better": "✕", "Code": "WEAK_BORNS", "Formula": "WEAK_BORNS"}, {"Name": "Weak borns per litter [№]", "Description": "The number of weak born piglets per litter for the farrowings in the given period.", "Period dependent": "", "Is more better": "✕", "Code": "WEAK_BORNS_PER_LITTER", "Formula": "WEAK_BORNS / FARROWINGS"}, {"Name": "Weaned piglets [№]", "Description": "The number of weaned piglets at the end of the period", "Period dependent": "", "Is more better": "", "Code": "WEANED_PIGLET_FINAL_AMOUNT", "Formula": "WEANED_PIGL_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Weaned piglets [№]", "Description": "Total number of weaned piglets", "Period dependent": "✓", "Is more better": "✓", "Code": "WEANED_PIGLETS", "Formula": "WEANED_PIGLETS"}, {"Name": "Weaned piglets / farrowing pen / year [№]", "Description": "The average number of wenaed piglets per farrowing pen per year", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_FARROWING_PEN_YEAR", "Formula": "(WEANED_PIGLETS_AMOUNT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Weaned piglets / farrowing pen / year [№]", "Description": "The average number of weaned piglets per farrowing pen per year based on the average number of sows", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_FARROWING_PEN_YEAR2", "Formula": "(AVERAGE_SOW_AMOUNT * WEANED_PIGLETS_SOW_YEAR) / FARROWING_PENS"}, {"Name": "Weaned piglets / female / year (based on avg. sows+yba)", "Description": "The average number of weaned piglets per female per year based on the average number of sows, inseminated gilts and maiden gilts", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_FEMALE_YEAR_US", "Formula": "(WEANED_PIGLETS/AVG_NUMBER_OF_SOWS_GILTS_AND_YBA)*(362.25/DAYS_COUNT)"}, {"Name": "Weaned piglets / sow / year (based on avg. sows)", "Description": "The average number of weaned piglets per sow per year based on the average number of sows", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_SOW_YEAR_US", "Formula": "(WEANED_PIGLETS/SOWS_AVERAGE_AMOUNT)*(365.25/DAYS_COUNT)"}, {"Name": "Weaned piglets / sow / year [№]", "Description": "The average number of weaned piglets per sow per year", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_SOW_YEAR", "Formula": "LITTER_SOW_YEAR * WEANED_PIGLETS_PER_LITTER"}, {"Name": "Weaned piglets / week [№]", "Description": "The average number of weaned piglets per week", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_PER_WEEK", "Formula": "7 * WEANED_PIGLETS / DAYS_COUNT"}, {"Name": "Weaned piglets per litter [№]", "Description": "The average number of weaned piglets for each litter. It is calculated by dividing the number of all weaned piglets with the number of weaned sows.", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_PER_LITTER", "Formula": "WEANED_PIGLETS / WEANED_SOWS"}, {"Name": "Weaned piglets per weaning [№]", "Description": "The average number of piglets per weaning and is calculated as the total number of weaned piglets divided by the total number of weanings during the period.", "Period dependent": "", "Is more better": "✓", "Code": "WEANED_PIGLETS_PER_WEANING", "Formula": "WEANED_PIGLETS / WEANINGS"}, {"Name": "Weaned piglets total weight [kg]", "Description": "The total weight of all weaned piglets in the period", "Period dependent": "✓", "Is more better": "✓", "Code": "WEANED_PIGLETS_TOTAL_WEIGHT", "Formula": "WEANING_TOTAL_WEIGHT"}, {"Name": "Weaned sows / week [№]", "Description": "Number of sows weaned per week.", "Period dependent": "", "Is more better": "", "Code": "WEANED_SOWS_PER_WEEK", "Formula": "7 * WEANED_SOWS / DAYS_COUNT"}, {"Name": "Weaned sows liveborn [№]", "Description": "The number of liveborns for all weaned sows", "Period dependent": "", "Is more better": "", "Code": "WEANED_SOWS_LIFE_BORNS", "Formula": "WEANED_SOWS_LIFE_BORNS"}, {"Name": "Weaned sows liveborn / litter [№]", "Description": "The number of liveborn per weaned sow", "Period dependent": "", "Is more better": "", "Code": "WEANED_SOWS_LIFE_BORNS_PER_LITTER", "Formula": "WEANED_SOWS_LIFE_BORNS / WEANED_SOWS"}, {"Name": "Weaned sows stillborn / litter [№]", "Description": "The number of stillborn per weaned sow", "Period dependent": "", "Is more better": "", "Code": "WEANED_SOWS_DEAD_BORNS_PER_LITTER", "Formula": "WEANED_SOWS_DEAD_BORNS / WEANED_SOWS"}, {"Name": "Weaned weight / farrowing pen / year [kg]", "Description": "The average weight per weaned litter per farrowing pen per year based on the total weight of all weaned pigs", "Period dependent": "", "Is more better": "", "Code": "WEANED_WEIGHT_FARROWING_PEN_YEAR", "Formula": "(WEANED_PIGLETS_TOTAL_WEIGHT / DAYS_COUNT) * 365 / FARROWING_PENS"}, {"Name": "Weaned weight / farrowing pen / year [kg]", "Description": "The average weight per weaned litter per farrowing pen per year based on the average number of sows and average number of piglets per sow", "Period dependent": "", "Is more better": "", "Code": "WEANED_WEIGHT_FARROWING_PEN_YEAR2", "Formula": "(AVERAGE_SOW_AMOUNT * WEANED_PIGLETS_SOW_YEAR * WEANING_WEIGHT) / FARROWING_PENS"}, {"Name": "Weaner and fattener feed consumption [FU]", "Description": "", "Period dependent": "✓", "Is more better": "", "Code": "WEANFATT_FEED_CONSUMPTION_ENERGY", "Formula": "settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + FATT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : WEAN_FEED_CONSUMPTION_ENERGY + FATT_FEED_CONSUMPTION_ENERGY"}, {"Name": "Weaner and fattener feed consumption [kg]", "Description": "", "Period dependent": "✓", "Is more better": "", "Code": "WEANFATT_FEED_CONSUMPTION_WEIGHT", "Formula": "settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + FATT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : WEAN_FEED_CONSUMPTION_WEIGHT + FATT_FEED_CONSUMPTION_WEIGHT"}, {"Name": "Weaner and fattener feed per produced fattener [FU]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANFATT_FEED_PER_PRODUCED_FATT_ENERGY", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + FATT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : WEAN_FEED_CONSUMPTION_ENERGY + FATT_FEED_CONSUMPTION_ENERGY) / FATT_PRODUCED_PIGS"}, {"Name": "Weaner and fattener feed per produced fattener [kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANFATT_FEED_PER_PRODUCED_FATT_WEIGHT", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + FATT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : WEAN_FEED_CONSUMPTION_WEIGHT + FATT_FEED_CONSUMPTION_WEIGHT) / FATT_PRODUCED_PIGS"}, {"Name": "Weaner and fattener feed per sold liveweight [FU/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANFATT_FEED_PER_SOLD_LIVEWEIGHT_ENERGY", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_ENERGY + FATT_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : WEAN_FEED_CONSUMPTION_ENERGY + FATT_FEED_CONSUMPTION_ENERGY) / (WEAN_SOLD_TOTAL_WEIGHT + FATT_SOLD_TOTAL_WEIGHT)"}, {"Name": "Weaner and fattener feed per sold liveweight [kg/1kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANFATT_FEED_PER_SOLD_LIVEWEIGHT_WEIGHT", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT + FATT_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : WEAN_FEED_CONSUMPTION_WEIGHT + FATT_FEED_CONSUMPTION_WEIGHT) / (WEAN_SOLD_TOTAL_WEIGHT + FATT_SOLD_TOTAL_WEIGHT)"}, {"Name": "Weaner feed / transferred weaner [FU]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANER_FEED_ENERGY_TRANSFERRED_WEANERS", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_ENERGY : WEAN_FEED_CONSUMPTION_ENERGY) / WEAN_EXIT_AMOUNT"}, {"Name": "Weaner feed / transferred weaner [kg]", "Description": "", "Period dependent": "", "Is more better": "", "Code": "WEANER_FEED_WEIGHT_TRANSFERRED_WEANERS", "Formula": "(settings$include_batches_in_feed_cons ? WEAN_FEED_CONSUMPTION_WITH_BATCHES_WEIGHT : WEAN_FEED_CONSUMPTION_WEIGHT) / WEAN_EXIT_AMOUNT"}, {"Name": "Weaners [№]", "Description": "The number of weaners at the end of the period", "Period dependent": "", "Is more better": "", "Code": "WEANER_FINAL_AMOUNT", "Formula": "WEAN_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Weaning to fertile insemination avg. days", "Description": "Calculated as days from a weaning and until the sow is served with an insemination that leads to a farrowing and divided by number of this weanings.", "Period dependent": "✓", "Is more better": "✕", "Code": "WEANING_TO_FERTILE_INSEMINATION_DAYS_AVG", "Formula": "WEANING_TO_FERTILE_INSEMINATION_DAYS / WEANING_TO_FERTILE_INSEMINATION_SOWS"}, {"Name": "Weaning weight per litter [kg]", "Description": "The average weight per litter in kg", "Period dependent": "", "Is more better": "", "Code": "WEANING_WEIGHT_PER_LITTER", "Formula": "WEANING_TOTAL_WEIGHT / WEANED_SOWS"}, {"Name": "Weaning weight per weaning [kg]", "Description": "The average weight per weaning in kg", "Period dependent": "", "Is more better": "", "Code": "WEANING_WEIGHT_PER_WEANING", "Formula": "WEANING_TOTAL_WEIGHT / WEANINGS"}, {"Name": "Weanings [№]", "Description": "Total number of weanings.", "Period dependent": "✓", "Is more better": "", "Code": "WEANINGS", "Formula": "WEANINGS"}, {"Name": "Young boars at period end [№]", "Description": "The number of young boars at the end of the period", "Period dependent": "", "Is more better": "", "Code": "YOUNG_BOAR_FINAL_REAL_AMOUNT", "Formula": "YOUNG_BOAR_PERIOD_END_REAL_AMOUNT"}, {"Name": "Young breeding animals [№]", "Description": "The number of maiden gilts at the end of the period", "Period dependent": "", "Is more better": "", "Code": "MAIDENGILT_FINAL_AMOUNT", "Formula": "GILT_PERIOD_END_SALDO_AMOUNT"}, {"Name": "Young breeding animals age > 180 days [№]", "Description": "The number of maiden gilts that are older than 180 days", "Period dependent": "", "Is more better": "", "Code": "GILT_ABOVE_AGE_AMOUNT$180D", "Formula": "GILT_ABOVE_AGE_AMOUNT$180D"}, {"Name": "Young breeding animals age ≤ 180 days [№]", "Description": "The number of maiden gilts that are 180 days old or younger", "Period dependent": "", "Is more better": "", "Code": "GILT_BELLOW_AGE_AMOUNT$180D", "Formula": "GILT_BELLOW_AGE_AMOUNT$180D"}, {"Name": "Young breeding animals in breeding animal locations at period end [№]", "Description": "The number of maiden gilts in a breeding animal location at the end of the period", "Period dependent": "", "Is more better": "", "Code": "GILT_IN_OTHERS_FINAL_AMOUNT", "Formula": "GILT_PERIOD_END_SALDO_AMOUNT - GILT_IN_SOWS_PERIOD_END_SALDO_AMOUNT"}]