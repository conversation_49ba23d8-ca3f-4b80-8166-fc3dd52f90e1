export function removeMarkdown(markdownText: string): string {
	if (!markdownText) {
		return "";
	}

	// Remove headers (# Header)
	let plainText = markdownText.replace(/^(#+\s*)/gm, "");

	// Remove bold/italic markers
	plainText = plainText.replace(/(\*\*|__)(.*?)\1/g, "$2");
	plainText = plainText.replace(/(\*|_)(.*?)\1/g, "$2");

	// Remove code blocks
	plainText = plainText.replace(/``````/g, "");
	plainText = plainText.replace(/`([^`]+)`/g, "$1");

	// Remove links
	plainText = plainText.replace(/\[([^\]]+)\]\([^)]+\)/g, "$1");

	// Remove images
	plainText = plainText.replace(/!\[([^\]]+)\]\([^)]+\)/g, "");

	// Remove blockquotes
	plainText = plainText.replace(/^\s*>\s*/gm, "");

	// Remove horizontal rules
	plainText = plainText.replace(/^\s*[-*_]{3,}\s*$/gm, "");

	// Remove list markers
	plainText = plainText.replace(/^\s*[-*+]\s+/gm, "");
	plainText = plainText.replace(/^\s*\d+\.\s+/gm, "");

	// Remove HTML tags
	plainText = plainText.replace(/<[^>]*>/g, "");

	// Remove extra whitespace
	plainText = plainText.replace(/\n\s*\n/g, "\n\n");
	plainText = plainText.trim();

	return plainText;
}
