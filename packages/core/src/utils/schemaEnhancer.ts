import { z } from "zod";

// Generated by Claude
/**
 * Enhances a Zod schema with descriptions
 */
export function enhanceSchemaWithDescriptionsInternal<T extends z.ZodTypeAny>(
	schema: T,
	descriptions: Record<string, string>,
	currentPath: string = "",
): T {
	const deepDescribe = () => {
		if (schema instanceof z.ZodObject) {
			const newShape = Object.fromEntries(
				Object.entries(schema.shape).map(([key, field]) => {
					const newPath = currentPath ? `${currentPath}.${key}` : key;
					const enhancedField = enhanceSchemaWithDescriptionsInternal(field as z.ZodTypeAny, descriptions, newPath);
					return [key, descriptions[newPath] ? enhancedField.describe(descriptions[newPath]) : enhancedField];
				}),
			);
			return z.object(newShape) as unknown as T;
		} else if (schema instanceof z.ZodArray) {
			const enhancedElement = enhanceSchemaWithDescriptionsInternal(schema.element, descriptions, `${currentPath}.[element]`);
			return z.array(enhancedElement) as unknown as T;
		} else if (schema instanceof z.ZodUnion) {
			const enhancedOptions = schema.options.map((option: z.ZodTypeAny) =>
				enhanceSchemaWithDescriptionsInternal(option, descriptions, currentPath),
			);
			return z.union(enhancedOptions as [z.ZodTypeAny, z.ZodTypeAny, ...z.ZodTypeAny[]]) as unknown as T;
		} else {
			// For primitive types or other Zod types
			return schema;
		}
	};

	const deepDescribed = deepDescribe();
	return descriptions[currentPath] ? deepDescribed.describe(descriptions[currentPath]) : deepDescribed;
}
