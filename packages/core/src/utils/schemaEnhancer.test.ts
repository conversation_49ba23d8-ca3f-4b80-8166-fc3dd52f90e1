import { z } from "zod";
import { enhanceSchemaWithDescriptionsInternal } from "./schemaEnhancer";
import { expect } from "@jest/globals"; // Assume this is the file containing our function
// Generated by <PERSON>

describe("enhanceSchemaWithDescriptionsInternal", () => {
	const descriptions = {
		"": "Person",
		name: "Full name of the person",
		age: "Age in years",
		"address.street": "Street address",
		"hobbies.[element]": "List of hobbies",
		"education.[element].degree": "Type of degree earned",
	};

	test("enhances a simple schema", () => {
		const schema = z.object({
			name: z.string(),
			age: z.number(),
		});

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, descriptions);

		expect(descriptions[""]).toBe("Person");
		expect(enhanced.description).toBe("Person");
		expect(enhanced.shape.name.description).toBe("Full name of the person");
		expect(enhanced.shape.age.description).toBe("Age in years");
	});

	test("enhances a nested object schema", () => {
		const schema = z.object({
			name: z.string(),
			address: z.object({
				street: z.string(),
				city: z.string(),
			}),
		});

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, descriptions);

		expect(enhanced.shape.name.description).toBe("Full name of the person");
		expect(enhanced.shape.address.shape.street.description).toBe("Street address");
		expect(enhanced.shape.address.shape.city.description).toBeUndefined();
	});

	test("enhances an array schema", () => {
		const schema = z.object({
			name: z.string(),
			hobbies: z.array(z.string()),
		});

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, descriptions);

		expect(enhanced.shape.name.description).toBe("Full name of the person");
		expect(enhanced.shape.hobbies.element.description).toBe("List of hobbies");
	});

	test("enhances a schema with array of objects", () => {
		const schema = z.object({
			name: z.string(),
			education: z.array(
				z.object({
					degree: z.string(),
					year: z.number(),
				}),
			),
		});

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, descriptions);

		expect(enhanced.shape.name.description).toBe("Full name of the person");
		expect(enhanced.shape.education.element.shape.degree.description).toBe("Type of degree earned");
		expect(enhanced.shape.education.element.shape.year.description).toBeUndefined();
	});

	test("enhances a schema with union type", () => {
		const schema = z.object({
			name: z.string(),
			status: z.union([z.literal("active"), z.literal("inactive")]),
		});

		const descriptionsWithUnion = {
			...descriptions,
			status: "Current status of the person",
		};

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, descriptionsWithUnion);

		expect(enhanced.shape.name.description).toBe("Full name of the person");
		expect(enhanced.shape.status.description).toBe("Current status of the person");
	});

	test("does not modify schema without matching descriptions", () => {
		const schema = z.object({
			name: z.string(),
			email: z.string().email(),
		});

		const enhanced = enhanceSchemaWithDescriptionsInternal(schema, {});

		expect(enhanced.shape.name.description).toBeUndefined();
		expect(enhanced.shape.email.description).toBeUndefined();
	});
});
