You will be shown a production efficiency report for a pig farm. Your task is to identify top farm issues in the latest period ({{latestPeriodName}}) based on the report's data. Focus on KPIs with goals.

You will analyze the issues in two steps:
1. Issue hierarchy
2. Unexplained consequential issues
3. Top issues

1. Issue hierarchy:
- Each node in the hierarchy is an identified issue.
- Group issues that target the same problem using different metrics.
- Exclude issues whose KPI values are within the goal.
- Use causes and consequences to construct relations between nodes in the hierarchy. Start with root issues that have no causes and end with issues without consequences. Explain these relations.

2. Unexplained consequential issues
- Create a list of consequential (non-root) issues from issue hierarchy that can't be fully explained by identified root issues.
- Its OK if this list is empty.

3. Top issues:
- Only include root issues from the issue hierarchy and unexplained consequential issues. Do NOT include consequential issues unless they are in the "Unexplained consequential issues" list. Do NOT include any other issues.
- Exclude issues without a significant negative financial impact.
- List the issues in order of the highest negative financial impact.
- Maximum 5 issues.

Provide the following bullet point for each issue in the top issues:
- A) List of KPI codes of all indicating KPIs related to the issue.
- B) Explain whether it is a root issue from the hierarchy or unexplained consequential issue.
- C) {{IdentifiedIssueDescription}}

{{#if holdingInstructions}}
{{holdingInstructions}}
{{/if}}

{{#if SOPs}}
The farm has a set of Standard Operating Procedures (SOPs). Consider them when identifying and prioritizing issues.
Standard Operating Procedures (SOPs):
{{SOPs}}
End of SOPs.
{{/if}}

{{#if segesManual}}
There is a SEGES manual for running a pig farm. Use it to identify and prioritize issues.
SEGES manual:
{{segesManual}}
End of SEGES manual.
{{/if}}

{{#if southWestVetsSOPs}}
There are South West Vets SOPs for running a pig farm. Use it to identify and prioritize issues.
South West Vets SOPs:
{{southWestVetsSOPs}}
End of South West Vets SOPs.
{{/if}}

The report:
{{reportText}}
