Several issues have been identified on the farm. Your goal is to determine the root cause of one of the issues based on collected data points. The result this analysis will be used to determine what actions are need to resolve the issue.

Write detailed explanation of how you used the data points to determine the cause mentioned in the first section. Make sure all relevant data points are considered. Make sure there are no assumptions and everything is confirmed by the data. Consider all likely causes of the issue.
If the exact cause can't be confirmed by the data, suggest what needs to be done to find the actual cause. Is it necessary to call a veterinary to the farm to determine the exact cause?
There might be some errors in data recording. If any of the data points relevant to this analysis show signs of being incorrect, mention it.

{{#if holdingInstructions}}
{{holdingInstructions}}
{{/if}}

The issue to focus on:
{{issue.title}}
{{issue.description}}

Other issues:
{{#each otherIssues}}
{{this.title}}
{{this.description}}

{{/each}}
If issues in "Other issues" are contributing as causes to the focused issue, acknowledge them and focus on other causes.

{{#if SOPs}}
The holding has a set of Standard Operating Procedures (SOPs). It might contain useful information to analyze the cause.
Standard Operating Procedures (SOPs):
{{SOPs}}
End of SOPs.

Farm specific KPI goals in "Data points" section override goals/targets from SOPs. If you mention a goal/target from an SOP, specify the name of the SOP.
{{/if}}

{{#if segesManual}}
There is a SEGES manual for running a pig farm. It might contain useful information to analyze the cause.
SEGES manual:
{{segesManual}}
End of SEGES manual.

Farm specific KPI goals in "Data points" section override goals/targets from SEGES. If you mention a goal/target from an SEGES, specify where it comes from.
{{/if}}

Data points:

{{reportText}}
End of Efficiency Report.

{{#if additionalKpis}}
Additional KPIs  that might be relevant have been calculated:
{{additionalKpis}}
End of additional KPIs.
{{/if}}

{{#if medicineUsages}}
The following section describes the medicine usage amount on the farm during the efficiency report periods.
{{medicineUsages}}
End of the medicine usage section.
{{/if}}

{{#if pregnancyFailures}}
The following section describes the pregnancy failures on the farm during the {{latestPeriodName}} period:
{{pregnancyFailures}}
End of the pregnancy failures section.
{{/if}}