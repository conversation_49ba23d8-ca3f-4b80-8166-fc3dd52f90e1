import * as fs from "fs";
import path from "path";

/**
 * Load fallback prompt.
 * The prompt must be a ${promptType}.hbs file that must be present in the directory, where getFallbackPrompt function is defined.
 */
export function getFallbackPrompt(promptType: string): string {
	if (promptType.indexOf(".") !== -1) {
		throw new Error("Prompt types must not contain dots (.) to avoid possible path traversal attacks. Prompt type: " + promptType);
	}
	try {
		return fs.readFileSync(path.resolve(__dirname, promptType + ".hbs"), "utf-8");
	} catch (e) {
		throw new Error(`Failed to load fallback prompt type ${promptType}`);
	}
}
