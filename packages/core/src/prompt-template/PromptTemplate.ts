import { sql } from "../database";
import { SYSTEM_ID } from "../Constants";
import { getFallbackPrompt } from "./fallback/FallbackPromptTemplates";
import Handlebars from "handlebars";
import { match, P } from "ts-pattern";
import { HoldingId } from "../HoldingId";
import { z } from "zod";
import { PigbotHandlebarsOptions, registerUniversalHelpers, renderHandlebarTemplate } from "../utils/handlebarsUtils";
import logger from "../logger";

export type TemplateText = {
	templateText: string;
};

type TemplateId = string;

type Override = TemplateId | TemplateText | null;

export type TemplateOverrides = Record<string, Override>;

/**
 * A prompt id is a unique identifier for a prompt template.
 * If holding id is specified, then holding level template is loaded if it exists.
 * If the template is not found on the holding level, the system level template and then fallback is loaded.
 * If id is specified, it is loaded directly.
 * If id is null, then fallback template is looked up using promptType.
 */
export type PromptId =
	| {
			promptType: string;
			holdingId: HoldingId;
			templateOverrides?: TemplateOverrides;
	  }
	| { promptType: string; id: string | null };

type PromptTemplate = {
	id: string;
	type: string;
	root_id: number;
	template: string;
	created_at: Date;
	created_by: number;
	updated_at: Date;
	updated_by: number;
};

export type PromptTemplateData = {
	template: string;
	id: string | null;
};

// Make sure to register all universal handlebars helpers before using them
registerUniversalHelpers();

/**
 * Get the prompt template for a given prompt id.
 * @param promptId Either type and holdingId or concrete id.
 * @param optional If true, returns null if the template is not found. Otherwise, returns a fallback template (or error if it doesn't exist).
 * @template T Type magic to return null if optional is true, otherwise return PromptTemplateData.
 */
export async function getPromptTemplate<T extends boolean>(
	promptId: PromptId,
	optional: T,
): Promise<T extends true ? PromptTemplateData | null : PromptTemplateData> {
	async function getPromptById(id: string) {
		const [template] = await sql<[PromptTemplate]>`SELECT *
																									 FROM prompt_template
																									 WHERE id = ${id}`;
		return template;
	}

	function getFallbackTemplate() {
		return optional ? null : { id: null, template: getFallbackPrompt(promptId.promptType) };
	}

	const template = await match(promptId)
		.with(
			{
				holdingId: P.any,
			},
			async ({ promptType, holdingId, templateOverrides }) => {
				if (templateOverrides && templateOverrides[promptType] !== undefined) {
					const override = templateOverrides[promptType];
					return match(override)
						.with({ templateText: P.string }, ({ templateText }) => ({ id: null, template: templateText }))
						.with(P.string, async (overrideId) => await getPromptById(overrideId))
						.with(null, () => getFallbackTemplate())
						.exhaustive();
				}

				const templates = await sql<[PromptTemplate]>`
					SELECT pt.*
					FROM active_template
								 JOIN prompt_template pt on pt.id = active_template.template_id
					WHERE active_template.type = ${promptType}
						AND (active_template.root_id = ${SYSTEM_ID}
						OR active_template.root_id = ${holdingId.rootId})
					ORDER BY active_template.root_id DESC
					LIMIT 1`;

				if (templates.length > 0) {
					return templates[0];
				} else {
					return getFallbackTemplate();
				}
			},
		)
		.with({ id: P.string }, async ({ id }) => {
			return await getPromptById(id);
		})
		.with({ id: null }, () => getFallbackTemplate())
		.exhaustive();

	return template as T extends true ? PromptTemplateData | null : PromptTemplateData;
}

export type RenderedPromptTemplate = {
	template: PromptTemplateData;
	context: Record<string, unknown>;
	content: string;
};

/**
 * Renders the prompt template with context. Prompt template uses resolution described in [PromptId].
 */
export async function renderPromptTemplate<T extends boolean = false>({
	promptId,
	context = {},
	optional = false as T,
}: {
	promptId: PromptId;
	context?: Record<string, unknown>;
	optional?: T;
}): Promise<T extends true ? RenderedPromptTemplate | null : RenderedPromptTemplate> {
	const template = await getPromptTemplate(promptId, optional);

	if (template) {
		try {
			const content = Handlebars.compile(template.template, PigbotHandlebarsOptions)(context);

			return {
				template,
				context,
				content,
			};
		} catch (e) {
			throw Object.assign(
				new Error(
					`Failed to render prompt template ${JSON.stringify(promptId)}: ${(e as Error).message}\n${template.template}\nContext:\n${JSON.stringify(context, null, 2)}`,
				),
				{
					cause: e,
				},
			);
		}
	} else {
		return null as T extends true ? RenderedPromptTemplate | null : RenderedPromptTemplate;
	}
}

/**
 * Returns the schema with descriptions.
 * The descriptions can be overridden using prompt management and use context.
 */
export async function enhanceSchemaWithDescriptions<T extends z.ZodTypeAny>(
	schema: T,
	templateType: string,
	templateOverrides: TemplateOverrides,
	holdingId: HoldingId,
	context: Record<string, unknown> = {},
): Promise<T> {
	const prefix = `${templateType}/response/`;

	// 1. Get descriptions from overrides
	const descriptionOverrides = Object.entries(templateOverrides).reduce(
		(acc, [key, id]) => {
			if (key.startsWith(prefix) && id !== null) {
				const promptType = key.replace(prefix, "");
				acc[promptType] = id;
			}
			return acc;
		},
		{} as Record<string, Override>,
	);

	// Process overrides into actual description text
	const overrideDescriptions = Object.fromEntries(
		await Promise.all(
			Object.entries(descriptionOverrides).map(async ([promptType, override]) => {
				const template = await match(override)
					.with({ templateText: P.string }, ({ templateText }) => templateText)
					.otherwise(async (id) => {
						const rendered = await renderPromptTemplate({
							promptId: { promptType, id },
							context,
							optional: false,
						});
						return rendered.content;
					});

				return [promptType, template];
			}),
		),
	);

	async function doEnhance<T extends z.ZodTypeAny>(schema: T, prefix: string, currentPath: string = ""): Promise<T> {
		const getDescription = async (path: string): Promise<string | undefined> => {
			// 1. Try to get from override
			if (overrideDescriptions[path]) {
				return overrideDescriptions[path];
			}

			// 2. Try to get from database using getPromptTemplate

			const promptType = `${prefix}${path}`;
			try {
				const rendered = await renderPromptTemplate({
					promptId: { promptType, holdingId },
					context,
					optional: true,
				});
				if (rendered) {
					return rendered.content;
				}
			} catch (error) {
				// Continue to the next option if fetching from DB fails
				logger.error(`Failed to load description from DB for ${path}:`, error);
			}

			// 3. Use existing description from zod schema
			if (schema.description) {
				return renderHandlebarTemplate(schema.description, context);
			} else {
				return undefined;
			}
		};

		const deepDescribe = async () => {
			if (schema instanceof z.ZodObject) {
				const entries = await Promise.all(
					Object.entries(schema.shape).map(async ([key, field]) => {
						const newPath = currentPath ? `${currentPath}.${key}` : key;
						const enhancedField = await doEnhance(field as z.ZodTypeAny, prefix, newPath);

						const description = await getDescription(newPath);
						return [key, description ? enhancedField.describe(description) : enhancedField];
					}),
				);
				return z.object(Object.fromEntries(entries)) as unknown as T;
			} else if (schema instanceof z.ZodArray) {
				const arrayPath = `${currentPath}.[element]`;
				const enhancedElement = await doEnhance(schema.element, prefix, arrayPath);
				return z.array(enhancedElement) as unknown as T;
			} else if (schema instanceof z.ZodUnion) {
				const enhancedOptions = await Promise.all(schema.options.map((option: z.ZodTypeAny) => doEnhance(option, prefix, currentPath)));
				return z.union(enhancedOptions as [z.ZodTypeAny, z.ZodTypeAny, ...z.ZodTypeAny[]]) as unknown as T;
			} else {
				// For primitive types or other Zod types
				return schema;
			}
		};

		const deepDescribed = await deepDescribe();
		const rootDescription = await getDescription(currentPath);
		return rootDescription ? deepDescribed.describe(rootDescription) : deepDescribed;
	}

	return doEnhance(schema, prefix, "");
}
