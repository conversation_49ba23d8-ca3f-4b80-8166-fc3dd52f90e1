import { BarSeriesOption, EChartsOption, LineSeriesOption } from "echarts";
import { jsonrepair } from "jsonrepair";
import type { PieChartParams, StackedBarChartParams, StackedLineGraphParams } from "./GraphSchemas";

const colors = [
	"#5470C6", // Blue
	"#91CC75", // Green
	"#FF9F7F", // Coral
	"#FFBB6B", // Orange
	"#73C0DE", // Light Blue
	"#E062AE", // Pink
	"#7D3C98", // Purple
	"#52BE80", // Emerald
	"#F7DC6F", // Yellow
	"#E74C3C", // Red
];

function getTitle(params: { title: string }) {
	return {
		text: params.title,
		textStyle: {
			fontSize: 14,
		},
		left: "center",
	};
}

/**
 * Converts StackedLineGraphParams to ECharts option format using markLine for goals
 */
export const convertStackedLineGraphToECharts = (params: StackedLineGraphParams): EChartsOption => {
	const { yAxisName, yAxisUnit, series, periodNames } = params;

	const dataYMin = Math.min(...series.map((s) => Math.min(...s.data, s.goal ?? Infinity)));
	const dataYMax = Math.max(...series.map((s) => Math.max(...s.data, s.goal ?? -Infinity)));
	const range = dataYMax - dataYMin == 0 ? 1 : dataYMax - dataYMin; //If range is zero, we would divide by zero, so we set it to 1
	const exponent = Math.floor(Math.log10(range));
	const roundingFactor = Math.pow(10, -exponent);
	const axisYMinComputed = Math.floor((dataYMin - Math.max(range / 5, 0)) * roundingFactor) / roundingFactor;
	const axisYMin = dataYMin >= 0 ? Math.max(axisYMinComputed, 0) : axisYMinComputed;
	const axisYMax = Math.ceil((dataYMax + Math.max(range / 5, 0)) * roundingFactor) / roundingFactor;

	const eChartSeries = series.map((s, index) => {
		const seriesItem: LineSeriesOption = {
			name: s.title,
			type: "line",
			data: s.data,
			smooth: false,
			symbol: "circle",
			symbolSize: 8,
			emphasis: {
				focus: "series",
			},
			color: colors[index % colors.length],
			markLine:
				s.goal !== undefined && s.goal !== null
					? {
							silent: true,
							label: {
								formatter: `Goal: ${s.goal}`,
							},
							lineStyle: {
								type: "dashed",
								width: 2,
								color: colors[index % colors.length],
							},
							data: [
								{
									yAxis: s.goal,
									name: "Goal",
								},
							],
						}
					: undefined,
		};
		return seriesItem;
	});

	const seriesWithGoal = series.filter((s) => s.goal !== undefined && s.goal !== null);

	return {
		title: getTitle(params),
		tooltip: {
			trigger: "axis",
			// using any, because of really complicated types in echart.
			// tried to use CallbackDataParams | CallbackDataParams[], but it does not know about axisValueLabel.
			// tried TooltipCallbackDataParams, but it does not know other params.
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			formatter: function (params: any) {
				let result = `${params[0].axisValueLabel}<br/>`;
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				params.forEach((param: any) => {
					if (param.seriesType === "line") {
						result += `${param.marker} ${param.seriesName}: ${param.value}${yAxisUnit}<br/>`;
					}
				});

				if (seriesWithGoal.length > 0) {
					series.forEach((s, index) => {
						if (s.goal !== undefined && s.goal !== null) {
							result += `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${colors[index % colors.length]};"></span>`;
							result += `Goal ${s.title}: ${s.goal}<br/>`;
						}
					});
				}

				return result;
			},
		},
		legend: {
			data: eChartSeries.map((s) => s.name as string).filter((a) => a !== undefined),
			orient: "horizontal",
			bottom: 10,
		},
		/*		grid: {
			left: "5%",
			right: seriesWithGoal.length > 0 ? "14%" : "3%",
			bottom: "15%",
			top: "15%",
			containLabel: true,
		},*/
		xAxis: {
			type: "category",
			boundaryGap: false,
			data: periodNames,
		},
		yAxis: {
			type: "value",
			name: yAxisName + ` [${yAxisUnit}]`,
			min: axisYMin,
			max: axisYMax,
			nameLocation: "middle",
			nameGap: 40,
			axisLabel: {
				formatter: `{value}`,
			},
		},
		series: eChartSeries,
	};
};

export const convertPieChartToECharts = (params: PieChartParams): EChartsOption => {
	// Calculate total value for percentage calculations
	const totalValue = params.series.reduce((sum, item) => sum + item.value, 0);
	const groupingThreshold = totalValue * 0.05;

	// Sort series by value in descending order
	const sortedSeries = [...params.series].sort((a, b) => b.value - a.value);

	// Group small slices into "Other" category
	const processedSeries: Array<{ name: string; value: number }> = [];
	let otherValue = 0;
	const otherItems: Array<{ name: string; value: number }> = [];

	// Find the cutoff point where remaining slices should be grouped
	let cumulativeSmallValue = 0;
	let cutoffIndex = sortedSeries.length;

	// Work backwards from smallest to find slices that together make ≤5%
	for (let i = sortedSeries.length - 1; i >= 0; i--) {
		const potentialCumulative = cumulativeSmallValue + sortedSeries[i].value;
		if (potentialCumulative <= groupingThreshold) {
			cumulativeSmallValue = potentialCumulative;
			cutoffIndex = i;
		} else {
			break;
		}
	}

	// Only group if we have at least 2 slices to group and they're actually small
	const shouldGroup = cutoffIndex < sortedSeries.length - 1;

	if (shouldGroup) {
		// Add the larger slices
		for (let i = 0; i < cutoffIndex; i++) {
			processedSeries.push(sortedSeries[i]);
		}

		// Calculate "Other" value and collect items
		for (let i = cutoffIndex; i < sortedSeries.length; i++) {
			otherValue += sortedSeries[i].value;
			otherItems.push(sortedSeries[i]);
		}

		// Add "Other" slice if it has value
		if (otherValue > 0) {
			processedSeries.push({ name: "Other", value: Math.round(otherValue * 100) / 100 });
		}
	} else {
		// No grouping needed, use original series
		processedSeries.push(...sortedSeries);
	}

	return {
		title: getTitle(params),
		tooltip: {
			trigger: "item",

			formatter: (function (otherItemsCapture, seriesUnits) {
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				return function (params: any) {
					const unitsText = seriesUnits === undefined ? "" : " " + seriesUnits;
					const baseTooltip = `${params.name}: ${params.value}${unitsText}`;

					// If this is the "Other" slice and we have items grouped, show the breakdown
					if (params.name === "Other" && otherItemsCapture.length > 0) {
						const breakdown = otherItemsCapture.map((item) => `  • ${item.name}: ${item.value}${unitsText}`).join("<br/>");
						return `${baseTooltip}<br/><br/>Includes:<br/>${breakdown}`;
					}

					return baseTooltip;
				};
			})(otherItems, params.seriesUnits),
		},
		legend: {
			orient: "horizontal",
			bottom: 10,
			data: processedSeries.map((item) => item.name),
		},
		series: [
			{
				type: "pie",
				radius: "50%",
				data: processedSeries.map((item, index) => ({
					...item,
					itemStyle: {
						color: colors[index % colors.length],
					},
				})),
				emphasis: {
					itemStyle: {
						shadowBlur: 10,
						shadowOffsetX: 0,
						shadowColor: "rgba(0, 0, 0, 0.5)",
					},
				},
				label: {
					formatter: `{b} ({c}${params.seriesUnits === undefined ? "" : " " + params.seriesUnits})`,
				},
			},
		],
	};
};

export const convertStackedBarChartToECharts = (params: StackedBarChartParams): EChartsOption => {
	const { yAxisName, yAxisUnit, series, periodNames } = params;

	const eChartSeries = series.map(
		(s, index) =>
			({
				name: s.title,
				type: "bar",
				stack: "total",
				emphasis: {
					focus: "series",
				},
				data: s.data,
				color: colors[index % colors.length],
			}) as BarSeriesOption,
	);

	return {
		title: getTitle(params),
		tooltip: {
			trigger: "axis",
			axisPointer: {
				type: "shadow",
			},
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			formatter: (params: any) => {
				let total = 0;
				let result = `${params[0].axisValueLabel}<br/>`;
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				params.forEach((param: any) => {
					total += param.value;
					result += `${param.marker} ${param.seriesName}: ${param.value}${yAxisUnit}<br/>`;
				});
				result += `<br/><b>Total: ${total}${yAxisUnit}</b>`;
				return result;
			},
		},
		legend: {
			data: series.map((s) => s.title),
			orient: "horizontal",
			bottom: 10,
		},
		...(series.length >= 6 && {
			grid: {
				bottom: "25%",
			},
		}),
		xAxis: {
			type: "category",
			data: periodNames,
		},
		yAxis: {
			type: "value",
			name: yAxisName + ` [${yAxisUnit}]`,
			nameLocation: "middle",
			nameGap: 40,
			axisLabel: {
				formatter: `{value}`,
			},
		},
		series: eChartSeries,
	};
};

export function convertJsonToGraphOpt(type: string, text: string) {
	let convertedOptions: EChartsOption;
	let explanation: string;
	let title: string;
	let height = 300;

	// LLM sometimes returns broken json. This attempts to fix it.
	const options = JSON.parse(jsonrepair(text));

	try {
		if (type === "vfastackedlinegraph") {
			explanation = options.explanation;
			title = options.title;
			convertedOptions = convertStackedLineGraphToECharts(options);
			if (convertedOptions.series && (!Array.isArray(convertedOptions.series) || convertedOptions.series.length === 1)) {
				height = 250;
			}
		} else if (type === "piechart") {
			explanation = options.explanation;
			title = options.title;
			convertedOptions = convertPieChartToECharts(options);
		} else if (type === "stackedbarchart") {
			convertedOptions = convertStackedBarChartToECharts(options);
			explanation = options.explanation;
			title = options.title;
		} else {
			throw new Error("AI has generated not recognized code block");
		}
	} catch (e) {
		throw new Error("Error generating graph to ECharts: \nGraph type: " + type + "\nGraph text: " + text, { cause: e });
	}

	return { title, convertedOptions, explanation, height };
}
