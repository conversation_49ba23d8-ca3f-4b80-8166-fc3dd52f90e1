import { UserContext } from "../UserContext";
import { match } from "ts-pattern";
import { MessageChunk, Usage } from "../llm/LlmCommon";

export type ResponseId = {
	type: "responseId";
	responseId: string;
};
/**
 * Chunks of streamed response and responseId.
 */
export type StreamedResponseWithIdChunk = MessageChunk | Usage | ResponseId;

/**
 * Removes chunks that are not authorized for the provided role.
 */
export function stripUnauthorizedChunksFromStream(
	chunk: StreamedResponseWithIdChunk,
	stripForRole: UserContext["role"] | false,
): StreamedResponseWithIdChunk[] {
	return match(chunk)
		.with({ type: "usage" }, (chunk) => (!stripForRole || stripForRole === "backoffice" ? [chunk] : []))
		.otherwise((chunk) => [chunk]);
}
