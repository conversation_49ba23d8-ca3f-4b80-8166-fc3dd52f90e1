// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace EffReportPrompts {
	const prefix = "eff-report-35"; // 35 == Version 3.5
	const holdingInstructions = `holding-instructions`;
	export const summary = `${prefix}/summary-links`;
	export const summary_holdingInstructions = `${summary}/${holdingInstructions}`;

	export const issueIdentificationFolder = `${prefix}/issue-identification`;
	export const issueIdentification_1_issue_analysis = `${issueIdentificationFolder}/1-issue-analysis`;
	export const issueIdentification_holdingInstructions = `${issueIdentificationFolder}/${holdingInstructions}`;
	export const issueIdentification_2_top_issues = `${issueIdentificationFolder}/2-top-issues`;

	export const overview = `${prefix}/overview`;

	const causeAnalysisFolder = `${prefix}/cause-analysis`;
	export const causeAnalysis_1_initialAnalysis = `${causeAnalysisFolder}/1-initial-analysis`;
	export const causeAnalysis_2_review = `${causeAnalysisFolder}/2-review`;
	export const causeAnalysis_3_final = `${causeAnalysisFolder}/3-final`;
	export const causeAnalysis_holdingInstructions = `${causeAnalysisFolder}/${holdingInstructions}`;
}
