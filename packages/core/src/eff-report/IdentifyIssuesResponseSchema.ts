import { z } from "zod";

const IdentifiedIssueSchema = z.object({
	title: z.string().describe("Short title of the issue that focuses on problematic KPIs"),
	description: z.string().describe(
		`Concise summary that includes: 
			- Description of the issue. Don't suggest improvements. Include values and (if available) goals/targets of indicating KPIs.
			- Mention issues that are happening as a consequence of this issue.
			- Mention financial impact on the farm. Don't estimate costs.
		
		Consider the following in the summary:
			- Don't explain why this issue is happening, what is causing it, what contributes to it, or what it suggests.			 
			- Don't use the word analysis.
			- Reference to KPIs using their names, not codes.						  			 
			- Format the text in markdown. It must be a single block of text without lists or paragraphs. Highlight bits of crucial information in bold. Never highlight entire block of text.`,
	),
	summary: z.string().describe("Summary of the issue must be a single sentence and written in an easy-to-understand way."),
	indicatingKPIs: z
		.array(
			z.object({
				code: z.string().describe("KPI code"),
			}),
		)
		.describe("List of KPI codes of all indicating KPIs related to the issue"),
});

export type IdentifiedIssue = z.infer<typeof IdentifiedIssueSchema>;

/**
 * Structure of identify issues LLM response
 */
export const IdentifyIssuesResponseSchema = z.object({
	issues: z.array(IdentifiedIssueSchema).describe("List of issues from the top issues section."),
});

export type IdentifyIssuesResponse = z.infer<typeof IdentifyIssuesResponseSchema>;
