import { HoldingId, SystemHoldingId } from "../HoldingId";
import { FarmInfo } from "../cf-link/GetFarmInfo";
import { renderPromptTemplate, TemplateOverrides } from "../prompt-template/PromptTemplate";
import { IntroductionPrompts } from "../introduction/IntroductionPrompts";
import { renderTemplateFile } from "../utils/renderTemplateFile";
import path from "path";

export async function getIntroduction(params: {
	language: string;
	holdingId: HoldingId;
	farmInfo: FarmInfo;
	templateOverrides: TemplateOverrides;
}) {
	const holdingInstructions = (
		await renderPromptTemplate({
			promptId: {
				promptType: IntroductionPrompts.prompt_holdingInstructions,
				holdingId: params.holdingId,
				templateOverrides: params.templateOverrides,
			},
			optional: true,
		})
	)?.template.template;

	return await renderPromptTemplate({
		promptId: {
			promptType: IntroductionPrompts.prompt,
			holdingId: SystemHoldingId,
			templateOverrides: params.templateOverrides,
		},
		context: {
			country: params.farmInfo.countryCode,
			language: params.language,
			settings: params.farmInfo.settings,
			farmName: params.farmInfo.farmName,
			farmTimeline:
				params.farmInfo.farmTimeline.length > 0
					? renderTemplateFile(path.resolve(__dirname, "farmTimelineToText.hbs"), params.farmInfo)
					: null,
			holdingInstructions,
		},
	});
}
