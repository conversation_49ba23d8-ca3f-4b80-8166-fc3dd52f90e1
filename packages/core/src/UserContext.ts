import { z } from "zod";

// Sorted by the order of least to most permissions
const roles = ["user", "power-user", "backoffice"] as const;

// TODO use this schema directly once pigman is deployed
const internalSchema = z.object({
	userId: z.coerce.number().int().positive(),
	name: z.string(),
	rootId: z.coerce.number().int().positive(),
	role: z.enum(roles),
	farmId: z.coerce.number().int().positive(),
	farmName: z.string(),
	countryCode: z.string(),
	rootName: z.string(),
	env: z.string().optional(),
});

export const userContextSchema = internalSchema.transform((data) => ({
	...data,
	env: data.env ?? guessEnvFromRootId(data.rootId), // workaround for missing env in old pigman
}));

/**
 * RootIds on AM server that have id < 100k
 */
const HoldingsInAM = new Set([
	684, 792, 1604, 3083, 5045, 5110, 5376, 6616, 7416, 7844, 8074, 8080, 8245, 8351, 8716, 8793, 8973, 8975, 9007, 9009, 9012, 9014, 9016,
	9018, 9020, 9023, 9025, 9027,
]);

/**
 * Function that tries to guess env from rootId
 * @param rootId
 */
export function guessEnvFromRootId(rootId: number) {
	return rootId >= 100000 || HoldingsInAM.has(rootId) ? "prod-am" : "prod-pigs";
}

export type UserContext = z.infer<typeof userContextSchema>;

const userContextSchemaOptional = internalSchema.partial();
/**
 * All fields are optional
 */
export type UserContextOptional = z.infer<typeof userContextSchemaOptional>;

export function hasAtLeast(role: UserContext["role"], user: UserContext["role"] | UserContext) {
	const userRoleIdx = typeof user === "string" ? roles.indexOf(user) : roles.indexOf(user.role);
	return roles.indexOf(role) <= userRoleIdx;
}
