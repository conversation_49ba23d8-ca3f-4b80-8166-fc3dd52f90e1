import OpenAI from "openai";
import { ChatCompletionMessageParam } from "openai/resources";
import { cachePromise, hash } from "../Cache";
import { get_encoding } from "tiktoken";
import { match, P } from "ts-pattern";
import { RateLimiterQueue, RateLimiterRedis } from "rate-limiter-flexible";
import { sharedRedisClient } from "../RedisClient";
import Config from "../Config";
import * as fs from "node:fs";
import sizeOf from "image-size";
import { z } from "zod";
import { zodResponseFormat } from "openai/helpers/zod";
import { MetricData, saveCostMetric } from "../cost-metrics";
import * as CompletionsAPI from "openai/src/resources/completions";
import path from "path";
import logger from "../logger";
import * as ChatAPI from "openai/src/resources/chat/chat";
import { AssistantUpdateParams } from "openai/src/resources/beta/assistants";
import { ResponseCacheEnabled, ResponseHash, ResponseWithUsage, StreamedResponseChunk, Usage } from "./LlmCommon";

const openai = new OpenAI({
	apiKey: Config.OPENAI_API_KEY,
});

export const encoderGpt4 = get_encoding("cl100k_base"); // This is what gpt-4 uses: https://cookbook.openai.com/examples/how_to_count_tokens_with_tiktoken

// OpenAI Tokens Per Minute limits https://platform.openai.com/docs/guides/rate-limits?context=tier-one
const limiterFlexible = new RateLimiterRedis({
	points: Config.CHATGPT4O_TPM,
	duration: 60,
	storeClient: sharedRedisClient,
	// Each environment has its own rate limiting
	// Their sum mustn't exceed the global limit
	keyPrefix: `chatgpt_${Config.NODE_ENV}`,
});

const limiterQueue = new RateLimiterQueue(limiterFlexible);

const GPT4o: ChatAPI.ChatModel = "gpt-4o-2024-11-20";

async function rateLimiting(messages: Array<ChatCompletionMessageParam>, maxToken: number) {
	const tokensCount = getTokensCount(messages, maxToken);

	// Wait until rate limiting allows us to send the request
	await limiterQueue.removeTokens(tokensCount);
}

function getTokensCount(messages: Array<ChatCompletionMessageParam>, maxToken: number) {
	return messages.reduce<number>((acc, message) => {
		const tokensCount = match(message.content)
			.with(P.string, (m) => encoderGpt4.encode(m).length)
			.with(P.array(P.any), (arr) =>
				arr.reduce((sum, item) => {
					return (
						sum +
						match(item)
							.with({ type: "text" }, (item) => encoderGpt4.encode(item.text).length)
							.with({ type: "image_url" }, (item) => estimateBase64Image(item.image_url.url))
							.run()
					);
				}, 0),
			)
			.otherwise(() => 0);
		return acc + tokensCount;
	}, maxToken);
}

function getUsage(metricData: MetricData, usage: CompletionsAPI.CompletionUsage): Usage {
	const cachedTokens = usage.prompt_tokens_details?.cached_tokens ?? 0;
	return {
		type: "usage",
		metricData: metricData,
		inputCost: ((usage.prompt_tokens - cachedTokens) * 2.5 + cachedTokens * 1.25) / 1000000,
		outputCost: (usage.completion_tokens * 10) / 1000000,
		inputTokens: usage.prompt_tokens,
		cachedTokens,
		outputTokens: usage.completion_tokens,
	};
}

/**
 * Convert the messages to an array if it's not already.
 * OpenAI API requires the array.
 */
function toMessagesArray(
	messages:
		| {
				systemMsg?: string;
				userMsg: string;
		  }
		| Array<ChatCompletionMessageParam>,
): Array<ChatCompletionMessageParam> {
	return Array.isArray(messages)
		? messages
		: [
				...(messages.systemMsg
					? [
							{
								role: "system",
								content: messages.systemMsg,
							} as const,
						]
					: []),
				{
					role: "user",
					content: messages.userMsg,
				},
			];
}

/**
 * Parameters for calling ChatGPT's completion
 */
export type ChatGPTCompletionParams = {
	messages:
		| ChatCompletionMessageParam[]
		| {
				systemMsg?: string;
				userMsg: string;
		  };
	metricData: MetricData;
	cacheIdx?: number;
	maxToken?: number;
};

export const ChatGPTMaxTokens = 16384;
export const DefaultMaxTokens = ChatGPTMaxTokens; // Max tokens in a response

export async function* streamCompletionChatGpt({ cacheIdx, messages, metricData, maxToken = DefaultMaxTokens }: ChatGPTCompletionParams) {
	const messagesArray = toMessagesArray(messages);

	const cache = await cachePromise;
	const hashKey = ResponseCacheEnabled && cacheIdx !== undefined ? `s-${cacheIdx}-${hash([messagesArray, maxToken])}-v4` : undefined;

	const cachedResponse = hashKey ? await cache.get<StreamedResponseChunk[]>(hashKey) : undefined;
	if (cachedResponse) {
		for (const chunk of cachedResponse) {
			yield chunk;
		}
	} else {
		await rateLimiting(messagesArray, maxToken);

		const stream = await openai.chat.completions.create({
			messages: messagesArray,
			model: GPT4o,
			stream: true,
			stream_options: {
				include_usage: true,
			},
			max_tokens: maxToken,
		});

		const valueToCache: StreamedResponseChunk[] = [];

		for await (const chunk of stream) {
			if (chunk.choices.length > 0) {
				// Chunk contains content
				const [choice] = chunk.choices;
				const { content } = choice.delta;
				if (content) {
					valueToCache.push(content);
					yield content;
				}
			} else if (chunk.usage) {
				// Chunk contains usage
				const usage = getUsage(metricData, chunk.usage);
				saveCostMetric({ metricData, usage, model: GPT4o });
				valueToCache.push(usage);
				yield usage;
			}
		}

		const responseHash: ResponseHash = {
			type: "responseHash",
			hash: hash(valueToCache),
		} as const;

		yield responseHash;

		valueToCache.push(responseHash);

		if (hashKey) {
			await cache.set(hashKey, valueToCache, Config.COMPLETION_CACHE_TTL!);
		}
	}
}

export async function textCompletionChatGpt({
	messages,
	metricData,
	cacheIdx = 0,
	maxToken = DefaultMaxTokens,
	responseFinishedCheck,
}: ChatGPTCompletionParams & {
	responseFinishedCheck?: boolean;
}): Promise<ResponseWithUsage<string>> {
	const apiMessages = toMessagesArray(messages);

	const cache = await cachePromise;

	const model = GPT4o;

	const hashKey = ResponseCacheEnabled && cacheIdx !== undefined ? `tu-${cacheIdx}-${hash([apiMessages, maxToken, model])}` : undefined;
	if (hashKey) {
		const cachedResponse = await cache.get<ResponseWithUsage<string>>(hashKey);

		if (cachedResponse) {
			return cachedResponse;
		}
	}
	await rateLimiting(apiMessages, maxToken);

	let attemptsCount = 0;
	const MaxAttempts = 3;

	const responseFinishedText = "RESPONSE_FINISHED";

	// Make the API call
	let response;

	const lastMessage = apiMessages[apiMessages.length - 1];

	if (responseFinishedCheck) {
		const instruction = `\n\nWrite the following text at the end of your response: ${responseFinishedText}`;

		match(lastMessage.content)
			.with(P.string, (text) => {
				if (text.indexOf(instruction) === -1) {
					lastMessage.content = text + instruction;
				}
			})
			.otherwise(() => {
				throw new Error(`The last message must be a string if responseFinishedCheck is enabled: ${typeof lastMessage.content}`);
			});
	}

	while (response === undefined || attemptsCount >= MaxAttempts) {
		attemptsCount++;

		response = await openai.chat.completions.create({
			model: model,
			messages: apiMessages,
			max_tokens: maxToken,
		});

		// LLM sometimes cuts of the response. Adding this to determine if the response finished.
		if (responseFinishedCheck && !response.choices.every((choice) => choice.message.content?.includes(responseFinishedText) ?? true)) {
			logger.warn(`Text indicating finished response "${responseFinishedText}" was not found 
			in response "${response.choices[0].message.content?.substring(-100)}" 
			for prompt ${JSON.stringify(lastMessage.content).substring(0, 100)}`);

			response = undefined;
		}
	}

	if (!response) {
		throw Error(`Failed to find finished text "${responseFinishedText}" in ${MaxAttempts} attemts`);
	}

	const usage = response.usage ? getUsage(metricData, response.usage) : undefined;
	if (usage) {
		saveCostMetric({ metricData, model: model, usage: usage });
	} else {
		logger.warn("Usage not found in completion response");
	}

	// Return the generated answer
	const messageContent = response.choices[0].message.content!;

	const result = { response: responseFinishedCheck ? messageContent.replace(responseFinishedText, "").trim() : messageContent, usage };

	if (hashKey) {
		await cache.set(hashKey, result, Config.COMPLETION_CACHE_TTL!);
	}

	return result;
}

/**
 * Perform a structured completion using the ChatGPT model
 * @param zodObject Response schema
 * @param objectName Object name for the response
 * @param messages Messages to send to the model
 * @param metricData Metric data to save
 * @param cacheIdx Cache index. Set to undefined to disable caching. By default, the completion is cached with idx 0.
 * @param maxToken Maximum number of tokens to generate. Default is the maximum that ChatGPT can handle.
 * @param n How many responses to generate.
 * @type N Type magic to handle return type base on number of responses to generate (n),
 */
export async function structuredCompletionChatGpt<
	ZodInput extends z.ZodType,
	N extends number = 1,
	R = [N] extends [1] ? z.infer<ZodInput> : z.infer<ZodInput>[],
>({
	zodObject,
	objectName,
	messages,
	metricData,
	cacheIdx = 0,
	maxToken = DefaultMaxTokens,
	n = 1 as N,
}: {
	zodObject: ZodInput;
	objectName: string;
	n?: N;
} & ChatGPTCompletionParams): Promise<ResponseWithUsage<R>> {
	const responseFormat = zodResponseFormat(zodObject, objectName);

	const apiMessages = toMessagesArray(messages);

	const hashKey =
		ResponseCacheEnabled && cacheIdx !== undefined
			? `tu-${cacheIdx}-${hash([apiMessages, maxToken, n, responseFormat.json_schema])}-v2`
			: undefined;

	const cache = await cachePromise;

	if (hashKey) {
		const cachedResponse = await cache.get<ResponseWithUsage<R>>(hashKey);

		if (cachedResponse) {
			return cachedResponse;
		}
	}

	await rateLimiting(apiMessages, maxToken);

	// Make the API call
	const response = await openai.beta.chat.completions.parse({
		model: GPT4o,
		messages: apiMessages,
		max_tokens: maxToken,
		response_format: responseFormat,
		n,
	});

	const usage = response.usage ? getUsage(metricData, response.usage) : undefined;
	if (response.usage) {
		saveCostMetric({ metricData, model: GPT4o, usage: getUsage(metricData, response.usage) });
	} else {
		logger.warn("Usage not found in completion response");
	}

	const parsedMessages = response.choices.map((choice) => {
		const parsedMessage = choice.message.parsed;
		if (parsedMessage !== null) {
			return parsedMessage as z.infer<ZodInput>;
		} else {
			throw new Error(
				`Failed to parse the response:\n${JSON.stringify(response.choices, null, 2)}\nInput:\n${JSON.stringify(messages, null, 2)}`,
			);
		}
	});

	// Return the generated answer
	if (n === 1) {
		const parsedMessage = parsedMessages[0];

		const result = { response: parsedMessage, usage };
		if (hashKey) {
			await cache.set(hashKey, result, Config.COMPLETION_CACHE_TTL!);
		}

		return result;
	} else {
		const result = { response: parsedMessages as R, usage };
		if (hashKey) {
			await cache.set(hashKey, result, Config.COMPLETION_CACHE_TTL!);
		}
		return result;
	}
}

function estimateBase64Image(base64Image: string): number {
	// Remove the data URL prefix if present
	const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, "");

	// Convert base64 to buffer
	const buffer = Buffer.from(base64Data, "base64");

	// Get the image dimensions
	const dimensions = sizeOf(buffer);

	if (!dimensions.width || !dimensions.height) {
		throw new Error("Unable to determine image dimensions");
	}

	// Calculate the effective dimensions after potential resizing
	let effectiveWidth = dimensions.width;
	let effectiveHeight = dimensions.height;

	if (effectiveWidth > 1024 || effectiveHeight > 1024) {
		const aspectRatio = effectiveWidth / effectiveHeight;
		if (aspectRatio > 1) {
			// Image is wider than it is tall
			effectiveWidth = 1024;
			effectiveHeight = Math.round(1024 / aspectRatio);
		} else {
			// Image is taller than it is wide
			effectiveHeight = 1024;
			effectiveWidth = Math.round(1024 * aspectRatio);
		}
	}

	// Calculate the number of 512px squares
	const squaresWide = Math.ceil(effectiveWidth / 512);
	const squaresHigh = Math.ceil(effectiveHeight / 512);
	const totalSquares = squaresWide * squaresHigh;

	// Estimate tokens based on OpenAI's guidelines
	// Each 512px square is approximately 170 tokens
	// Add 85 tokens for overhead
	const estimatedTokens = totalSquares * 170 + 85;

	return estimatedTokens;
}

export async function visionCompletion(imagePath: string, prompt: string, metricData: MetricData, idx = 0): Promise<string> {
	// Read the image file and encode it to base64
	const imageBuffer = fs.readFileSync(imagePath);
	const base64Image = imageBuffer.toString("base64");

	// Prepare the messages for the API call
	const messages: ChatCompletionMessageParam[] = [
		{
			role: "user",
			content: [
				{ type: "text", text: prompt },
				{
					type: "image_url",
					image_url: {
						url: `data:image/png;base64,${base64Image}`,
					},
				},
			],
		},
	];

	return textCompletionChatGpt({ messages: messages, metricData: metricData, cacheIdx: idx, maxToken: ChatGPTMaxTokens }).then(
		(response) => response.response,
	);
}

export async function setupAssistVectorStore(assistantId: string, vectorStoreId: string) {
	await openai.beta.assistants.update(assistantId, {
		tool_resources: { file_search: { vector_store_ids: [vectorStoreId] } },
	});
}

export async function uploadText(vectorStoreId: string, fPath: string): Promise<{ fileId: string } | { error: string }> {
	const absolutePath = path.resolve(Config.WORKER_DIR, fPath);
	if (fs.existsSync(absolutePath)) {
		// Retry uploading the file 3 times. The upload sometimes fails on 404 (no idea why). The advice from internet was to retry.
		let errors = "";
		for (let attempt = 1; attempt <= 3; attempt++) {
			try {
				const result = await openai.beta.vectorStores.files.uploadAndPoll(vectorStoreId, fs.createReadStream(absolutePath));
				return { fileId: result.id };
			} catch (e) {
				errors += `Attempt ${attempt} on file ${absolutePath}: ${e}\n`;
			}
		}
		return { error: errors }; // Return all errors if all attempts failed
	} else {
		return { error: `The file ${absolutePath} does not exist. Please try to run the workflow again.` };
	}
}

export async function listFileBatches(vectorStoreId: string) {
	return openai.beta.vectorStores.files.list(vectorStoreId);
}

export async function createVectorStore() {
	const vectorStore = await openai.beta.vectorStores.create({
		name: "Dixa Documentation",
	});
	return vectorStore.id;
}

export async function listVectorStores() {
	return openai.beta.vectorStores.list();
}

export async function removeAllVectorStores() {
	const vectorStores = await openai.beta.vectorStores.list();
	await Promise.all(vectorStores.data.map(async (vs) => await deleteVectorStore(vs.id)));
}

export async function deleteVectorStore(id: string) {
	return openai.beta.vectorStores.del(id);
}

export function createThread() {
	return openai.beta.threads.create().then((a) => a.id);
}

export function createRun(threadId: string, assistantId: string) {
	return openai.beta.threads.runs.stream(threadId, {
		assistant_id: assistantId,
	});
}

export async function sendMessageToThread(threadId: string, message: string) {
	await openai.beta.threads.messages.create(threadId, {
		role: "user",
		content: message,
	});
}

export async function getAssistant(assistantId: string) {
	return openai.beta.assistants.retrieve(assistantId);
}

export async function updateAssistant(assistantId: string, aup: AssistantUpdateParams) {
	return openai.beta.assistants.update(assistantId, aup);
}
