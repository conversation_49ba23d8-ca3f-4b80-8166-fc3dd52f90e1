import OpenAI from "openai";
import Config from "../Config";

const openai = new OpenAI({
	apiKey: Config.OPENAI_API_KEY,
});

//This function was used to create the assistant. We should create the assistant only once. Leaving it here for future refference.
export async function createAssistant() {
	return openai.beta.assistants.create({
		name: "Cloudfarms Support Assistant",
		instructions:
			"Given a user's question, search through the uploaded documents to find the most relevant information to answer the question. Use the content from the files to generate a detailed and accurate response. When you provide an answer, reference the path of the source file in a footnote for further reading or verification.",
		model: "gpt-4o",
		tools: [{ type: "file_search" }],
	});
}
