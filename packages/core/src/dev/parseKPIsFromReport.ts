type KPI = {
	name: string;
	code: string;
	description?: string;
};

const inputText = `
Status at the end of the period:

KPI: Average reproduction cycle number [№]
KPI code: SOW_REPRODUCTION_CYCLE_AVG_NUMBER
August: 2.2
July: 2
June: 1.9
May: 1.8

KPI: Average parity number [№]
KPI code: SOW_PARITY_AVG_NUMBER
KPI description: The average parity of the sows.
August: 1.3
July: 1.2
June: 1.1
May: 0.9

KPI: Average age of sows at period end [days]
KPI code: SOWPAR0_PERIOD_END_AVG_AGE
KPI description: The average age of the sows and gilts at the end of the period.
August: 0
July: 0
June: 0
May: 0

KPI: Weaned piglets [№]
KPI code: WEANED_PIGLET_FINAL_AMOUNT
August: 0
July: 0
June: 0
May: 0

KPI: Piglets [№]
KPI code: PIGLET_FINAL_AMOUNT
August: 24301
July: 23632
June: 23938
May: 22866

KPI: Weaners [№]
KPI code: WEANER_FINAL_AMOUNT
August: 2148268
July: 2137643
June: 2126194
May: 2116955

KPI: Fatteners at period end [№]
KPI code: FATT_FINAL_AMOUNT
KPI description: Number of fatteners in stock at the end of the chosen period
August: 0
July: 0
June: 0
May: 0

KPI: Maiden gilts in third heat [№]
KPI code: GILT_IN_THIRD_HEAT
August: 0
July: 0
June: 0
May: 1

KPI: Maiden gilts in second heat [№]
KPI code: GILT_IN_SECOND_HEAT
August: 1
July: 1
June: 0
May: 2

KPI: Maiden gilts in first heat [№]
KPI code: GILT_IN_FIRST_HEAT
August: 18
July: 12
June: 38
May: 26

KPI: Young breeding animals in breeding animal locations at period end [№]
KPI code: GILT_IN_OTHERS_FINAL_AMOUNT
August: 0
July: 0
June: 0
May: 0

KPI: Service events [№]
KPI code: SERVICE_EVENTS_TOTAL
KPI description: Total number of service events in a period
August: 1777
July: 1908
June: 1756
May: 1855

KPI: Maiden gilts in sow locations [№]
KPI code: GILT_IN_SOWS_FINAL_AMOUNT
August: 296
July: 326
June: 183
May: 159

KPI: Young breeding animals age > 180 days [№]
KPI code: GILT_ABOVE_AGE_AMOUNT$180D
August: 0
July: 0
June: 0
May: 0

KPI: Young breeding animals age ≤ 180 days [№]
KPI code: GILT_BELLOW_AGE_AMOUNT$180D
August: 0
July: 0
June: 0
May: 0

KPI: Young breeding animals [№]
KPI code: MAIDENGILT_FINAL_AMOUNT
August: 296
July: 326
June: 183
May: 159

KPI: Gilts at period end [№]
KPI code: PAR0_FINAL_AMOUNT
KPI description: Number of inseminated gilts in stock at the end of the chosen period
August: 762
July: 813
June: 926
May: 1173

KPI: Sows at period end [№]
KPI code: SOW_FINAL_AMOUNT
KPI description: Number of sows in stock at the end of the chosen period
August: 3240
July: 3183
June: 3064
May: 2869

KPI: Sow, gilt and YBA at period end
KPI code: SOW_GILT_AND_YBA_AT_PERIOD_END
KPI description: Number of sows, inseminated gilts and breeding animals in stock at the end of the chosen period
August: 4298
July: 4322
June: 4173
May: 4201

KPI: Gilts and sows at period end [№]
KPI code: PAR0SOW_FINAL_AMOUNT
KPI description: Number of sows and inseminated gilts in stock at the end of the chosen period
August: 4002
July: 3996
June: 3990
May: 4042

KPI: Young boars at period end [№]
KPI code: YOUNG_BOAR_FINAL_REAL_AMOUNT
August: 9
July: 9
June: 9
May: 9

KPI: Boars at period end [№]
KPI code: BOAR_FINAL_AMOUNT
KPI description: Number of boars in stock at the end of the chosen period
August: 8
July: 8
June: 8
May: 8

Production efficiency:

KPI: Total feed conversion without dead [FU/1lb]
KPI code: ALL_FEED_CONVERSION_RATIO_WITHOUT_DEAD_ENERGY
August: 0
July: 0
June: 0
May: 0

KPI: Total feed conversion without dead [lb/1lb]
KPI code: ALL_FEED_CONVERSION_RATIO_WITHOUT_DEAD_WEIGHT
August: 0
July: 0
June: 0
May: 0

KPI: Total feed conversion [FU/1lb]
KPI code: ALL_FEED_CONVERSION_RATIO_ENERGY
August: 0
July: 0
June: 0
May: 0

KPI: Total feed conversion [lb/1lb]
KPI code: ALL_FEED_CONVERSION_RATIO_WEIGHT
August: 0
July: 0
June: 0
May: 0

KPI: Feed / breeding animal / year [lb]
KPI code: FEED_WEIGHT_BREEDING_PIG_YEAR
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Feed / breeding animal / year [FU]
KPI code: FEED_ENERGY_BREEDING_PIG_YEAR
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Weaner and fattener feed consumption [lb]
KPI code: WEANFATT_FEED_CONSUMPTION_WEIGHT
August: 0
July: 0
June: 0
May: 0

KPI: Weaner and fattener feed consumption [FU]
KPI code: WEANFATT_FEED_CONSUMPTION_ENERGY
August: 0
July: 0
June: 0
May: 0

KPI: Breeding animals feed consumption [lb]
KPI code: BREED_FEED_CONSUMPTION_WEIGHT
KPI description: Feed consumption of boars, sows, inseminated gilts and maiden gilts in kg
August: 0
July: 0
June: 0
May: 0

KPI: Breeding animals feed consumption [FU]
KPI code: BREED_FEED_CONSUMPTION_ENERGY
KPI description: Feed consumption of boars, sows, inseminated gilts and maiden gilts in feed units
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed consumption [lb]
KPI code: SOWS_FEED_CONSUMPTION_WEIGHT
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed consumption [FU]
KPI code: SOWS_FEED_CONSUMPTION_ENERGY
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed / weaned piglet [lb]
KPI code: SOW_FEED_WEIGHT_WEANED_PIGLETS
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed / weaned piglet [FU]
KPI code: SOW_FEED_ENERGY_WEANED_PIGLETS
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed / sow / year [lb]
KPI code: SOW_FEED_WEIGHT_SOW_YEAR
August: 0
July: 0
June: 0
May: 0

KPI: Sow feed / sow / year [FU]
KPI code: SOW_FEED_ENERGY_SOW_YEAR
August: 0
July: 0
June: 0
May: 0

Sow statistics (per year):

KPI: Sow animal unit [AU]
KPI code: SOW_ANIMAL_UNITS
August: 82.5
July: 82.3
June: 79
May: 82.9

KPI: Avg. number of sows, gilts and YBA
KPI code: AVG_NUMBER_OF_SOWS_GILTS_AND_YBA
KPI description: Average number of sows, inseminated gilts and maiden gilts based on Feeding days
August: 4309
July: 4283
June: 4235
May: 4309

KPI: Avg. number of active sows [№]
KPI code: AVERAGE_SOW_AMOUNT
KPI description: Average number of sows based on Feeding days
August: 3996
July: 4007
June: 3996
May: 4050

KPI: Transferred fattener / sow / year [№]
KPI code: TRANSFERRED_FATTENERS_SOW_YEAR
More is better
August: 0
July: 0
June: 0
May: 0

KPI: Transferred weaner / sow / year [№]
KPI code: TRANSFERRED_WEANERS_SOW_YEAR
More is better
August: 0
July: 0
June: 0
May: 0

KPI: Weaned piglets / female / year (based on avg. sows+yba)
KPI code: WEANED_PIGLETS_FEMALE_YEAR_US
More is better
August: 29.05
July: 31.49
June: 26.56
May: 32.31

KPI: Weaned piglets / sow / year (based on avg. sows)
KPI code: WEANED_PIGLETS_SOW_YEAR_US
More is better
August: 31.33
July: 33.66
June: 28.15
May: 34.37

KPI: Weaned piglets / sow / year [№]
KPI code: WEANED_PIGLETS_SOW_YEAR
More is better
August: 33.2
July: 32.8
June: 31.2
May: 30.4

KPI: Liveborn/sow/year (based on avg. sows)
KPI code: LIVEBORNSOWYEAR_US
More is better
August: 37.62
July: 36.96
June: 35.97
May: 34.73

KPI: Live borns / sow / year [№]
KPI code: LIVE_BORNS_SOW_YEAR
More is better
KPI description: The average number of live born piglets pr. sow pr. year. It is calculated by multiplying the average number of litters pr sow pr year with the average number of live borns pr litter for the given period.
August: 37.6
July: 36.9
June: 35.9
May: 34.7

KPI: Litter / sow / year [№]
KPI code: LITTER_SOW_YEAR
More is better
KPI description: The average number of litter pr. sow pr. year.
August: 2.46
July: 2.42
June: 2.35
May: 2.28

Farrowing pen per year statistics:

KPI: Farrowing pens [№]
KPI code: FARROWING_PENS
KPI description: Number of farrowing pens specified in the locatoins' settings
August: 0
July: 0
June: 0
May: 0

Litter results:

KPI: Sold piglets [№]
KPI code: PIGLET_SOLD_AMOUNT
August: 0
July: 0
June: 0
May: 0

KPI: Dead weaned piglets [№]
KPI code: WEANED_PIGLET_DEAD_AMOUNT
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Lost piglets/farrowing incl. stillborn [№]
KPI code: LOST_PIGLETS_FARROWING_INCL_STILLBORN
Less is better
KPI description: Total born per litter minus weaned pigs per litter (totalborn is liveborn and stillborn).
August: 2.2
July: 2.1
June: 2.4
May: 2.4

KPI: Lost piglets/farrowing [№]
KPI code: LOST_PIGLETS_FARROWING
Less is better
KPI description: Liveborn per litter minus weaned pigs per litter.
August: 1.8
July: 1.7
June: 2
May: 1.9

KPI: Corrected piglet mortality [%]
KPI code: PIGLET_CORRECTED_MORTALITY
Less is better
KPI description: Liveborn in period against total weaned in period and then in %.
August: 11.8
July: 11.3
June: 13.2
May: 12.4

KPI: Pre-weaning mortality incl. nurse piglets
KPI code: PREWEANING_MORTALITY_INCL_NURSE_PIGLETS_US
Less is better
August: 10.6
July: 11.5
June: 13.5
May: 12.4

KPI: Pre-wean mortality [%]
KPI code: CALCULATED_PREWEANING_MORTALITY
Less is better
KPI description: It is based on the ratio between the liveborn and weaned piglets. Weaned piglets are counted in the chosen period and liveborn piglets are counted from the sows weaned in the chosen period. This KPI (pre-wean mortality) is better for a continuous flow production. It can provide bad/incorrect values however, when the piglets are exchanged between sows, when there are nursery sows. The less you exchange piglets between sows, the more accurate the number will be.
August: 10.9
August goal: 12
July: 11.8
July goal: 12
June: 12.4
June goal: 12
May: 12.2
May goal: 12

KPI: Dead piglets >8 days old of total loss %
KPI code: DEAD_PIGLETS_8_DAYS_OLD_OF_TOTAL_LOSS_US
Less is better
KPI description: Percentage of dead piglets that died in period and were more than 8 days old compared to the total amount of dead piglets
August: 23.3
July: 25
June: 24.2
May: 18.8

KPI: Dead piglets 2-8 days old of total loss %
KPI code: DEAD_PIGLETS_28_DAYS_OLD_OF_TOTAL_LOSS_US
Less is better
August: 24.8
July: 21.4
June: 25.3
May: 30.8

KPI: Dead piglets <2 days old of total loss %
KPI code: DEAD_PIGLETS_2_DAYS_OLD_OF_TOTAL_LOSS_US
Less is better
KPI description: Percentage of dead piglets that died in period and were less than 2 days old compared to the total amount of dead piglets
August: 51.8
July: 53.6
June: 50.5
May: 50.4

KPI: Dead piglet >8 days old of liveborn %
KPI code: DEAD_PIGLET_8_DAYS_OLD_OF_LIVEBORN_US
Less is better
KPI description: Percetage of piglets died after 8 days after farrowing compared to liveborn in the period
August: 2.7
July: 2.8
June: 3.1
May: 2.3

KPI: Dead piglet 2-8 days old of liveborn %
KPI code: DEAD_PIGLET_28_DAYS_OLD_OF_LIVEBORN_US
Less is better
KPI description: Percetage of piglets died between 2 and 8 days after farrowing compared to liveborn in the period
August: 2.9
July: 2.4
June: 3.2
May: 3.7

KPI: Dead piglet <2 days old of liveborn %
KPI code: DEAD_PIGLET_2_DAYS_OLD_OF_LIVEBORN_US
Less is better
KPI description: Percetage of piglets died within 2 days after farrowing compared to liveborn in the period
August: 6
July: 6.1
June: 6.4
May: 6.1

KPI: Dead piglets >8 days old
KPI code: PIGL_DEAD_GT_8_DAYS_OLD_AMOUNT
KPI description: Number of dead piglets that dies in period and where >8 days old
August: 342
July: 357
June: 362
May: 271

KPI: Dead piglets from 2-8 days old
KPI code: PIGL_DEAD_2_8_DAYS_OLD_AMOUNT
KPI description: Number of dead piglets that died in period and where between 2 and 8 days old
August: 364
July: 305
June: 377
May: 443

KPI: Dead piglets <2 days old
KPI code: PIGL_DEAD_LT_2_DAYS_OLD_AMOUNT
KPI description: Nubmber of dead piglets that died in period and where <2 days old
August: 760
July: 766
June: 754
May: 726

KPI: Avg. age of dead piglets
KPI code: AVG_AGE_OF_DEAD_PIGLETS_US
KPI description: Average age of dead piglets
August: 5
July: 5.2
June: 5.2
May: 4.8

KPI: Dead piglets before weaning [%]
KPI code: PIGLET_DEAD_RATIO
Less is better
KPI description: Percentage of registered dead piglets compared to the total number of liveborn piglets in the period
August: 11.49
July: 11.36
June: 12.65
May: 12.06

KPI: Dead piglets before weaning [№]
KPI code: PIGLET_DEAD_AMOUNT
Less is better
KPI description: Nubmber of registered dead piglets
August: 1466
July: 1428
June: 1493
May: 1440

KPI: Litter reconciliation
KPI code: LITTER_RECONCILIATION_US
Less is better
August: 669
July: -306
June: 1072
May: -1316

KPI: Nursery sows [%]
KPI code: NURSERY_SOWS
August: 2.84
July: 2.54
June: 3.87
May: 5.84

KPI: On-going nurse sows [№]
KPI code: ON_GOING_NURSE_SOWS
KPI description: Number of nursery weanings in the period where the sow isn't final weaned yet
August: 11
July: 8
June: 6
May: 13

KPI: Nurse sows [№]
KPI code: WEANED_NURSE_SOWS
KPI description: Number of weanings in the period which has a check mark in Nursery column
August: 20
July: 20
June: 35
May: 60

KPI: Acc. lactation length
KPI code: ACC_LACTATION_LENGTH
August: 19.6
July: 19.99
June: 19.38
May: 18.64

KPI: Regular lactation period [days]
KPI code: REGULAR_LACTATION_DAYS
August: 20
July: 20
June: 20
May: 19

KPI: Lactation period [days]
KPI code: LACTATION_DAYS
KPI description: Average lactation days calculated for weaned sows during the period. Sum of lactation days divided by the number of weaned sows.
August: 20.2
August goal: 21
July: 20.5
July goal: 21
June: 20.2
June goal: 21
May: 19.8
May goal: 21

KPI: Weaning weight per litter [lb]
KPI code: WEANING_WEIGHT_PER_LITTER
August: 0
July: 0
June: 0
May: 0

KPI: Weaning weight per weaning [lb]
KPI code: WEANING_WEIGHT_PER_WEANING
August: 0
July: 0
June: 0
May: 0

KPI: Weaned piglets total weight [lb]
KPI code: WEANED_PIGLETS_TOTAL_WEIGHT
More is better
August: 0
July: 0
June: 0
May: 0

KPI: Avg. weaning weight [lb]
KPI code: WEANING_WEIGHT
More is better
August: 0
July: 0
June: 0
May: 0

KPI: Avg. piglets weaned/litter (excl. partial weaning)
KPI code: AVG_PIGLETS_WEANEDLITTER_EXCL_PARTIAL_WEANING_US
More is better
August: 13.6
July: 13.6
June: 13.1
May: 13.3

KPI: Avg. piglets weaned/litter (>0 weaned pigs)
KPI code: AVG_PIGLETS_WEANEDLITTER_US
More is better
August: 14
August goal: 14.2
July: 13.9
July goal: 14.2
June: 13.7
June goal: 14.2
May: 14
May goal: 14.2

KPI: Weaned piglets per litter [№]
KPI code: WEANED_PIGLETS_PER_LITTER
More is better
KPI description: The average number of weaned piglets for each litter. It is calculated by dividing the number of all weaned piglets with the number of weaned sows.
August: 13.5
August goal: 13.2
July: 13.5
July goal: 13.2
June: 13.3
June goal: 13.2
May: 13.3
May goal: 13.2

KPI: Weaned piglets per weaning [№]
KPI code: WEANED_PIGLETS_PER_WEANING
More is better
KPI description: The average number of piglets per weaning and is calculated as the total number of weaned piglets divided by the total number of weanings during the period.
August: 13.1
July: 13.2
June: 12.8
May: 12.6

KPI: Pigs sold/transferred [№]
KPI code: WEAN_EXIT_AMOUNT
August: 0
July: 0
June: 0
May: 0

KPI: Partial weaned pigs [№]
KPI code: PARTIAL_WEANED_PIGLETS
KPI description: Number of pigs weaned at weanings marked as "Nursery" in the period
August: 323
July: 303
June: 379
May: 670

KPI: Weaned piglets / week [№]
KPI code: WEANED_PIGLETS_PER_WEEK
More is better
August: 2399
August goal: 2113
July: 2585
July goal: 2113
June: 2156
June goal: 2113
May: 2668
May goal: 2113

KPI: Weaned piglets
KPI code: WEANED_PIGLETS
More is better
August: 10625
July: 11449
June: 9239
May: 11815

KPI: Total dead piglets per litter [№]
KPI code: TOTAL_DEAD_PIGLETS_PER_LITTER
Less is better
August: 2.2
July: 2.1
June: 2.4
May: 2.3

KPI: Total dead piglets [№]
KPI code: TOTAL_DEAD_PIGLETS
Less is better
August: 1816
July: 1748
June: 1814
May: 1824

KPI: Mummified born ratio [%]
KPI code: MUMMIFIED_BORNS_RATIO
Less is better
August: 2.4
July: 2.3
June: 2.7
May: 3.3

KPI: Parity 1 mummificated borns/litter [№]
KPI code: PAR1_MUMMIFICATED_BORNS_PER_LITTER
Less is better
August: 0.5
July: 0.4
June: 0.5
May: 0.6

KPI: Parity 1 mumificated borns [№]
KPI code: PAR1_MUMIFICATED_BORNS
Less is better
August: 117
July: 136
June: 163
May: 239

KPI: Mummified borns per litter [№]
KPI code: MUMMIFIED_BORNS_PER_LITTER
Less is better
August: 0.39
August goal: 0.2
July: 0.36
July goal: 0.2
June: 0.43
June goal: 0.2
May: 0.53
May goal: 0.2

KPI: Mumificated borns [№]
KPI code: MUMIFICATED_BORNS
Less is better
August: 329
July: 299
June: 335
May: 419

KPI: Stillborns [%]
KPI code: DEAD_BORNS_RATIO
Less is better
August: 2.6
July: 2.4
June: 2.6
May: 3

KPI: Parity 1 stillborns/litter [№]
KPI code: PAR1_DEAD_BORNS_PER_LITTER
Less is better
August: 0.4
July: 0.4
June: 0.4
May: 0.4

KPI: Stillborn per litter [№]
KPI code: DEAD_BORNS_PER_LITTER
Less is better
KPI description: The average number of still-born piglets per litter in the given period.
August: 0.4
August goal: 0.8
July: 0.4
July goal: 0.8
June: 0.4
June goal: 0.8
May: 0.5
May goal: 0.8

KPI: Parity 1 stillborns [№]
KPI code: PAR1_DEAD_BORNS
Less is better
August: 108
July: 144
June: 132
May: 168

KPI: Stillborn [№]
KPI code: DEAD_BORNS
Less is better
KPI description: Total number of still-born piglets for the farrowings in the given period.
August: 350
July: 320
June: 321
May: 384

KPI: Weak borns [%]
KPI code: WEAK_BORNS_RATIO
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Vital liveborn piglets [%]
KPI code: VITAL_LIVE_BORNS_RATIO
More is better
August: 100
July: 100
June: 100
May: 100

KPI: Weak borns per litter [№]
KPI code: WEAK_BORNS_PER_LITTER
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Weak borns [№]
KPI code: WEAK_BORNS
Less is better
August: 0
July: 0
June: 0
May: 0

KPI: Breeding piglets per litter [№]
KPI code: BREED_BORNS_PER_LITTER
More is better
KPI description: The average number of live-born breeding piglets pr litter for the given period. It is calculated as live-born breeding piglets divided by live-born piglets.
August: 0
July: 0
June: 0
May: 0

KPI: Piglets for breeding [№]
KPI code: BREED_BORNS
More is better
KPI description: The total number of live-born piglets which have been selected for breeding in the period.
August: 0
July: 0
June: 0
May: 0

KPI: Liveborn ratio [%]
KPI code: LIVE_BORNS_RATIO
More is better
August: 94.9
July: 95.3
June: 94.7
May: 93.7

KPI: Parity 1 liveborns/litter [№]
KPI code: PAR1_LIVE_BORNS_PER_LITTER
More is better
KPI description: The average number of liveborn piglets pr litter for parity 1 farrowings finished in the given period.
August: 15.3
August goal: 15
July: 15.4
July goal: 15
June: 15.2
June goal: 15
May: 15.2
May goal: 15

KPI: Liveborn per litter [№]
KPI code: LIVE_BORNS_PER_LITTER
More is better
August: 15.3
August goal: 15
July: 15.3
July goal: 15
June: 15.3
June goal: 15
May: 15.2
May goal: 15

KPI: Parity 1 liveborns [№]
KPI code: PAR1_LIVE_BORNS
More is better
KPI description: The number of liveborn piglets from the finished farrowings with parity 1 during the set period.
August: 3901
July: 5409
June: 5333
May: 5951

KPI: Liveborn [№]
KPI code: LIVE_BORNS
More is better
August: 12760
July: 12571
June: 11804
May: 11939

KPI: Parity 1 total born / litter [№]
KPI code: PAR1_TOTAL_BORN_PER_LITTER
More is better
KPI description: Count of live borns, dead borns and mummified borns per farrowings for parity 1 sows.
August: 16.18
August goal: 16
July: 16.21
July goal: 16
June: 16.03
June goal: 16
May: 16.22
May goal: 16

KPI: Parity 1 total born [№]
KPI code: PARITY_1_TOTAL_BORN
More is better
KPI description: Count of live borns, dead borns and mummified borns for parity 1 sows.
August: 4126
July: 5689
June: 5628
May: 6358

KPI: Total born / litter [№]
KPI code: TOTAL_BORN_PER_LITTER
August: 16.1
August goal: 16
July: 16
July goal: 16
June: 16.2
June goal: 16
May: 16.3
May goal: 16

KPI: Total born [№]
KPI code: TOTAL_BORN_AMOUNT
KPI description: Total number of born piglets. Includes liveborn, still born and mummified piglets.
August: 13439
July: 13190
June: 12460
May: 12742

KPI: Weaned sows stillborn / litter [№]
KPI code: WEANED_SOWS_DEAD_BORNS_PER_LITTER
August: 0.4
July: 0.4
June: 0.5
May: 0.5

KPI: Weaned sows liveborn / litter [№]
KPI code: WEANED_SOWS_LIFE_BORNS_PER_LITTER
August: 15.1
July: 15.3
June: 15.4
May: 15.2

KPI: Weaned sows liveborn [№]
KPI code: WEANED_SOWS_LIFE_BORNS
August: 11879
July: 12941
June: 10676
May: 13494

KPI: 1st weaning [%]
KPI code: WEANED_PAR1_SOWS_RATIO
KPI description: The percentage of 1st weanings (Parity 1) compared to the total number of weanings.
August: 39.2
July: 47.7
June: 46.9
May: 56.9

KPI: 1st weaned sows [№]
KPI code: WEANED_PAR1_SOWS
KPI description: The total number of sows that has been weaned for the first time during the period
August: 309
July: 403
June: 326
May: 504

KPI: Litters weaned (>0 weaned pigs)
KPI code: LITTERS_WEANED_US
August: 759
July: 822
June: 676
May: 841

KPI: Litters weaned [№]
KPI code: WEANED_SOWS
KPI description: Total number of weanings. Corresponds to the number of sows weaned in the period.
August: 788
July: 845
June: 695
May: 886

KPI: Sows weaned with 0 pigs [№]
KPI code: WEANED_SOWS_ZERO_PIGS
KPI description: Number of sows weaned with 0 weaned pigs.
August: 29
July: 23
June: 19
May: 45

KPI: Weanings [№]
KPI code: WEANINGS
KPI description: Total number of weanings.
August: 811
July: 867
June: 723
May: 941

KPI: Pregnancy duration [days]
KPI code: GESTATING_DAYS
Less is better
KPI description: The average number of days of pregnancies for farrowed sows in the period (where farrowing was started). The duration is counted from first serving date to date where farrowing was started
August: 115
July: 116
June: 116
May: 116

KPI: Farrowing index
KPI code: FARROWING_INDEX
August: 2.54
July: 2.53
June: 2.47
May: 2.39

KPI: Number of consecutive farrows [№]
KPI code: COUNT_BETWEEN_FARROWINGS
KPI description: Number of consecutive farrowings pairs in period. Parity 2 sows and higher that farrowed in the period --> how many farrowings and previously farrowings pair.
August: 580
July: 472
June: 420
May: 392

KPI: Parity 1 farrowings [№]
KPI code: PAR1_FARROWINGS
More is better
KPI description: The number of finished farrowings of sows with parity 1 in the given period
August: 255
July: 351
June: 351
May: 392

KPI: Farrowings <7 liveborns %
KPI code: FARROWINGS_7_LIVEBORNS_US
Less is better
KPI description: Percentage of farrowings with less than 7 liveborns compared to total number of farrowings in period
August: 2.8
July: 2.6
June: 1.9
May: 2.6

KPI: Farrowings <7 liveborns [№]
KPI code: FARROWINGS_LESS_THAN_7_LIVEBORNS
KPI description: Number of farrowings with less than 7 liveborns
August: 23
July: 21
June: 15
May: 20

KPI: Assisted farrowing [№]
KPI code: ASSISTED_FARROWINGS
KPI description: Number of farrowing with a "Issue helped/Assisted" checkmark in farrowing screen
August: 0
July: 0
June: 0
May: 0

KPI: Farrowings [№]
KPI code: FARROWINGS
More is better
KPI description: The total number of farrowed sows in the period.
August: 835
July: 823
June: 771
May: 784

Reproduction:

KPI: Ins. gilts and sows transferred out [№]
KPI code: PAR0SOW_TRANSFERRED_OUT_AMOUNT
August: 0
July: 0
June: 0
May: 0

KPI: Exit age of sows [days]
KPI code: SOWPAR0_EXIT_AVG_AGE
August: 0
July: 0
June: 0
May: 0

KPI: Replacement rate at weaning [%]
KPI code: SOWS_WEANING_BASED_REPLACEMENT_RATE
August: 96.5
July: 115.3
June: 110.1
May: 129.7

KPI: Replacement rate at serving [%]
KPI code: SOWS_SERVING_BASED_REPLACEMENT_RATE
August: 64.5
July: 74
June: 37.5
May: 64.5

KPI: Replacement rate incl. bought gilts
KPI code: REPLACEMENT_RATE_US_INCL_BOUGHT_GILTS
Less is better
August: 59.88
August goal: 60
July: 69.32
July goal: 60
June: 35.36
June goal: 60
May: 60.7
May goal: 60

KPI: Replacement rate
KPI code: REPLACEMENT_RATE_US
Less is better
August: 59.88
August goal: 60
July: 69.32
July goal: 60
June: 35.36
June goal: 60
May: 60.7
May goal: 60

KPI: Sows remove rate [%/year]
KPI code: PAR0SOW_REMOVE_RATE
August: 62.8
August goal: 55
July: 72.3
July goal: 55
June: 53.3
June goal: 55
May: 49.4
May goal: 55

KPI: Females sold/year [%]
KPI code: FEMALES_SOLDYEAR
Less is better
August: 48.67
July: 56.39
June: 48.87
May: 32.81

KPI: Sows culling rate [%/year]
KPI code: PAR0SOW_CULLING_RATE
August: 49.5
August goal: 40
July: 59.4
July goal: 40
June: 38.4
June goal: 40
May: 33.4
May goal: 40

KPI: Females dead/year [%]
KPI code: FEMALES_DEADYEAR
Less is better
August: 12.58
August goal: 15.2
July: 12.65
July goal: 15.2
June: 14.66
June goal: 15.2
May: 15.59
May goal: 15.2

KPI: Sows mortality rate [%/year]
KPI code: PAR0SOW_DEAD_RATE
August: 13.26
August goal: 15
July: 12.93
July goal: 15
June: 14.92
June goal: 15
May: 15.99
May goal: 15

KPI: Dead sows ratio [%]
KPI code: SOWS_DEAD_SOLD_RATIO
Less is better
KPI description: Percentage of dead sows in relation to the total number of dead sows and inseminated gilts in period
August: 21.1
July: 17.9
June: 28
May: 32.4

KPI: Dead served gilts [№]
KPI code: PAR0_DEAD_AMOUNT
KPI description: Total number of dead inseminated gilts in period
August: 5
July: 11
June: 12
May: 9

KPI: Dead sows [№]
KPI code: SOW_DEAD_AMOUNT
KPI description: Total number of dead sows in period
August: 40
July: 33
June: 37
May: 46

KPI: Dead sows and gilts [№]
KPI code: SOWS_DEAD_AMOUNT
Less is better
KPI description: Total number of dead sows and inseminated gilts in period
August: 45
July: 44
June: 49
May: 55

KPI: Sold sows and gilts [№]
KPI code: SOWS_SOLD_AMOUNT
August: 168
July: 202
June: 126
May: 115

KPI: Sows added/gilts entered
KPI code: SOWS_ADDEDGILTS_ENTERED_US
August: 219
July: 252
June: 123
May: 222

KPI: Sow and ins. gilt feeding days [days]
KPI code: SOWS_FEEDING_DAYS
More is better
August: 123874
July: 124229
June: 119865
May: 125552

KPI: Gestation days at period end [days]
KPI code: SOWS_PERIOD_END_GESTATION_DAYS
August: 178306
July: 180980
June: 181035
May: 179224

KPI: Gestation days at period start [days]
KPI code: SOWS_PERIOD_START_GESTATION_DAYS
August: 180877
July: 181443
June: 180050
May: 176706

KPI: Empty sows (>60 days, no reserved) [%]
KPI code: FAILED_CYCLE_SCAN_EMPTY_PCT
August: 0
July: 0
June: 0
May: 0

KPI: Empty sows (>60 days, no reserved) [№]
KPI code: FAILED_CYCLE_SCAN_EMPTY
August: 0
July: 0
June: 0
May: 0

KPI: 30 days fertility for served in period [%]
KPI code: SOW_FERTILITY_RATE_FROM_PERIOD$30D
August: 95.9
July: 96.5
June: 98.1
May: 97

KPI: Observed heat [№]
KPI code: OBSERVED_HEAT
KPI description: Number of sows with a heat mark in the period. Number of sows with a negative pregnancy check in the period and to that added a heat reason which is marked for Heat in Illness type screen
August: 0
July: 0
June: 0
May: 0

KPI: Negative pregnancy check [№]
KPI code: NEGATIVE_PREGNANCY_CHECK
Less is better
KPI description: Number of sows and gilts with a negative pregnancy check in the period, but where the code does not have a mark for Abortion or Heat.
August: 13
July: 7
June: 7
May: 12

KPI: Conception rate
KPI code: CONCEPTION_RATE_US
More is better
KPI description: Percentage or succesful services compare to the total number of survices in the period
August: 95.7
August goal: 95
July: 95.7
July goal: 95
June: 96.3
June goal: 95
May: 93.3
May goal: 95

KPI: Farrowing rate of first farrowing sows [%]
KPI code: PAR1_FARROWING_RATE
More is better
August: 90.4
July: 92.6
June: 89.2
May: 89.1

KPI: Farrowing rate [%]
KPI code: FARROWING_RATE
More is better
KPI description: It is how many % of the farrowings are successful compared to the number of Servings. The farrowing rate KPI can only be used for periods, which is older than the pregnancy period, so that all of the servings should be “closed” already (either farrowed or failed).
August: 90.9
August goal: 92
July: 91.8
July goal: 92
June: 90.8
June goal: 92
May: 90.3
May goal: 92

KPI: Returner rate [%]
KPI code: REPEAT_SERVINGS_RATIO
Less is better
KPI description: The percentages of all servings were repeat servings during the given period.
August: 2.48
August goal: 5
July: 1.83
July goal: 5
June: 3.59
June goal: 5
May: 2.13
May goal: 5

KPI: Weaning to fertile insemination avg. days
KPI code: WEANING_TO_FERTILE_INSEMINATION_DAYS_AVG
Less is better
KPI description: Calculated as days from a weaning and until the sow is served with an insemination that leads to a farrowing and divided by number of this weanings.
August: 8.15
July: 9.13
June: 12.1
May: 16.55

KPI: Days from weaning to 1st service
KPI code: WEANING2SERVING_DAYS
Less is better
KPI description: Average number of days from the weaning to the first service in the period
August: 7.4
August goal: 5.5
July: 7.5
July goal: 5.5
June: 10.5
June goal: 5.5
May: 8.5
May goal: 5.5

KPI: NPD / sow / year (based on avg. sows)
KPI code: NPD_SOW_YEAR_US
Less is better
August: 33.77
July: 33.38
June: 40.95
May: 41.41

KPI: >>> NPD (late abortion)
KPI code: NPD_LATE_ABORTION_PER_LITTER
Less is better
KPI description: The total number of non productive days from service to when the status of the sow is changed to empty due to the registration of late abortion.
August: 0
July: 0
June: 0
May: 0

KPI: >>> NPD (weaning to remove)
KPI code: NPD_WEANING2SOLD_PER_LITTER
Less is better
KPI description: The total number of non productive days from last weaning of sow to when status of the sow is changed to removed.
August: 2.3
July: 3.5
June: 4
May: 3.5

KPI: >>> NPD (weaning to dead)
KPI code: NPD_WEANING2DEAD_PER_LITTER
Less is better
KPI description: The total number of non productive days from last weaning of sow to when status of the sow is changed to dead.
August: 0
July: 0.1
June: 0.1
May: 0

KPI: >>> NPD (service to sold)
KPI code: NPD_SERVING2SOLD_PER_LITTER
Less is better
KPI description: The total number of non productive days from service to when status of the sow is changed to sold.
August: 4
July: 1.9
June: 2.2
May: 3

KPI: >>> NPD (service to dead)
KPI code: NPD_SERVING2DEAD_PER_LITTER
Less is better
KPI description: The total number of non productive days from service to when status of the sow is changed to dead.
August: 1.4
July: 2.7
June: 3.1
May: 2.3

KPI: >>> NPD (service to exit)
KPI code: NPD_SERVING2EXIT_PER_LITTER
Less is better
KPI description: The total number of non productive days from service to when status of the sow is changed to exit.
August: 5.5
July: 4.6
June: 5.3
May: 5.3

KPI: >>> NPD (service to repeat service)
KPI code: NPD_SERVING2SERVING_PER_LITTER
Less is better
KPI description: The total number of non productive days from service to repeat service.
August: 0.7
July: 0.7
June: 1.8
May: 0.9

KPI: >>> NPD (weaning to service)
KPI code: NPD_WEANING2SERVING_PER_LITTER
Less is better
KPI description: The total number of non productive days from last weaning of sow to next service.
August: 5.3
July: 5
June: 6.2
May: 8.4

KPI: Non-productive days per litter
KPI code: NPD_PER_LITTER
Less is better
KPI description: The average number of non-productive days (NPD) for the period divided by the number of litters in the period. Non-productive days (NPD) are defined as days where the sow is in the following state: Empty, service to dead, service to exit, service to repeat service, service to sold, service to dead, weaning to dead, weaning to service, weaning to removed.
August: 13.7
July: 13.8
June: 17.4
May: 18.2

KPI: Gilt late abortions [№]
KPI code: PAR1_LATE_ABORTIONS
August: 0
July: 0
June: 0
May: 1

KPI: Late abortions [№]
KPI code: SOWS_LATE_ABORTIONS
August: 2
July: 0
June: 0
May: 2

KPI: Gilt early abortions [№]
KPI code: PAR0_EARLY_ABORTIONS
August: 1
July: 0
June: 0
May: 2

KPI: Early abortions [№]
KPI code: SOWS_EARLY_ABORTIONS
August: 5
July: 3
June: 3
May: 2

KPI: Abortions [%]
KPI code: SOWS_FAILED_CYCLES_ABORTION_PCT
KPI description: Total number of abortions divided with number of productive sows * 100
August: 0.18
July: 0.07
June: 0.08
May: 0.1

KPI: Abortions [№]
KPI code: SOWS_FAILED_CYCLES_ABORTION
KPI description: Total number of abortions. This includes both abortions early and later in the gestation period.
August: 7
July: 3
June: 3
May: 4

KPI: Weaned sows / week [№]
KPI code: WEANED_SOWS_PER_WEEK
KPI description: Number of sows weaned per week.
August: 178
July: 191
June: 162
May: 200

KPI: Farrowings / week [№]
KPI code: FARROWINGS_PER_WEEK
More is better
KPI description: The total number of farrowed sows per week
August: 189
July: 186
June: 180
May: 177

KPI: Pregnancy at 17 weeks [%]
KPI code: PREGNANCY_RATIO$17
More is better
KPI description: Shows how many % of the sows served during the same week, are still pregnant when they are in week 17 of the pregnancy.
August: 91.4
July: 90.9
June: 91.3
May: 91.5

KPI: Pregnancy in week 6 [%]
KPI code: PREGNANCY_RATIO$6
More is better
KPI description: Shows how many % of the sows served during the same week, are still pregnant when they are in week 6 of the pregnancy.
August: 96.3
July: 97.4
June: 95.3
May: 95

KPI: % sows bred within 7 days
KPI code: SOWS_BRED_WITHIN_7_DAYS_US
More is better
KPI description: Percentage of sows serviced in the period within 7 days after previously final weaning compared to all first time services.
August: 85.6
July: 84
June: 72
May: 80.9

KPI: Sows bred by 7 days [№]
KPI code: SERVED_WITHIN_7_DAYS
KPI description: Sows serviced in period within 7 days after previously final weaning. Count of how many sows are served within 7 days after their previously final weaning
August: 552
July: 555
June: 492
May: 525

KPI: Services / week [№]
KPI code: SERVINGS_PER_WEEK
More is better
August: 200
July: 210
June: 195
May: 201

KPI: Average repeat interval [days]
KPI code: REPEAT_INTERVAL_AVG
KPI description: Number of services followed at least by one reservice. Used in avg. repeat interval
August: 24.52
July: 26.12
June: 22.08
May: 35.7

KPI: Repeat services [№]
KPI code: REPEAT_SERVINGS
KPI description: The number of repeated inseminations that have been made during the given period.
August: 22
July: 17
June: 30
May: 19

KPI: Sows served [№]
KPI code: SOW_SERVINGS
More is better
August: 645
July: 661
June: 683
May: 649

KPI: % gilts of all first services
KPI code: GILTS_OF_ALL_FIRST_SERVICES_US
KPI description: Percentage of gilts served compared to only first services. So the the repeated servings are not compared to.
August: 25.3
July: 27.6
June: 15.3
May: 25.5

KPI: Gilts served [№]
KPI code: GILT_SERVINGS
More is better
KPI description: The total number of gilts served in the given period.
August: 219
July: 252
June: 123
May: 222

KPI: Matings per service
KPI code: MATINGS_PER_SERVICE_US
August: 2.01
August goal: 2.2
July: 2.05
July goal: 2.2
June: 2.1
June goal: 2.2
May: 2.08
May goal: 2.2

KPI: Services [№]
KPI code: SERVINGS
More is better
August: 886
July: 930
June: 836
May: 890

KPI: Third heat marked maiden gilts [№]
KPI code: GILT_THIRD_HEAT_MARKED
August: 0
July: 0
June: 0
May: 0

KPI: Second heat marked maiden gilts [№]
KPI code: GILT_SECOND_HEAT_MARKED
August: 1
July: 1
June: 0
May: 2

KPI: First heat marked maiden gilts [№]
KPI code: GILT_FIRST_HEAT_MARKED
August: 23
July: 12
June: 40
May: 25

Indicators per death reason:

KPI: Dead piglets before weaning [%] per death reason - Starve-out/Wasting
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 0.02
July: 0
June: 0.03
May: 0.02

KPI: Dead piglets before weaning [%] per death reason - Small Non-Viable
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 4.11
July: 4.5
June: 4.63
May: 4.33

KPI: Dead piglets before weaning [%] per death reason - Scours
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 0.29
July: 0.24
June: 0.73
May: 0.54

KPI: Dead piglets before weaning [%] per death reason - Rupture
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 0.83
July: 1.07
June: 1.56
May: 1.21

KPI: Dead piglets before weaning [%] per death reason - Laid On
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 4.89
July: 5.04
June: 4.85
May: 4.21

KPI: Dead piglets before weaning [%] per death reason - Injured/Deformed
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 1.34
July: 0.49
June: 0.8
May: 1.75

KPI: Dead piglets before weaning [%] per death reason - Health
KPI code: PIGLET_DEAD_RATIO<DeathReason>
Less is better
August: 0
July: 0.01
June: 0
May: 0

KPI: Dead sows [%] per death reason - Sudden Death
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 17.5
July: 18.18
June: 27.03
May: 28.26

KPI: Dead sows [%] per death reason - Prolapse
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 45
July: 36.36
June: 37.84
May: 32.61

KPI: Dead sows [%] per death reason - Injured/Lame
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 27.5
July: 27.27
June: 21.62
May: 17.39

KPI: Dead sows [%] per death reason - Health
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 2.5
July: 3.03
June: 5.41
May: 4.35

KPI: Dead sows [%] per death reason - Farrowing Complication/Abortion
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 7.5
July: 12.12
June: 2.7
May: 17.39

KPI: Dead sows [%] per death reason - Body Condition
KPI code: SOW_DEAD_AMOUNT_PCT<DeathReason>
August: 0
July: 0
June: 5.41
May: 0

KPI: Dead sows [№] per death reason - Sudden Death
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 7
July: 6
June: 10
May: 13

KPI: Dead sows [№] per death reason - Prolapse
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 18
July: 12
June: 14
May: 15

KPI: Dead sows [№] per death reason - Injured/Lame
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 11
July: 9
June: 8
May: 8

KPI: Dead sows [№] per death reason - Health
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 1
July: 1
June: 2
May: 2

KPI: Dead sows [№] per death reason - Farrowing Complication/Abortion
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 3
July: 4
June: 1
May: 8

KPI: Dead sows [№] per death reason - Body Condition
KPI code: SOW_DEAD_AMOUNT<DeathReason>
August: 0
July: 0
June: 2
May: 0`;

const parseKPIs = (text: string): KPI[] => {
	const kpiPattern = /KPI:\s*(.+)\nKPI code:\s*(.+)(?:\nKPI description:\s*(.+))?/g;
	const kpis: KPI[] = [];
	let match: RegExpExecArray | null;

	while ((match = kpiPattern.exec(text)) !== null) {
		const [_, name, code, description] = match;
		const kpi: KPI = { name, code };
		if (description) {
			kpi.description = description;
		}
		kpis.push(kpi);
	}

	return kpis;
};

const transformKPIs = (kpis: KPI[]): string[] => {
	return kpis.map((kpi) => {
		const base = `Name: ${kpi.name}\nCode: ${kpi.code}`;
		return kpi.description ? `${base}\nDescription: ${kpi.description}` : base;
	});
};

const kpis = parseKPIs(inputText);
// const transformedKPIs = transformKPIs(kpis);
// transformedKPIs.forEach((kpi) => console.log(kpi + "\n"));
console.log(JSON.stringify(kpis));
