import { PIGBOT_WORKER_QUEUE, startOrAwaitWorkflow } from "../temporal/temporal";
import { GetReport } from "../cf-link/GetReport";

async function test() {
	return await startOrAwaitWorkflow(
		PIGBOT_WORKER_QUEUE,
		`getFarmInfo-7619-prod-am`,
		GetReport,
		[
			{ farmId: 100430, env: "prod-am" },
			[
				{ from: "2025-01-01", to: "2025-01-31" },
				{ from: "2025-02-01", to: "2025-02-28" },
				{ from: "2025-03-01", to: "2025-03-31" },
				{ from: "2025-04-01", to: "2025-04-30" },
			],
			"en",
		],
		"10m",
	);
}

test().then((r) => {
	console.log("result");
	console.log(r);
});
