export interface Section {
	content: string;
	children: Section[];
}

export function splitMarkdownV3(markdown: string, removeHorizontalLine: boolean = false): Section[] {
	const lines = markdown.split("\n");
	const rootSections: Section[] = [];
	let currentSection: Section | null = null;
	let listStack: Section[] = [];

	function isHeading(line: string): boolean {
		return /^#{1,6}\s/.test(line) || /^\*\*[^*\n]+\*\*$/.test(line);
	}

	function getListDepth(line: string): number {
		const match = line.match(/^(\s*)([*-]|\d+\.)/);
		return match ? Math.floor(match[1].length / 2) : -1;
	}

	function isHorizontalLine(line: string): boolean {
		return /^(?:[-*_]){3,}$/.test(line.trim());
	}

	for (const line of lines) {
		if (isHorizontalLine(line) && removeHorizontalLine) continue; //Ignore horizontal line

		if (isHeading(line)) {
			currentSection = { content: line, children: [] };
			rootSections.push(currentSection);
			listStack = [];
		} else {
			const listDepth = getListDepth(line);
			if (listDepth >= 0) {
				const listItem: Section = { content: line.trim(), children: [] };
				if (listDepth === 0) {
					if (currentSection) {
						currentSection.children.push(listItem);
					} else {
						rootSections.push(listItem);
					}
					listStack = [listItem];
				} else {
					if (listDepth < listStack.length) {
						listStack = listStack.slice(0, listDepth);
					}
					if (listStack.length > 0) {
						listStack[listStack.length - 1].children.push(listItem);
						listStack.push(listItem);
					} else {
						if (currentSection) {
							currentSection.children.push(listItem);
						} else {
							rootSections.push(listItem);
						}
						listStack = [listItem];
					}
				}
			} else {
				// Handle non-list lines by appending to current context
				if (listStack.length > 0) {
					// Append to the deepest list item
					const lastItem = listStack[listStack.length - 1];
					lastItem.content += "\n" + line;
				} else if (currentSection) {
					// Append to current section's content if no children
					if (currentSection.children.length === 0) {
						currentSection.content += "\n" + line;
					} else if (line.trim() !== "") {
						// Create a new section if line is not blank
						currentSection = { content: line, children: [] };
						rootSections.push(currentSection);
					}
				} else if (line.trim() !== "") {
					// Create new root section
					currentSection = { content: line, children: [] };
					rootSections.push(currentSection);
				}
			}
		}
	}

	return rootSections;
}

export function splitMarkdownV2(markdown: string): Section[] {
	const lines = markdown.split("\n");
	const rootSections: Section[] = [];
	let currentSection: Section | null = null;
	let listStack: Section[] = [];

	function isHeading(line: string): boolean {
		return /^#{1,6}\s/.test(line) || /^\*\*[^*\n]+\*\*$/.test(line);
	}

	function getListDepth(line: string): number {
		const match = line.match(/^(\s*)([*-]|\d+\.)/);
		return match ? Math.floor(match[1].length / 2) : -1;
	}

	for (const line of lines) {
		if (isHeading(line)) {
			currentSection = { content: line, children: [] };
			rootSections.push(currentSection);
			listStack = [];
		} else {
			const listDepth = getListDepth(line);
			if (listDepth >= 0) {
				const listItem: Section = { content: line.trim(), children: [] };
				if (listDepth === 0) {
					if (currentSection) {
						currentSection.children.push(listItem);
					} else {
						rootSections.push(listItem);
					}
					listStack = [listItem];
				} else {
					if (listDepth < listStack.length) {
						listStack = listStack.slice(0, listDepth);
					}
					listStack[listStack.length - 1].children.push(listItem);
					listStack.push(listItem);
				}
			} else {
				if (currentSection && currentSection.children.length === 0) {
					currentSection.content += "\n" + line;
				} else if (line.trim() !== "") {
					currentSection = { content: line, children: [] };
					rootSections.push(currentSection);
				}
				listStack = [];
			}
		}
	}

	return rootSections;
}

/**
 * Finds a section with given id in a tree of sections
 */
export const findSection = (sectionId: string, sections: Section[], parentKey: string | undefined = undefined): string | undefined => {
	for (const [index, section] of sections.entries()) {
		const currentSectionId = parentKey ? `${parentKey}-${index}` : `${index}`;
		if (currentSectionId === sectionId) {
			function renderSection(section: Section): string {
				let content = section.content;
				for (const child of section.children) {
					content += "\n" + renderSection(child);
				}
				return content;
			}

			return renderSection(section);
		}
		const found = findSection(sectionId, section.children, currentSectionId);
		if (found) {
			return found;
		}
	}
};

/**
 * Parses markdown and finds section with given id
 * @param sectionId
 * @param markdown
 */
export function getSection(sectionId: string, markdown: string) {
	const rootSections = splitMarkdownV3(markdown); //This function is used to send feedback to slack. It should use the latest version of splitMarkdown

	const content = findSection(sectionId, rootSections);
	if (content) return content;
	else throw new Error(`Section with id ${sectionId} not found`);
}
