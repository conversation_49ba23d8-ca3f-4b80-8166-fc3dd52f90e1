import { renderPromptTemplate, TemplateOverrides } from "../prompt-template/PromptTemplate";
import { LanguageLevelPrompts } from "./LanguageLevelPrompts";
import { SystemHoldingId } from "../HoldingId";

/**
 * Return a context object with sentences describing standard and expert language level:
 * - WriteInStandardLanguageLevel
 * - WriteInExpertLanguageLevel
 */
export async function getLanguageLevelsContext(templateOverrides?: TemplateOverrides) {
	return {
		WriteInStandardLanguageLevel: (
			await renderPromptTemplate({
				promptId: { promptType: LanguageLevelPrompts.standard, templateOverrides, holdingId: SystemHoldingId },
				context: {},
			})
		).content,
		WriteInExpertLanguageLevel: (
			await renderPromptTemplate({
				promptId: { promptType: LanguageLevelPrompts.expert, templateOverrides, holdingId: SystemHoldingId },
				context: {},
			})
		).content,
	};
}
