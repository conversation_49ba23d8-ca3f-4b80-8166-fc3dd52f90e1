export type MessageChunk = string;
export type QueryStarted = { type: "queryStarted" };
export type ThreadId = { type: "threadId"; threadId: string };
export type QueryEnded = { type: "queryEnded" };
export type AnnotationSummaryChunk = { type: "annotationSummary"; annotationsSummary: string };
export type Annotation = { index: number; startIndex: number; endIndex: number; fileId: string; elevioLink: string; title: string };

export type SupportChatbotResponseChunk = MessageChunk | QueryStarted | ThreadId | QueryEnded | AnnotationSummaryChunk;

export type ChatbotMessageParams = { message: string; threadId?: string; sendToSlack: boolean };
export type OpenAiIdElevioId = { openAiId: string; elevioId: string; title: string };
export type FilesNotReady = { type: "filesNotReady" };
export type FilesReady = { type: "filesReady"; numberOfFiles: number };
