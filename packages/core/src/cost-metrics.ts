import { sql } from "./database";
import { UserContextOptional } from "./UserContext";
import Config from "./Config";
import logger from "./logger";
import { Usage } from "./llm/LlmCommon";

/**
 * Metric data without LLM api data
 */
export type MetricData = {
	/**
	 * Activity that is being measured
	 */
	activity: string;
	/**
	 * An activity can consist of several actions
	 */
	action: string;

	/**
	 * Context of the action. It is used to log the metric data to that user/farm/holding.
	 */
	context?: UserContextOptional;
};

export function saveCostMetric({
	metricData,
	model,
	usage,
}: {
	metricData: MetricData;
	model: string;
	usage: // Either cost or input/output tokens and cost
	| {
				cost: number;
		  }
		| Usage;
}) {
	// Wrapped in try-catch to avoid crashing the request with non-critical functionality
	try {
		if (Config.NODE_ENV === "production" && (metricData.activity === "dev" || metricData.action === "dev")) {
			logger.warn("Saving dev metric in production");
		}

		sql<[]>`INSERT INTO metrics (activity, action, account_id, farm_id, root_id, env, model, input_tokens, input_cost, output_tokens,
																 output_cost, cost)
						VALUES (${metricData.activity}, ${metricData.action}, ${metricData.context?.userId ?? null},
										${metricData.context?.farmId ?? null},
										${metricData.context?.rootId ?? null},
										${metricData.context?.env ?? null}, ${model},
										${"inputTokens" in usage ? usage.inputTokens : null}, ${"inputCost" in usage ? usage.inputCost : null},
										${"outputTokens" in usage ? usage.outputTokens : null}, ${"outputCost" in usage ? usage.outputCost : null},
										${"cost" in usage ? usage.cost : usage.inputCost + usage.outputCost})`.catch(logger.error);
	} catch (e) {
		logger.warn("Got error while saving metrics", e);
	}
}
