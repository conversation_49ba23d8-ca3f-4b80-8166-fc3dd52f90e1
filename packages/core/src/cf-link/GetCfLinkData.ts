import { FarmHoldingId } from "../FarmId";
import { proxyActivities } from "@temporalio/workflow";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities, ExtraDataPoints, Period } from "./CfLinkActivities";

export async function GetCfLinkData(params: {
	farmHoldingId: FarmHoldingId;
	langCode: string;
	periods: Period[];
	dataPointsToGet: string[];
	breeds: string | null;
}) {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "10m",
		taskQueue: getCfLinkQueue(params.farmHoldingId.env),
	});

	const latestPeriodOnly = params.periods.slice(0, 1);

	const dataPointsToGetSet = new Set(params.dataPointsToGet);

	const undefinedPromise = Promise.resolve(undefined);

	const [medicineUsages, additionalKpis, pregnancyFailuresPeriods] = await Promise.all([
		dataPointsToGetSet.has(ExtraDataPoints.MEDICINE_USAGE_SUMMARY)
			? activities.GetMedicineUsages(params.periods, params.farmHoldingId.farmId, params.langCode)
			: undefinedPromise,

		activities.CalculateKPIs(params.farmHoldingId.farmId, params.langCode, params.dataPointsToGet, params.periods),

		dataPointsToGetSet.has(ExtraDataPoints.PREGNANCY_FAILURES_REPORT)
			? activities.GetPregnancyFailures(params.farmHoldingId.farmId, params.langCode, latestPeriodOnly, params.breeds ?? "")
			: undefinedPromise,
	]);

	return { medicineUsages, additionalKpis, pregnancyFailures: pregnancyFailuresPeriods?.[0] };
}
