import { proxyActivities } from "@temporalio/workflow";
import { FarmId } from "../FarmId";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities, Period } from "./CfLinkActivities";
import { DateTime } from "luxon";
import { EfficiencyReportData } from "../EfficiencyReportData";

export async function GetReport(farmId: FarmId, periods: Period[], langCode: string, locationId?: number): Promise<EfficiencyReportData> {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "10m",
		taskQueue: getCfLinkQueue(farmId.env),
	});

	function getMonthList(locale: string): string[] {
		const year = new Date().getFullYear(); // 2020
		const monthList = [...Array(12).keys()]; // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
		const formatter = new Intl.DateTimeFormat(locale, {
			month: "long",
		});

		const getMonthName = (monthIndex: number) => formatter.format(new Date(year, monthIndex));

		return monthList.map(getMonthName);
	}

	const reportKpis = await activities.GetEfficiencyReport(farmId.farmId, langCode, periods, locationId);

	const report: EfficiencyReportData = {
		periods: periods,
		sections: reportKpis.map((section) => ({
			label: section.name,
			kpis: section.kpis
				.filter((kpi) => kpi.periodData !== undefined)
				.map((kpi) => ({
					label: kpi.name,
					code: kpi.code,
					isMoreBetterOpt: null, //TODO - read isMoreBetterOpt
					description: kpi.description,
					periodValues: kpi.periodData.map((periodValue) => ({
						value: periodValue.value ? periodValue.value : null,
						goalOpt: periodValue.goal ? periodValue.goal : null,
					})),
				})),
		})),
		reportName: "Efficiency Report",
		monthNames: getMonthList(langCode),
		language: langCode,
		reportContext: {
			useCommonWeight: null,
			breeds: "",
			reportDate: DateTime.now().toISODate(),
		},
	};

	return report;
}
