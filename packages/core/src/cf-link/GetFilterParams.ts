import { proxyActivities } from "@temporalio/workflow";
import { FarmId } from "../FarmId";
import { getCfLinkQueue } from "../temporal/TemporalKeys";
import { CfLinkActivities, Location, Period } from "./CfLinkActivities";

export type FilterParams = {
	locations: Location[];
	specificServingGroupLength: number | null;
};

export async function GetFilterParams(farmId: FarmId, period: Period, langCode: string): Promise<FilterParams> {
	const activities = proxyActivities<CfLinkActivities>({
		startToCloseTimeout: "1m",
		taskQueue: getCfLinkQueue(farmId.env),
	});

	const [farmLocations, specificServingGroupLength] = await Promise.all([
		activities.GetFarmLocations(farmId.farmId, langCode, period),
		activities.GetSpecificServingGroupLength(farmId.farmId, langCode),
	]);

	return {
		locations: farmLocations,
		specificServingGroupLength,
	};
}
