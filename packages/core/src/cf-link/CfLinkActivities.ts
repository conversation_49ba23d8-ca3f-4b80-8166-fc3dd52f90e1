export interface Period {
	from: string; // Assuming PeriodString contains date range or similar fields
	to: string;
}

export interface PeriodAmounts {
	medicineAmount: number;
	animalCount: number;
}

export interface MedicineUsageData {
	illnessType: string;
	medicine: string;
	animalType: string;
	medicineUnit: string;
	amounts: (PeriodAmounts | null)[];
}

export type KpiResultData = {
	name: string;
	code: string;
	description?: string;
	values: (number | null)[];
};

export type FullKpiPeriodData = {
	value?: number;
	goal?: number;
	rangeFrom?: number;
	rangeTo?: number;
	avgHoldingValue?: number;
};

export type FullKPIResult = {
	name: string;
	code: string;
	description?: string;
	periodData: FullKpiPeriodData[];
};

export type PregnancyFailureReason = {
	label: string;
	parityValues: (number | null)[];
	all: number;
};

export interface FarmTimelineEntity {
	from: string;
	to?: string;
	description: string;
}

export interface KpiGoalData {
	code: string;
	name: string;
	formula: string;
	description: string;
	goal: number;
	rangeFrom: number | null;
	rangeTo: number | null;
}

export interface Setting {
	readonly code: string;
	readonly label: string;
	readonly description: string;
	readonly value: string;
	readonly unit: string;
}

export type GetFarmInfo = {
	readonly farmName: string;
	readonly countryCode: string;
	readonly settings: Setting[];
};

export type Location = {
	id: number;
	name: string;
};

export type KpiSectionData = {
	name: string;
	kpis: FullKPIResult[];
};

export interface CfLinkActivities {
	GetFarmInfo(farmId: number, usesSeges: boolean): Promise<GetFarmInfo>;

	GetMedicineUsages(periodsString: Period[], farmId: number, langCode: string): Promise<MedicineUsageData[]>;

	CalculateAssociatedKpisFromCF(
		farmId: number,
		language: string,
		kpis: string[],
		periods: { from: string; to: string }[],
		filteredKpis: string[],
	): Promise<KpiResultData[]>;

	CalculateKPIs(farmId: number, language: string, kpis: string[], periods: { from: string; to: string }[]): Promise<KpiResultData[]>;

	GetPregnancyFailures(farmId: number, langCode: string, periodsString: Period[], breed: string): Promise<PregnancyFailureReason[][]>;

	GetTimeline(from: string, to: string, farmId: number, langCode: string): Promise<FarmTimelineEntity[]>;

	GetKpiGoals(farmId: number, date: string, langCode: string): Promise<KpiGoalData[]>;

	GetSpecificServingGroupLength(farmId: number, langCode: string): Promise<number | null>;

	GetFarmLocations(farmId: number, langCode: string, period: Period): Promise<Location[]>;

	GetEfficiencyReport(farmId: number, langCode: string, period: Period[], locationId?: number): Promise<KpiSectionData[]>;
}

/**
 * Non-KPI data points
 */
export const ExtraDataPoints = {
	MEDICINE_USAGE_SUMMARY: "MEDICINE_USAGE_SUMMARY",
	PREGNANCY_FAILURES_REPORT: "PREGNANCY_FAILURES_REPORT",
};
