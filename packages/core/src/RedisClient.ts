import RedisClient, { RedisOptions } from "ioredis";
import Config from "./Config";
import logger from "./logger";

// Shared Redis for rate limiting across all environments.
export const sharedRedisOptions: RedisOptions = {
	host: Config.SHARED_REDIS_HOST,
	port: Config.SHARED_REDIS_PORT,
	username: "default",
	password: Config.SHARED_REDIS_PASS,
	tls: {
		rejectUnauthorized: false,
	},
};

export const sharedRedisClient = new RedisClient(sharedRedisOptions);
sharedRedisClient.on("error", (err) => {
	logger.error(err);
});

// Use local Redis if available, otherwise use shared Redis
export const localRedisOptions: RedisOptions =
	Config.LOCAL_REDIS_HOST && Config.LOCAL_REDIS_PORT
		? {
				host: Config.LOCAL_REDIS_HOST,
				port: Config.LOCAL_REDIS_PORT,
			}
		: sharedRedisOptions;

export const localRedisClient = sharedRedisOptions === localRedisOptions ? sharedRedisClient : new RedisClient(localRedisOptions);

localRedisClient.on("error", (err) => {
	logger.error(err);
});
