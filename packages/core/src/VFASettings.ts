import { sql } from "./database";
import { vfaSettingsSchema } from "./VFASettingsSchema";
import { HoldingId } from "./HoldingId";

/**
 * Returns settings for a holding
 */
export async function getSettings(holdingId: HoldingId) {
	const response = await sql<[{ setting: unknown }]>`
		SELECT setting.setting as setting
		FROM setting
		WHERE env = ${holdingId.env}
			AND root_id = ${holdingId.rootId}`;

	return vfaSettingsSchema.parse(response[0]?.setting ?? {});
}
