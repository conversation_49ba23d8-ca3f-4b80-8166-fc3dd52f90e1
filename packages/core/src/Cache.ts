import * as cacheManager from "cache-manager";
import { createHash } from "crypto";
import { ioRedisStore } from "@tirke/node-cache-manager-ioredis";
import { localRedisOptions } from "./RedisClient";

export const cachePromise = cacheManager.caching(ioRedisStore, {
	instanceConfig: localRedisOptions,
});

export function hash(input: unknown) {
	return createHash("sha256").update(JSON.stringify(input)).digest("hex");
}
