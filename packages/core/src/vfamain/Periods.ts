import { Period as PeriodLink } from "../cf-link/CfLinkActivities";

export type PeriodType = "standard-period" | "1-week" | "calendar-months" | "quarterly";

export interface Period {
	periodType: PeriodType;
	periodSince: Date | number;
}

type DefaultPeriodType = {
	key: PeriodType;
	displayValue: string;
};

export const StandardPeriodLengthDays = 28;

export const isDatePeriod = (periodType: PeriodType) => periodType === "standard-period" || periodType === "1-week";

export function defaultPeriodsDynamic(periodLength: number): DefaultPeriodType[] {
	const standardPeriodLengthString = periodLengthString(periodLength);
	const standardPeriodDisplayValue = `6 x ${standardPeriodLengthString}`;
	return [
		{ key: "standard-period", displayValue: standardPeriodDisplayValue },
		{ key: "1-week", displayValue: "6 x 1-week" },
		{ key: "calendar-months", displayValue: "6 calendar months" },
		{ key: "quarterly", displayValue: "Quarterly" },
	];
}

export function periodLengthString(periodLength: number) {
	return periodLength !== 0 && periodLength % 7 === 0 ? `${periodLength / 7}-week` : `${periodLength}-day`;
}

export function formatDateToISODate(date: Date): string {
	return date.toISOString().split("T")[0]; // Format to YYYY-MM-DD
}

export function getDatePeriods(period: Period, customServingGroupLength?: number): PeriodLink[] {
	//TODO - check if customServingGroupLength is always added!

	function generatePeriods(periodSince: Date, periodLength: number, numberOfPeriods: number) {
		const periodLinks: PeriodLink[] = [];
		for (let i = 0; i < numberOfPeriods; i++) {
			const to = new Date(periodSince.getFullYear(), periodSince.getMonth(), periodSince.getDate() - i * periodLength);
			const from = new Date(periodSince.getFullYear(), periodSince.getMonth(), periodSince.getDate() - ((i + 1) * periodLength - 1));

			periodLinks.push({
				from: formatDateToISODate(from),
				to: formatDateToISODate(to),
			});
		}
		return periodLinks;
	}

	function generateMonthsPeriods(firstMonth: number, monthNumbers: number, numberOfPeriods: number) {
		const periodLinks: PeriodLink[] = [];
		let year = new Date().getFullYear();
		const month = new Date().getMonth();

		if (firstMonth > month) {
			year -= 1;
		}

		for (let i = 0; i < numberOfPeriods; i++) {
			const to = new Date(year, firstMonth - i * monthNumbers, 0);
			const from = new Date(year, firstMonth - i * (monthNumbers - 1), 1); // Last day of the month
			periodLinks.push({
				from: formatDateToISODate(from),
				to: formatDateToISODate(to),
			});
		}
		return periodLinks;
	}

	// periodSince is not correctly typed as Date. We need to manually convert it.
	if (typeof period.periodSince === "string") {
		const periodSince = new Date(period.periodSince);
		if (period.periodType === "standard-period" && customServingGroupLength) {
			const normalizedServingLength = Math.floor(28 / customServingGroupLength) * customServingGroupLength;
			return generatePeriods(periodSince, normalizedServingLength, 6);
		} else if (period.periodType === "standard-period") {
			return generatePeriods(periodSince, 28, 6);
		} else if (period.periodType === "1-week") {
			return generatePeriods(periodSince, 7, 6);
		}
	} else if (typeof period.periodSince === "number") {
		if (period.periodType === "calendar-months") {
			return generateMonthsPeriods(period.periodSince, 1, 6);
		} else if (period.periodType === "quarterly") {
			return generateMonthsPeriods(period.periodSince, 3, 6);
		}
	}
	throw new Error(`Unsupported period type: ${JSON.stringify(period)}`);
}
