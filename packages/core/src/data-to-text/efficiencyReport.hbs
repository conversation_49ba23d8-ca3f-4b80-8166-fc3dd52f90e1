Efficiency Report

{{#if (not periodsMatchMonths)}}
Periods (YYYY-MM-DD):
{{#each periods}}
Period {{add @index 1}}: {{this.from}} - {{this.to}}
{{/each}}

{{/if}}

{{#if reportContext}}
{{#if reportContext.useCommonWeight}}This report will use common weight, so avg. common entry and exit weights from location will be used in calculation of Produced pigs KPI.
{{else if (eq reportContext.useCommonWeight false)}}This report will not use common weight, so weight entered at entry and exit will be used.
{{/if}}
{{#if reportContext.breeds}}The report was created for the following breeds: {{reportContext.breeds}}
{{/if}}
{{/if}}

{{#each sections}}
{{this.label}}:

{{> kpiText kpis=kpis periodNames=../periodNames}}
{{/each}}