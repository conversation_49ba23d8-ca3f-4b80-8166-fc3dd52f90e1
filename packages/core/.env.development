CHATGPT4O_TPM=10000000
LOCAL_REDIS_HOST=localhost
LOCAL_REDIS_PORT=6379
PG_HOST=localhost
PG_PORT=6543
PG_USER=postgres
PG_PASSWORD=postgrespassword
PG_DATABASE=postgres
JWT_KEY=3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R
WORKER_DIR=/tmp/worker
JWT_SECRET=3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R
ENABLE_WIP=true
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_JWT_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lc3BhY2Vfcm9sZXMiOnsicGlnYm90IjoiYWRtaW4ifX0.D2z-EDpFkQpJ3PezptWWBZahnDJ_Xw_aO0tOa8cmta8
# See DevJwtToken.ts for enviromnent of development user. See TestData.ts for test user.
# openai project development
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_ASSISTANT_ID=asst_B7HGjq9LuFJIBMwgvUz5hFjd