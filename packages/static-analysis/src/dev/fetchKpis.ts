import { runWor<PERSON> } from "../worker";
import logger from "pigbot-core/src/logger";
import { temporalClient } from "pigbot-core/src/temporal/temporal";
import { getKpisWithDescriptionsWorkflow } from "../workflows";
import * as fs from "node:fs";

async function start() {
	runWorker().catch((err) => {
		logger.error(err);
		process.exit(1);
	});

	const client = await temporalClient;

	const handle = await client.workflow.start(getKpisWithDescriptionsWorkflow, {
		taskQueue: "static",
		args: [],
		workflowId: "calculateAssociations",
		workflowIdReusePolicy: 4,
	});

	const kpisWithDescriptions = await handle.result();
	fs.writeFileSync("kpisWithDescriptions.json", JSON.stringify(kpisWithDescriptions, null, 2));
}

start()
	.then(() => process.exit(0))
	.catch(console.error);
