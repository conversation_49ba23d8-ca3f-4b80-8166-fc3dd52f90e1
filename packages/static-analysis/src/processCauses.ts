/* eslint-disable no-console */
import breedingKPIs from "pigbot-core/src/utils/breedingKPIs.json";
import { structuredCompletionChatGpt, textCompletionChatGpt } from "pigbot-core/src/llm/ChatGptClient";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import * as path from "node:path";
import { z } from "zod";
import { pathExists, writeFile, writeFileSync } from "fs-extra";
import { KpiStaticAnalysis, loadCauses } from "pigbot-core/src/relevant-data/RelevantDataService";

type KPIData = {
	Code: string;
	Name: string;
	Description: string;
};

async function generateCausesSummary(kpi: KPIData): Promise<string> {
	const generateCausesPrompt = renderTemplateFile(path.resolve(__dirname, "generateCauses.hbs"), kpi);

	const systemMsg = "You are a pig farm manager who studied veterinary school.";
	const causesSummary = await textCompletionChatGpt({
		messages: {
			systemMsg,
			userMsg: generateCausesPrompt,
		},
		cacheIdx: 0,
		maxToken: 4096,
		metricData: {
			action: "generateCauses",
			activity: "makeAssociations",
		},
	});

	return causesSummary;
}

export async function processCauses() {
	const reportKpis: KPIData[] = breedingKPIs;
	const allCauses = await loadCauses();

	// Generate causesSummary in parallel for all KPIs
	const causesSummaryPromises = reportKpis.map((kpi) => generateCausesSummary(kpi));
	const causesSummaries = await Promise.all(causesSummaryPromises);

	// Process the rest sequentially
	for (let index = 0; index < reportKpis.length; index++) {
		const kpi = reportKpis[index];
		const causesSummary = causesSummaries[index];

		const filePath = path.resolve(__dirname, `kpi/${kpi.Code}.json`);

		if (await pathExists(filePath)) {
			console.log(`KPI ${index + 1}/${reportKpis.length}: ${kpi.Code} - Already exists, skipping...`);
			continue; // Skip to the next KPI if the file exists
		}

		console.log(`KPI ${index + 1}/${reportKpis.length}: ${kpi.Code} - Processing...`);

		const prompt = renderTemplateFile(path.resolve(__dirname, "transformCause.hbs"), {
			causesSummary,
			allCauses: allCauses.length > 0 ? allCauses : null, // allCauses is now loaded from directory
		});

		const systemMsg = "You are a pig farm manager who studied veterinary school.";
		const response = await structuredCompletionChatGpt({
			zodObject: z.object({
				causes: z
					.array(
						z.object({
							cause: z
								.string()
								.describe(
									"Description of the possible cause of the problem. Make sure the description of the cause is clearly understandable, even without any additional context. If the cause targets a type of animals, make sure the type is mentioned.",
								),
							existingId: z
								.number()
								.nullable()
								.describe(
									"The ID of the historical cause that matches the possible cause. The text of the historical cause may be different, but the meaning must be exactly the same.",
								),
						}),
					)
					.describe(
						"Convert the summary of possible causes to JSON array. Each item in the array must be a distinct cause (don't use OR).",
					),
			}),
			objectName: "causes",
			messages: {
				systemMsg,
				userMsg: prompt,
			},
			metricData: {
				action: "generateCauses",
				activity: "makeAssociations",
			},
			maxToken: 4096,
			cacheIdx: 0,
		});

		const kpiToSave: KpiStaticAnalysis = {
			code: kpi.Code,
			name: kpi.Name,
			causes: [] as { cause: string; id: number }[],
		};

		for (const cause of response.causes) {
			let id;
			if (!cause.existingId) {
				id = allCauses.length > 0 ? Math.max(...allCauses.map((c) => c.id)) + 1 : 1; // Get max id and increment, or start from 1 if no causes yet
				allCauses.push({ cause: cause.cause, id, dataPointRelevancy: {} });
			} else {
				id = cause.existingId;
			}

			kpiToSave.causes.push({ cause: cause.cause, id });
		}

		await writeFile(filePath, JSON.stringify(kpiToSave, null, 2), "utf-8");
	}

	for (const cause of allCauses) {
		const safeShortenedCause = cause.cause
			.toLowerCase()
			.replace(/[^a-z0-9]+/g, "_")
			.substring(0, 40);
		const causeFilePath = path.resolve(__dirname, `cause/${cause.id}-${safeShortenedCause}.json`);
		writeFileSync(causeFilePath, JSON.stringify(cause, null, 2), "utf-8");
	}

	return allCauses;
}
