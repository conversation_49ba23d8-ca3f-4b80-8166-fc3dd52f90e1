/* eslint-disable no-console */
import logger from "pigbot-core/src/logger";
import { runWorker } from "./worker";
import { temporalClient } from "pigbot-core/src/temporal/temporal";
import { getKpisWithDescriptionsWorkflow } from "./workflows";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import * as path from "node:path";
import { encoderGpt4, structuredCompletionChatGpt } from "pigbot-core/src/llm/ChatGptClient";
import { processCauses } from "./processCauses";
import { z } from "zod";
import { writeFileSync } from "fs-extra";

import { ExtraDataPoints } from "pigbot-core/src/cf-link/CfLinkActivities";

async function start() {
	runWorker().catch((err) => {
		logger.error(err);
		process.exit(1);
	});

	const client = await temporalClient;

	const handle = await client.workflow.start(getKpisWithDescriptionsWorkflow, {
		taskQueue: "static-pigbot",
		args: [],
		workflowId: "calculateAssociations",
		workflowIdReusePolicy: 4,
	});

	const kpisWithDescriptions = (await handle.result()).filter((k) => k.descriptionOpt);

	const allCauses = await processCauses();

	const dataPoints = kpisWithDescriptions.map((kpi) => {
		function transformString(input: string): string {
			// Regex to find the content inside angle brackets and capture it
			const regex = /<([^>]+)>/;
			const match = input.match(regex);

			if (match) {
				const innerString = match[1]; // Get the captured group (content inside <>)

				// Use regex to convert PascalCase or similar to space-separated lowercase
				const transformedString = innerString.replace(/([A-Z])/g, (match, p1, offset) => {
					if (offset === 0) {
						return p1.toLowerCase(); // Lowercase the first letter
					} else {
						return " " + p1.toLowerCase(); // Add a space and lowercase subsequent uppercase letters
					}
				});
				return transformedString;
			} else {
				// If no match (not in <brackets>), return the original string or handle as needed
				return input; // Or throw an error, or return an empty string, etc.
			}
		}

		const placeholder = kpi.kpi.dynamicSource?.placeHolder;

		let desc;

		const suffix = "The KPI will contain monthly numerical values over a period of the last several months";
		if (placeholder) {
			const groupedBy = transformString(placeholder);
			desc = `This data point will provide unique KPI for each ${groupedBy} registered on the farm. Each KPI will only contain values relevant for the specific ${groupedBy}.\nKPI description: ${kpi.descriptionOpt}\n${suffix}`;
		} else {
			desc = `${kpi.descriptionOpt}\n${suffix}`;
		}
		const description = `KPI: ${kpi.kpi.name}\nDescription:\n${desc}`;

		const datapoint = {
			code: kpi.kpi.code,
			description,
		};
		return datapoint;
	});

	dataPoints.push({
		code: ExtraDataPoints.MEDICINE_USAGE_SUMMARY,
		description:
			"This data represents the amounts of medicine administered and the number of treated animals for an illness over a period of last several months. The data is organized into blocks, with each block detailing specific combinations of medicines, illness types, and animal types.",
	});

	dataPoints.push({
		code: ExtraDataPoints.PREGNANCY_FAILURES_REPORT,
		description:
			"This is a Pregnancy Failures Report from a pig management system, displaying data categorized by parity (the number of times a sow has given birth). The report tracks various failure-related metrics and reproductive statuses across different parity groups.\n" +
			"\n" +
			"Key Elements of the Report:\n" +
			"Total Failures: The number of failed pregnancies across different parity levels.\n" +
			"Return to Estrus Categories: Various types of return to estrus (early, regular, irregular, and late), indicating whether a sow did not conceive or lost pregnancy.\n" +
			"Empty Status: The percentage of sows that remain empty after an attempted pregnancy.\n" +
			"Abortions: The percentage of pregnancies that ended in abortion.\n" +
			"Deaths: The percentage of sows that died during the reproductive cycle.\n" +
			"Sales Data: Information on the percentage of sows sold to slaughterhouses or sold while pregnant.\n" +
			"Scanned Empty: The percentage of sows identified as not pregnant through ultrasound or other scanning methods.\n" +
			"The report provides an overview of reproductive inefficiencies and losses, allowing farm managers to assess fertility trends and take corrective actions where needed.",
	});

	const dataPointDesc: Record<string, string> = Object.fromEntries(dataPoints.map((dp) => [dp.code, dp.description]));

	const resolutionSchema = z.object({
		relevant: z.number().describe("Values 0-5. 0 means not relevant, 5 means very relevant"),
		explanation: z.string(),
	});

	const totalSelectedKPIs: string[] = [];

	// for await (const cause of allCauses.filter((c) => c.id === 12)) {
	// for await (const cause of allCauses.filter((c) => issueCauses.has(c.id))) {
	for await (const cause of allCauses) {
		let relevantDataPointsChanged = false;

		Object.keys(cause.dataPointRelevancy).forEach((code) => {
			// Remove data points that no longer exist
			if (dataPointDesc[code] === undefined) {
				console.log(`Removing ${code}`);
				if (cause.dataPointRelevancy[code] >= 4) {
					relevantDataPointsChanged = true;
				}
				delete cause.dataPointRelevancy[code];
			}
		});

		console.log(`Cause ${cause.id}: ${cause.cause}`);

		const unprocessedDataPoints = dataPoints.filter((dp) => cause.dataPointRelevancy[dp.code] === undefined);

		if (unprocessedDataPoints.length > 0) {
			console.log(`Filtering unprocessed data points: ${unprocessedDataPoints.length}`);
		}

		await Promise.all(
			unprocessedDataPoints.map(async (dataPoint) => {
				const prompt = renderTemplateFile(path.resolve(__dirname, "filterKPI.hbs"), { cause, dataPoint });

				let resolution;
				while (!resolution) {
					try {
						resolution = await structuredCompletionChatGpt({
							zodObject: resolutionSchema,
							objectName: "resolution",
							messages: {
								systemMsg: "You are a pig farm manager who studied veterinary school.",
								userMsg: prompt,
							},
							cacheIdx: 0,
							maxToken: 4096,
							metricData: {
								action: "filterKPI",
								activity: "makeAssociations",
							},
						});
					} catch (error) {
						logger.error("Failed to get response, retrying in 1 second...", error);
						await new Promise((resolve) => setTimeout(resolve, 1000));
					}
				}
				console.log(`${dataPoint.code}: ${resolution.relevant}`);
				cause.dataPointRelevancy[dataPoint.code] = resolution.relevant;
				if (resolution.relevant >= 4) {
					relevantDataPointsChanged = true;
				}
			}),
		);
		const relevantOnly = Object.entries(cause.dataPointRelevancy).filter(([, relevancy]) => relevancy >= 4);

		const relevantDataPointsContext = {
			dataPoints: relevantOnly.map(([code]) => `Code: ${code}\n${dataPointDesc[code]}`).join("\n\n"),
			cause: cause.cause,
		};

		if (relevantDataPointsChanged || cause.causeAnalysis === undefined) {
			const prompt = renderTemplateFile(path.resolve(__dirname, "causeAnalysis.hbs"), relevantDataPointsContext);
			console.log(`Relevant data points: ${relevantOnly.length}`);
			console.log(`Prompt size: ${encoderGpt4.encode(prompt).length}`);

			console.log("Selecting KPIs for cause analysis..");
			let causeAnalysis;
			while (!causeAnalysis) {
				try {
					causeAnalysis = await structuredCompletionChatGpt({
						zodObject: z.object({
							selectedDataPoints: z.array(z.string()).describe("List of codes of selected data points"),
							explanation: z.string(),
							// .describe(
							// 	"Provide a comprehensive explanation of what actions can be taken by analyzing the numerical values of the selected data points.",
							// ),
						}),
						objectName: "report",
						messages: {
							systemMsg: "You are a pig farm manager who studied veterinary school.",
							userMsg: prompt,
						},
						metricData: {
							action: "makeAssociations",
							activity: "makeAssociations",
						},
						cacheIdx: 0,
					});
				} catch (error) {
					logger.error("Failed to get response, retrying in 1 second...", error);
					await new Promise((resolve) => setTimeout(resolve, 1000));
				}
			}

			console.log("Cause analysis:");
			console.log(causeAnalysis);
			cause.causeAnalysis = causeAnalysis;
		} else {
			console.log(`Cause analysis already done`);
		}

		if (relevantDataPointsChanged || cause.actionAnalysis === undefined) {
			const prompt = renderTemplateFile(path.resolve(__dirname, "actionAnalysis2.hbs"), relevantDataPointsContext);

			console.log("Selecting KPIs for action analysis..");
			let actionAnalysis;
			while (!actionAnalysis) {
				try {
					actionAnalysis = await structuredCompletionChatGpt({
						zodObject: z.object({
							// actions: z.string(),
							selectedDataPoints: z.array(z.string()).describe("List of codes of selected data points"),
							explanation: z.string(),
						}),
						objectName: "report",
						messages: {
							systemMsg: "You are a pig farm manager who studied veterinary school.",
							userMsg: prompt,
						},
						metricData: {
							action: "makeAssociations",
							activity: "makeAssociations",
						},
						cacheIdx: 0,
					});
				} catch (error) {
					logger.error("Failed to get response, retrying in 1 second...", error);
					await new Promise((resolve) => setTimeout(resolve, 1000));
				}
			}

			console.log("Action analysis:");
			console.log(actionAnalysis);
			cause.actionAnalysis = actionAnalysis;
		} else {
			console.log(`Actions analysis already done`);
		}

		const finalSelectedDataPoints = Array.from(
			new Set([...cause.causeAnalysis!.selectedDataPoints, ...cause.actionAnalysis!.selectedDataPoints]),
		);

		console.log(`Final selected data points: ${finalSelectedDataPoints.length}`);
		console.log(finalSelectedDataPoints);
		totalSelectedKPIs.push(...finalSelectedDataPoints);

		const safeShortenedCause = cause.cause
			.toLowerCase()
			.replace(/[^a-z0-9]+/g, "_")
			.substring(0, 40);
		const causeFilePath = path.resolve(__dirname, `cause/${cause.id}-${safeShortenedCause}.json`);
		writeFileSync(causeFilePath, JSON.stringify(cause, null, 2), "utf-8");
	}

	const distinctIssueKpis = Array.from(new Set(totalSelectedKPIs));
	console.log(`Total distinct KPIs: ${distinctIssueKpis.length}`);
	console.log(distinctIssueKpis);
}

start()
	.then(() => process.exit(0))
	.catch(console.error);
