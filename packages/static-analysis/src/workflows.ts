import { proxyActivities } from "@temporalio/workflow";

interface Kpi {
	code: string;
	scope: Record<string, unknown>;
	name: string;
	formula: string;
	decimalPlaces: number;
	periodDependent: boolean;
	hideWhenZero: boolean;
	isMoreBetterOpt: boolean | null;
	paramsJsonOpt: string | null;
	daysOpt: number | null;
	inhabitantCodeOpt: string | null;
	externalId: string | null;
	ratioForAverageOpt: number | null;
	dynamicSource: {
		placeHolder: string;
	} | null;
	kpiUnit: Record<string, unknown>;
	dynamicValue: unknown | null;
	params: Record<string, unknown>;
	labelKeyAbbr: string;
	valueAtPeriodEnd: boolean;
}

export interface KpiDescription {
	kpi: Kpi;
	descriptionOpt: string;
}

interface StaticActivities {
	GetKpisWithDescriptions(): Promise<KpiDescription[]>;
}

const activities = proxyActivities<StaticActivities>({
	startToCloseTimeout: "2m",
	taskQueue: "static-link",
});

export async function getKpisWithDescriptionsWorkflow() {
	return await activities.GetKpisWithDescriptions();
}
