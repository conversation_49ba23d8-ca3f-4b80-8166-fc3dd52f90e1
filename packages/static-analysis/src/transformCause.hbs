You will be given a summary of possible causesAnalysis of a problem on a pig farm.
You will also be given a list historical causesAnalysis of problems on other farms.

Convert the summary of possible causesAnalysis to JSON array. Each item in the array must be a distinct cause (don't use OR).
The JSON array should contain the following fields:
cause: The possible cause of the problem. Make sure the cause is clearly understandable, even without any additional context. If the cause targets a type of animals, make sure the type is mentioned.
existingId: The ID of the historical cause that matches the possible cause. The text of the historical cause may be different, but the meaning must be exactly the same.

Historical causesAnalysis:
{{#if allCauses}}
{{#each allCauses}}
ID: {{this.id}}
{{this.cause}}
{{/each}}
{{/if}}
{{#unless allCauses}}
There are no historical causesAnalysis.
{{/unless}}
End of historical causesAnalysis.

Here is the summary:
{{causesSummary}}