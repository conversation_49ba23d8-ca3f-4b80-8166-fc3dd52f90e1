{"name": "pigbot-proto", "version": "1.0.0", "scripts": {"generate": "protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=esModuleInterop=true --ts_proto_opt=outputServices=grpc-js --ts_proto_out=./server ./src/* && protoc --plugin=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_opt=outputClientImpl=grpc-web --ts_proto_out=./web ./src/*"}, "devDependencies": {"@grpc/grpc-js": "^1.11.1", "protobufjs": "^7.3.3", "ts-proto": "^1.181.2"}, "dependencies": {"@improbable-eng/grpc-web": "^0.15.0", "browser-headers": "^0.4.1", "rxjs": "^7.8.1"}}