syntax = "proto3";
import "google/protobuf/wrappers.proto";

package com.cloudfarms.vfa;

service VFABackend {
	rpc GetEfficiencyReportSummary (GetEfficiencyReportSummaryRequest) returns (stream ChatResponse) {}
}

message GetEfficiencyReportSummaryRequest {
	EfficiencyReportResponse report = 1;
}

message EfficiencyReportResponse {
	repeated Period periods = 1;
	repeated Section sections = 2;
	string country = 3;
	string reportName = 4;
	repeated string monthNames = 5;
	string language = 6;
}

message Period {
	string from = 1;
	string to = 2;
}

message Section {
	string label = 1;
	repeated Kpi kpis = 2;
}

message Kpi {
	string label = 1;
	string code = 2;
	google.protobuf.BoolValue isMoreBetterOpt = 3;
	repeated PeriodValue periodValues = 4;
}

message PeriodValue {
	double value = 1;
	google.protobuf.DoubleValue goalOpt = 2;
}

message ChatResponse {
	oneof response {
		string message = 1;
		TokenCounts tokenCounts = 2;
	}
}

message TokenCounts {
	int32 inputTokensCount = 1;
	int32 outputTokensCount = 2;
}