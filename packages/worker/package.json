{"name": "pigbot-worker", "module": "index.ts", "devDependencies": {"@types/fs-extra": "^11.0.4", "node-dev": "^8.0.0", "nodemon": "^3.1.9", "testcontainers": "^10.13.1", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "nodemonConfig": {"execMap": {"ts": "ts-node"}, "ext": "ts", "watch": ["src"]}, "dependencies": {"@jest/globals": "^29.7.0", "@temporalio/activity": "^1.11.2", "@temporalio/client": "^1.11.7", "@temporalio/common": "^1.11.7", "@temporalio/worker": "^1.11.7", "@temporalio/workflow": "^1.11.7", "axios": "^1.7.7", "fp-ts": "^2.16.9", "fs-extra": "^11.2.0", "handlebars": "^4.7.8", "libreoffice-convert": "^1.6.0", "nanoid": "3.x", "node-poppler": "^7.2.2", "pdf-poppler": "^0.2.1", "pigbot-core": "workspace:*", "postgres": "^3.4.4", "rate-limiter-flexible": "^5.0.3", "rimraf": "^6.0.1", "rxjs": "^7.8.1", "sharp": "^0.33.5", "simple-pandoc": "^0.2.0", "ts-jest": "^29.2.5", "zod": "^3.23.8", "ts-pattern": "^5.4.0"}, "scripts": {"start": "nodemon src/worker.ts", "build": "tsc && ts-node src/build/build-workflow-bundle.ts", "build-workflow-bundle": "ts-node src/build/build-workflow-bundle.ts"}}