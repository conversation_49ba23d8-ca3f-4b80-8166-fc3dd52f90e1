import { proxyLocalActivities } from "@temporalio/workflow";
import type * as activites from "../activities";
import { combinePaths } from "../chatGPT/combinePaths";
import { match, P } from "ts-pattern";
import { OpenAiIdElevioId } from "pigbot-core/src/supportbot/SupportChatbotTypes";

const {
	fetchArticle,
	fetchArticleIds,
	uploadText,
	deleteDir,
	saveStringToFile,
	setupAssistVectorStore,
	createVectorStore,
	removeAllVectorStores,
	removeFileMap,
	insertFileMap,
} = proxyLocalActivities<typeof activites>({
	startToCloseTimeout: "2 minute",
});

const dixaPath = "dixa/";

export async function downloadAllArticles(assistantId: string) {
	await removeFileMap(assistantId);

	await removeAllVectorStores();

	const vectorStoreId = await createVectorStore();

	await setupAssistVectorStore(assistantId, vectorStoreId);

	async function downloadRec(page: number = 1, aggregatedResults: string[] = []): Promise<string[]> {
		const result = await fetchArticleIds(page);
		const newAggregatedResults = aggregatedResults.concat(...result.articleIds);
		if (page >= result.totalPages) return newAggregatedResults;
		else return downloadRec(page + 1, newAggregatedResults);
	}

	const articleIds = await downloadRec();

	const res = await Promise.all(
		articleIds.map(async (articleId) => {
			return await fetchArticle(articleId);
		}),
	);

	const filePathsArticleIds = await Promise.all(
		res
			.filter((f) => f !== undefined)
			.map(async ({ filename, content, articleId, title }) => {
				const filePath = combinePaths(dixaPath, filename + ".html");
				await saveStringToFile(content, filePath);
				return { filePath, articleId, title };
			}),
	);

	const errors: string[] = [];
	const successfulUploads: OpenAiIdElevioId[] = [];

	for (const { filePath, articleId, title } of filePathsArticleIds) {
		const uploadResult = await uploadText(vectorStoreId, filePath);
		match(uploadResult)
			.with({ fileId: P.string }, (res) => {
				successfulUploads.push({ openAiId: res.fileId, elevioId: articleId, title });
			})
			.with({ error: P.string }, (res) => {
				errors.push(res.error);
			})
			.exhaustive();
	}

	await insertFileMap(assistantId, successfulUploads);

	await deleteDir(dixaPath);
	return errors;
}
