import axios, { AxiosRequestConfig } from "axios";
import Config from "pigbot-core/src/Config";
import { RateLimiterQueue, RateLimiterRedis } from "rate-limiter-flexible";
import { sharedRedisClient } from "pigbot-core/src/RedisClient";
import logger from "pigbot-core/src/logger";
import { cachePromise } from "pigbot-core/src/Cache";
import { getCacheString } from "pigbot-core/src/supportbot/SupportChatbotUtils";
import { OpenAiIdElevioId } from "pigbot-core/src/supportbot/SupportChatbotTypes";

export {
	createVectorStore,
	listVectorStores,
	deleteVectorStore,
	uploadText,
	setupAssistVectorStore,
	listFileBatches,
	removeAllVectorStores,
} from "pigbot-core/src/llm/ChatGptClient";

interface articleIdsAndPages {
	totalPages: number;
	articleIds: string[];
}

interface Translation {
	title: string;
	language_id: string;
	body: string;
}

interface Article {
	id: string;
	translations: Translation[];
}

interface ArticlesApiResponse {
	articles: Article[];
	total_pages: number;
}

interface ArticleApiResponse {
	article: Article;
}

// Authorization token (replace with your actual token)
const token = Config.DIXA_TOKEN;

const jwt = Config.DIXA_JWT;

// Axios request configuration
const config: AxiosRequestConfig = {
	headers: {
		Authorization: `Bearer ${jwt}`, // Bearer token for authorization
		"x-api-key": token,
		"Content-Type": "application/json", // Specify the content type
	},
};

const limiterFlexible = new RateLimiterRedis({
	points: 10,
	duration: 1,
	storeClient: sharedRedisClient,
	keyPrefix: "dixa-api",
});

const limiterQueue = new RateLimiterQueue(limiterFlexible);

// Define an async function to perform the API request
export async function fetchArticleIds(page: number): Promise<articleIdsAndPages> {
	await limiterQueue.removeTokens(1);
	// API endpoint you want to connect to
	const apiUrl = `https://api.elev.io/v1/articles?page=${page}&status=published`;

	try {
		// Make the GET request
		const response = await axios.get<ArticlesApiResponse>(apiUrl, config);

		// If the request is successful, the response data will be logged
		const totalPages = response.data.total_pages;
		const articleIds = response.data.articles.map((article: Article) => article.id);

		return {
			totalPages,
			articleIds,
		};
	} catch (error) {
		logger.error("Error fetching data:", error);
		throw error;
	}
}

export async function fetchArticle(
	articleId: string,
): Promise<{ filename: string; content: string; articleId: string; title: string } | undefined> {
	await limiterQueue.removeTokens(1);

	const apiUrl = `https://api.elev.io/v1/articles/${articleId}`;
	try {
		// Make the GET request
		const response = await axios.get<ArticleApiResponse>(apiUrl, config);

		// If the request is successful, the response data will be logged
		const enTranslation = response.data.article.translations.find((t: Translation) => {
			return t.language_id === "en";
		});

		if (enTranslation && enTranslation.body !== null)
			return { filename: enTranslation.title.replace(/\//g, "_"), content: enTranslation.body, articleId, title: enTranslation.title };
		else return undefined;
	} catch (error) {
		logger.error("Error fetching data:", error);
		throw error;
	}
}

export async function removeFileMap(assistantId: string) {
	const cache = await cachePromise;
	await cache.del(getCacheString(assistantId));
}

export async function insertFileMap(assistantId: string, files: OpenAiIdElevioId[]) {
	const cache = await cachePromise;
	await cache.set(getCacheString(assistantId), files);
}
