export function combinePaths(...paths: string[]): string {
	// Filter out empty strings to avoid unnecessary slashes
	const filteredPaths = paths.filter((path) => path.trim() !== "");

	// Combine paths ensuring there is a single slash between each fragment
	const combinedPath = filteredPaths
		.map((path, index) => {
			// Remove leading and trailing slashes from each path except the first and last fragments
			if (index === 0) {
				return path.replace(/\/+$/, "");
			} else if (index === filteredPaths.length - 1) {
				return path.replace(/^\/+/, "");
			} else {
				return path.replace(/^\/+|\/+$/g, "");
			}
		})
		.join("/");

	return combinedPath;
}
