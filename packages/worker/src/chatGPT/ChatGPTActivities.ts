import { convertFileToPNGScreenshots as convertFileToPNGScreenshotsOG } from "../doc-processing/DocumentToPngs";
import Config from "pigbot-core/src/Config";
import * as path from "node:path";
import * as fs from "node:fs";
import { Readable } from "stream";
import { createWriteStream } from "fs";
import pandoc from "../doc-processing/pandoc";
//TODO - msatala - file contains activities that does not please tidy up.
export { getConfig } from "pigbot-core/src/Config";

export async function convertFileToPNGScreenshots(inputFile: string, outputDir: string, scalePageTo?: number): Promise<string[]> {
	return convertFileToPNGScreenshotsOG(inputFile, path.resolve(Config.WORKER_DIR, outputDir), scalePageTo);
}

export async function appendToFile(filePath: string, text: string) {
	// Append text to the file or create the file if it does not exist
	fs.appendFileSync(path.resolve(Config.WORKER_DIR, filePath), text, { encoding: "utf8" });
}

export async function markdown2Word(markdown: string, file: string) {
	// Create a readable stream from the markdown string
	const markdownStream = new Readable();
	markdownStream.push(markdown);
	markdownStream.push(null); // Signal the end of the stream

	// Create a write stream to the output Word file
	const writeStream = createWriteStream(path.resolve(Config.WORKER_DIR, file));

	// Convert markdown to Word using pandoc and pipe the streams
	const wordStream = pandoc("gfm", "docx").stream(markdownStream);
	wordStream.pipe(writeStream);

	// Return a promise that resolves when the conversion is complete
	return new Promise<void>((resolve, reject) => {
		writeStream.on("finish", resolve);
		writeStream.on("error", reject);
	});
}
