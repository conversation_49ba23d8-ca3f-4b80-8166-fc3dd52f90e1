import { rimraf } from "rimraf";
import path from "node:path";
import { ensureFile, writeFile } from "fs-extra";
import Config from "pigbot-core/src/Config";

export async function deleteDir(dir: string) {
	await rimraf(path.resolve(Config.WORKER_DIR, dir));
}

export async function saveStringToFile(data: string, file: string): Promise<void> {
	const filePath = path.resolve(Config.WORKER_DIR, file);
	await ensureFile(filePath);
	await writeFile(filePath, data, "utf-8");
}
