// @@@SNIPSTART typescript-bundle-workflow
import { bundleWorkflowCode } from "@temporalio/worker";
import { writeFile } from "fs/promises";
import path from "path";
import logger from "pigbot-core/src/logger";

async function bundle() {
	const { code } = await bundleWorkflowCode({
		workflowsPath: require.resolve("../workflows"),
	});
	const codePath = path.join(__dirname, "../../workflow-bundle.js");

	await writeFile(codePath, code);
}

// @@@SNIPEND

bundle().catch((err) => {
	logger.error(err);
	process.exit(1);
});
