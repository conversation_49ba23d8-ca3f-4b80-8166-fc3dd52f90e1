import { z } from "zod";
import { structuredCompletionChatGpt } from "pigbot-core/src/llm/ChatGptClient";
import logger from "pigbot-core/src/logger";

type KpiMatrixData = {
	code: string;
	isFormula: Boolean;
	isDynamic?: boolean;
	suffix: String[];
	description?: String;
};

type BaseKpiData = {
	code: String;
	description: String;
};

type FormulaKpiData = {
	code: String;
	formula: String;
};

type KpiAssociationData = {
	code: String;
	explanation: String;
};

type KpiMatrixAssociationData = {
	code: String;
	associated: KpiAssociationData[];
};

type KpiDescriptionData = {
	code: String;
	description: String;
};

function fold<T, U>(reducer: (acc: U, x: T) => U, init: U, xs: T[]): U {
	let acc = init;
	for (const x of xs) {
		acc = reducer(acc, x);
	}
	return acc;
}

const fn = (acc: String, x: KpiMatrixData) => {
	if (x.isFormula) {
		return acc + `Code: ${x.code}\nFormula: ${x.description}\n`;
	} else {
		return acc + `Code: ${x.code}\nDescription: ${x.description}\n`;
	}
};

const settings = `variable,label,description
"context$breed","Breed filter contain some breed(s)","Breed filter contain some breed(s) [boolean]"
"context$useCommonWeights","Use common weights", "Use common weights. If ""true"" avg. common entry and exit weights from location will be used in calculation of Produced pigs KPI else weight entered at entry and exit will be used [boolean]"
"settings$default_par1_weight","Default weight of first farrowed sow","The default weight for first farrowed sows"
"settings$fatt_gompertz_const_kg","Fatteners Seges standard Gompertz constant","Fatteners Seges standard Gompertz constant 230 kg for Danish market"
"settings$fatt_std_entry_weight_kg","Fatteners Seges standard entry weight","Fatteners Seges standard entry weight = 30 kg for Danish market"
"settings$fatt_std_exit_weight_kg","Fatteners Seges standard exit weight","Fatteners Seges standard exit weight = 110 kg for Danish market"
"settings$fatt_std_feed_const","Fatteners Seges standard feed constant","Fatteners Seges standard feed constant = 0.018 for Danish market"
"settings$fatt_std_liveweight_factor","Fatteners Seges standard liveweight factor","Fatteners Seges standard liveweight factor = 0.76 for Danish market"
"settings$fatt_std_slope_meat_pct","Fatteners Seges standard slope meat percentage","Fatteners Seges standard slope meat percentage = -0.07 for Danish market"
"settings$include_batches_in_feed_cons","Include batches in feed consumption KPIs per location","Include batches in feed consumption KPIs per location [boolean]"
"settings$include_mummies_in_total_born","Include mummified in calculation of total born","If this setting is set to ""false"", then mummified will not be a part of the calculation of total born as the calculation will then be ""liveborn + stillborn"". If the setting is set to ""true"", then the calculation instead will be ""liveborn + stillborn + mummified""."
"settings$lactation_days_avg","Average days for lactation","Average days for lactation"
"settings$litter_sow_year_algorithm","Algorithm for calculating litter per sow per year","Algorithm for calculating litter per sow per year. 1 = NPD based, 2 = Farrowing based"
"settings$newborn_weight","Newborn piglet weight","Newborn piglet weight, default 1 kg"
"settings$sow_production_with_nursery","Sow production with nursery","Sow production with nursery [boolean]"
"settings$wean_gompertz_const_kg","Weaners Seges standard Gompertz constant","Weaners Seges standard Gompertz constant 230 kg for Danish market"
"settings$wean_std_entry_weight_kg","Weaners Seges standard entry weight","Weaners Seges standard entry weight = 7 kg for Danish market"
"settings$wean_std_exit_weight_kg","Weaners Seges standard exit weight","Weaners Seges standard exit weight = 30 kg for Danish market"
"settings$wean_std_feed_const","Weaners Seges standard feed constant","Weaners Seges standard feed constant = 0.018 for Danish market"
`;

export async function getKpiAssociationMatrix(kpiCode: String, kpiMatrixData: KpiMatrixData[]): Promise<KpiMatrixAssociationData> {
	const preamble = `Try to find associations between the defined KPI and a set of other KPIs.
		By association, we mean that if the defined KPI is not performing well,
		other KPIs can help identify the root cause of the problem. Please try
		to find the smallest set of KPIs that are associated with the defined KPI.
		Additionally, there is an explanation field in the result. 
		Please write the explanation in a way that is easy to understand.
		You will receive a list of KPIs that have a description or formula as initial information.
		Also, formulas can use settings. 
		Here is an explanation in CSV format with a header:
		${settings}

		Here is the KPI code to be described: ${kpiCode}
		Here is the set of KPIs:
		`;

	const userMessage = preamble + fold(fn, "", kpiMatrixData);

	logger.debug("-------------------------------------------");
	logger.debug(`userMessage: ${userMessage}`);
	logger.debug("-------------------------------------------");

	const { response: llmResponse } = await structuredCompletionChatGpt({
		objectName: "kpi-matrix-association",
		zodObject: z.object({
			code: z.string(),
			associated: z.array(z.object({ code: z.string(), explanation: z.string() })),
		}),
		messages: [
			{
				role: "user",
				content: userMessage,
			},
		],
		metricData: {
			activity: "kpi-matrix-association",
			action: "kpi-matrix-association",
		},
		cacheIdx: 0,
	});

	const response: KpiMatrixAssociationData = {
		code: llmResponse.code,
		associated: llmResponse.associated,
	};
	logger.debug(`response code: ${response.code}, associations: ${response.associated.map((x) => x.code)}`);
	return response;
}

export async function getKpiDescriptions(
	kpiCode: String,
	baseKpiData: BaseKpiData[],
	formulaKpiData: FormulaKpiData[],
): Promise<KpiDescriptionData> {
	const preamble = `You will receive a list of KPIs identified by code, along with a description or mathematical formula as input information.
	Additionally, you will receive a single KPI code that needs to be described. 
	Use language similar to the descriptions already provided in the input information.
	Try to create a short summary description of the KPI.  
	Do not use KPI codes in the description. Do not convert KPI code .
	Also in formula can be used variables, here is explanation in csv format in header:
	${settings}
	end of variables
	  	
	terminology: 
	- use "interval" instead of "period"
	end of terminology
	
	Here is the KPI code to be described : ${kpiCode}
	
	Here is set of KPI:`;

	const fn1 = (acc: String, x: BaseKpiData) => {
		return acc + `Code: ${x.code}\nDescription: ${x.description}\n`;
	};

	const fn2 = (acc: String, x: FormulaKpiData) => {
		return acc + `Code: ${x.code}\nFormula: ${x.formula}\n`;
	};

	const userMessage = preamble + "\n\n" + fold(fn1, "", baseKpiData) + "\n" + fold(fn2, "", formulaKpiData);

	logger.debug("-------------------------------------------");
	logger.debug(`userMessage: ${userMessage}`);
	logger.debug("-------------------------------------------");

	const { response: llmResponse } = await structuredCompletionChatGpt({
		objectName: "kpi-description",
		zodObject: z.object({
			code: z.string(),
			description: z.string(),
		}),
		messages: [
			{
				role: "user",
				content: userMessage,
			},
		],
		metricData: {
			activity: "kpi-descriptions",
			action: "kpi-descriptions",
		},
		cacheIdx: 0,
	});

	const response: KpiDescriptionData = {
		code: llmResponse.code,
		description: llmResponse.description,
	};
	logger.debug(`response code: ${response.code}, description: ${response.description}`);
	return response;
}
