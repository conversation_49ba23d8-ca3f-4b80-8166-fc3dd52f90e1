import { concatAll, Observable, OperatorFunction, toArray } from "rxjs";
import { condition } from "@temporalio/workflow";

type Config = Awaited<ReturnType<typeof import("../activities").getConfig>>;

export function devTools(config: Config) {
	const isDevMode = config.NODE_ENV === "development";

	/**
	 * In development mode, completes all previous steps before continuing.
	 * In production mode, does nothing.
	 */
	function completePreviousStepsX<T>(): OperatorFunction<T, T> {
		if (isDevMode) return (source: Observable<T>) => source.pipe(toArray(), concatAll());
		else return (source: Observable<T>) => source.pipe();
	}

	/**
	 * In development mode, pauses the workflow forever.
	 *
	 * Can be used to prevent the workflow from finishing and then restarting it manually
	 */
	async function pauseHereX() {
		if (isDevMode) return await condition(() => false);
		else return;
	}

	return {
		completePreviousStepsX,
		pauseHereX,
	};
}
