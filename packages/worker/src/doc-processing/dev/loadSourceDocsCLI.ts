import { loadSourceDocs, LoadSourceDocsParams } from "../loadSourceDocs";
import { SystemHoldingId } from "pigbot-core/src/HoldingId";
import { SegesManualIdentifier, SouthWestVetsSOPsIdentifier } from "pigbot-core/src/source-docs/SourceDoc";

async function main() {
	const defaultConfig = {
		debugMode: true,
		overwrite: false,
	};

	const configs: { [key: string]: LoadSourceDocsParams } = {
		seges: {
			...defaultConfig,
			source: SegesManualIdentifier,
			docDir: "/home/<USER>/dev/cloudfarms/pigbot/docs/SEGES manual for VFA",
		},
		swv: {
			...defaultConfig,
			source: SouthWestVetsSOPsIdentifier,
			docDir: "/home/<USER>/dev/cloudfarms/pigbot/docs/SWV SOPs for VFA",
		},
		jbs: {
			...defaultConfig,
			source: { sourceName: "SOPs", holdingId: { rootId: 100412, env: "prod-am" } },
			docDir: "/home/<USER>/dev/cloudfarms/pigbot/docs/JBS",
		},
		firstfarms: {
			...defaultConfig,
			source: { sourceName: "SOPs", holdingId: { rootId: 201, env: "prod-pigs" } },
			docDir: "/home/<USER>/dev/cloudfarms/pigbot/docs/Firstfarms/",
		},
		// Add more configurations here
	};

	const configName = process.argv[2];
	const config = configs[configName];

	if (config === undefined) {
		throw new Error(`Config not found for ${configName}`);
	}

	await loadSourceDocs(config);
}

main()
	.then(() => process.exit())
	.catch((e) => {
		console.error(e);
		process.exit(1);
	});
