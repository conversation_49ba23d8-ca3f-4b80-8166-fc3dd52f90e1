import { determinePageIdentifyIssuesRelevance, getRelevancy } from "../loadSourceDocs";
import { sql } from "pigbot-core/src/database";
import { from, mergeMap } from "rxjs";
import { ISSUE_IDENTIFICATION_STEP } from "pigbot-core/src/eff-report/identifyIssues";

// reprocess relevancy for content in the database
async function main() {
	const pages = await sql<[{ idx: number; source_doc_id: number; content: string }]>`
		SELECT idx, source_doc_id, content
		FROM source_doc_page;
	`;

	const updates = pages.map(async (page) => {
		const relevantFor = await getRelevancy(page.content);

		console.log(`Relevancy for ${page.source_doc_id}/${page.idx} is ${relevantFor}`);

		await sql`
			UPDATE source_doc_page
			SET relevant_for = ${relevantFor}
			WHERE idx = ${page.idx}
				AND source_doc_id = ${page.source_doc_id}`;
	});

	await Promise.all(updates);
}

main()
	.then(() => process.exit())
	.catch((e) => {
		console.error(e);
		process.exit(1);
	});
