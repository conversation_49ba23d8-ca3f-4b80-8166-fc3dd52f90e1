import { spawn } from "child_process";
import { Readable } from "stream";

const command = "pandoc";

const pandoc = (from: string, to: string, ...args: string[]) => {
	const option = ["-f", from, "-t", to].concat(args);

	const converter = (src: string) =>
		new Promise<string>((resolve, reject) => {
			const proc = spawn(command, option);
			proc.on("error", reject);
			let data = "";
			proc.stdout.on("data", (chunk) => {
				data += chunk.toString();
			});
			proc.stdout.on("end", () => resolve(data));
			proc.stdout.on("error", reject);
			proc.stdin.write(src);
			proc.stdin.end();
		});

	converter.stream = (srcStream: Readable) => {
		const proc = spawn(command, option);
		srcStream.pipe(proc.stdin);
		return proc.stdout;
	};

	return converter;
};

export default pandoc;
