import { promises as fs } from "fs";
import Config from "pigbot-core/src/Config";
import { from, lastValueFrom, mergeAll, mergeMap, of } from "rxjs";
import { combinePaths } from "../chatGPT/combinePaths";
import { convertFileToPNGScreenshots, saveStringToFile } from "../activities";
import { renderTemplateFile } from "pigbot-core/src/utils/renderTemplateFile";
import path from "node:path";
import { isSingleColorPNG } from "./isSingleColorPNG";
import { structuredCompletionChatGpt, textCompletionChatGpt, visionCompletion } from "pigbot-core/src/llm/ChatGptClient";
import { parseJson } from "../chatGPT/parseJson";
import { sql } from "pigbot-core/src/database";
import * as os from "node:os";
import { z } from "zod";
import { pipe } from "fp-ts/function";
import * as A from "fp-ts/Array";
import breedingKPIs from "pigbot-core/src/utils/breedingKPIs.json";
import { SourceIdentifier } from "pigbot-core/src/source-docs/SourceDoc";
import { ISSUE_IDENTIFICATION_STEP } from "pigbot-core/src/eff-report/identifyIssues";
import logger from "pigbot-core/src/logger";

async function extractPageContent(imageFile: string, activity: string) {
	const imageToTextPrompt = renderTemplateFile(path.resolve(__dirname, "vision-page-to-text.hbs"));

	const imagePath = path.resolve(Config.WORKER_DIR, imageFile);

	if (await isSingleColorPNG(imagePath)) return null;
	else
		return await visionCompletion(imagePath, imageToTextPrompt, {
			activity,
			action: "extractPageContent",
		});
}

async function getLanguages(text: string, activity: string) {
	const { response } = await textCompletionChatGpt({
		messages: [
			{
				role: "user",
				content: renderTemplateFile(path.resolve(__dirname, "get-languages.hbs"), { text }),
			},
		],
		metricData: {
			activity: activity,
			action: "getLanguages",
		},
	});

	return parseJson<string[]>(response);
}

const activity = "process-source-doc";

async function extractContent(docFilePath: string, workDir: string, debugMode = false) {
	await fs.mkdir(workDir, { recursive: true });

	logger.debug(`Processing doc: ${docFilePath} into directory: ${workDir}`);

	const filesInOutputDir = await fs.readdir(workDir);

	const existingPngFiles = filesInOutputDir.filter((file) => file.endsWith(".png"));

	let pngs;
	if (existingPngFiles.length > 0) {
		logger.debug(`Found ${existingPngFiles.length} PNG files in ${workDir}, skipping conversion.`);
		pngs = existingPngFiles;
	} else {
		pngs = await convertFileToPNGScreenshots(docFilePath, workDir, 1024);
	}

	return from(pngs).pipe(
		mergeMap(async (png, idx) => {
			const content = await extractPageContent(combinePaths(workDir, png), activity);

			logger.debug(`${docFilePath}: Extracted text #${idx}`);
			if (content) {
				if (debugMode) {
					const nameWithoutExt = png.substring(0, png.length - 4);

					await saveStringToFile(content, path.resolve(workDir, `${nameWithoutExt}.md`));
				}

				return of({
					idx,
					content,
					png,
				});
			} else return of();
		}),
		mergeAll(),
	);
}

async function getDocFiles(docDir: string): Promise<string[]> {
	const docFiles: string[] = [];

	async function walkDir(currentPath: string) {
		const entries = await fs.readdir(currentPath);
		for (const entry of entries) {
			const fullPath = path.join(currentPath, entry);
			const stat = await fs.stat(fullPath);

			if (stat.isDirectory()) {
				await walkDir(fullPath);
			} else if (stat.isFile() && (path.extname(entry).toLowerCase() === ".pdf" || path.extname(entry).toLowerCase() === ".docx")) {
				docFiles.push(path.relative(docDir, fullPath));
			}
		}
	}

	await walkDir(docDir);
	return docFiles;
}

function getWipName(name: string) {
	return `${name}-wip`;
}

async function saveSourceDoc(sourceId: number, docPath: string, docRootDir: string) {
	const data = await fs.readFile(path.resolve(docRootDir, docPath));

	const [{ id }] = await sql<[{ id: number }]>`INSERT INTO source_doc (source_id, file_name, file_data)
																							 VALUES (${sourceId}, ${docPath}, ${data})
																							 RETURNING id AS id`;

	return id;
}

async function clearWipData({ sourceName, holdingId: { rootId, env } }: SourceIdentifier) {
	const wipName = getWipName(sourceName);

	await sql`DELETE
						FROM source
						WHERE name = ${wipName}
							AND root_id = ${rootId}
							AND env = ${env}`;
}

async function savePageContent(
	sourceDocId: number,
	page: {
		idx: number;
		content: string;
		compressed: string;
		languages: string[];
		relevantFor: string[];
	},
) {
	await sql`INSERT INTO source_doc_page (source_doc_id, idx, languages, content, compressed, relevant_for)
						VALUES (${sourceDocId}, ${page.idx}, ${page.languages}, ${page.content}, ${page.compressed}, ${page.relevantFor})`;
}

/**
 * Make the wip source docs the current source docs
 */
async function replaceCurrentWithWip({ holdingId: { rootId, env }, sourceName }: SourceIdentifier) {
	// In one transaction, delete the current source docs and replace them with wip source docs
	await sql.begin(async (sql) => {
		await sql`DELETE
							FROM source
							WHERE name = ${sourceName}
								AND root_id = ${rootId}
								AND env = ${env}`;
		await sql`UPDATE source
							SET name = ${sourceName}
							WHERE name = ${getWipName(sourceName)}
								AND root_id = ${rootId}
								AND env = ${env}`;
	});
}

function getDocWorkDir(relativeDocFilePath: string, outputDir: string) {
	const docPathWithoutExt = relativeDocFilePath.replace(/\.pdf$/i, "").replace(/\.docx$/i, "");
	const pathComponents = docPathWithoutExt.split(path.sep).map((cmp) => cmp.replace(/[^a-zA-Z0-9\-_]/g, "_"));

	const targetDir = path.resolve(outputDir, ...pathComponents);
	return targetDir;
}

/**
 * Determine whether the page is relevant for identifying issues.
 * @param content
 */
export async function determinePageIdentifyIssuesRelevance(content: string) {
	const kpis = breedingKPIs;

	return await structuredCompletionChatGpt({
		zodObject: z.object({
			relevant: z.boolean().describe("Is the page relevant?"),
			explanation: z.string().describe("Explanation of why the page is or isn't relevant."),
		}),
		objectName: "relevancy",
		messages: [
			{
				role: "user",
				content: renderTemplateFile(path.resolve(__dirname, "determine-issues-identification-relevance.hbs"), { page: content, kpis }),
			},
		],
		metricData: {
			activity: activity,
			action: "determine-page-relevance",
		},
		cacheIdx: 0,
		maxToken: 4096,
	});
}

export async function determinePageKPIRelevance(content: string) {
	const allKpis = breedingKPIs;
	// Turn allKpis into an array of 20-sized arrays using fp-ts
	const chunkedKPIs = pipe(allKpis, A.chunksOf(50));

	const chunkedResponse = await Promise.all(
		chunkedKPIs.map((kpis) => {
			return structuredCompletionChatGpt({
				zodObject: z.object({
					kpis: z
						.array(
							z.object({
								code: z.string(),
								relevant: z.boolean().describe(`The documentation is relevant for a KPI if:
- The activities described on the page can help improve the results of the given KPI.
- The content of the page helps determine the cause of why a given KPI has poor results.
`),
								explanation: z.string(),
							}),
						)
						.describe("Each KPI in the list must have an entry here."),
				}),
				objectName: "kpis",
				messages: [
					{
						role: "user",
						content: renderTemplateFile(path.resolve(__dirname, "determine-kpi-relevance.hbs"), { page: content, kpis }),
					},
				],
				metricData: {
					activity: activity,
					action: "determine-page-relevance",
				},
				cacheIdx: 0,
				maxToken: 4096,
			});
		}),
	);

	const response = chunkedResponse
		.flatMap((responseWithUsage) => responseWithUsage.response.kpis)
		.filter((kpi) => kpi.relevant)
		.map((kpi) => kpi.code);

	return response;
}

async function createWipSource(source: SourceIdentifier) {
	const [{ id: sourceId }] = await sql<[{ id: number }]>`
		INSERT INTO source (name, root_id, env)
		VALUES (${getWipName(source.sourceName)}, ${source.holdingId.rootId}, ${source.holdingId.env})
		RETURNING id AS id`;
	return sourceId;
}

export interface LoadSourceDocsParams {
	source: SourceIdentifier;
	docDir: string;
	debugMode?: boolean;
	overwrite: boolean;
}

export async function getRelevancy(content: string) {
	const relevantKPIs = await determinePageKPIRelevance(content);
	const issueIdentificationRelevant = (await determinePageIdentifyIssuesRelevance(content)).response.relevant;

	return [...(issueIdentificationRelevant ? [ISSUE_IDENTIFICATION_STEP] : []), ...relevantKPIs];
}

export async function loadSourceDocs({ source, docDir, overwrite, debugMode = false }: LoadSourceDocsParams) {
	const { sourceName } = source;

	const relativeDocFilesPaths = await getDocFiles(docDir);
	const outputDir = path.resolve(Config.WORKER_DIR, sourceName);

	async function cleanup() {
		if (!debugMode) {
			await fs.rm(outputDir, { recursive: true, force: true });
		}
		await clearWipData(source);
	}

	await cleanup();

	const sourceId = await createWipSource(source);

	// We will process the files with parallelism equal to the number of cores
	const numCPUs = os.cpus().length;

	const existingSourceId = !overwrite
		? (
				await sql<[{ id: number }]>`
				SELECT id
				FROM source
				WHERE name = ${sourceName}
					AND root_id = ${source.holdingId.rootId}
					AND env = ${source.holdingId.env}`
			)?.[0]?.id
		: undefined;

	const relativeDocFilePathsToProcess = await (async () => {
		if (!existingSourceId) {
			return relativeDocFilesPaths;
		} else {
			const existingSourceDocs = await sql<{ file_name: string }[]>`
				SELECT file_name
				FROM source_doc
				WHERE source_id = ${existingSourceId}`;

			const existingDocFiles = existingSourceDocs.map((doc) => doc.file_name);

			return relativeDocFilesPaths.filter((doc) => !existingDocFiles.includes(doc));
		}
	})();

	try {
		await lastValueFrom(
			from(relativeDocFilePathsToProcess).pipe(
				mergeMap(async (relativeDocFilePath) => {
					const workDir = getDocWorkDir(relativeDocFilePath, outputDir);

					const [sourceDocId, filesContents] = await Promise.all([
						saveSourceDoc(sourceId, relativeDocFilePath, docDir),
						extractContent(path.resolve(docDir, relativeDocFilePath), workDir, debugMode),
					]);

					return from(filesContents).pipe(
						mergeMap(async (item) => {
							const languages = await getLanguages(item.content, "extract-language");
							const compressed = await textCompletionChatGpt({
								messages: [
									{
										role: "user",
										content: renderTemplateFile(path.resolve(__dirname, "compress-text.hbs"), { document: item.content }),
									},
								],
								metricData: { activity, action: "compress-text" },
								cacheIdx: 0,
								maxToken: 4096,
							});

							const relevantFor: string[] = await getRelevancy(item.content);

							if (debugMode) {
								// save the compressed text and the relevancy into files for debugging
								const nameWithoutExt = item.png.substring(0, item.png.length - 4);

								await saveStringToFile(compressed.response, path.resolve(workDir, `${nameWithoutExt}-compressed.md`));
								await saveStringToFile(JSON.stringify(relevantFor, null, 2), path.resolve(workDir, `${nameWithoutExt}-relevancy.md`));
							}

							return {
								...item,
								compressed: compressed.response,
								languages,
								relevantFor,
							};
						}),
						mergeMap(async (item) => {
							await savePageContent(sourceDocId, item);
						}),
					);
				}, numCPUs),
				mergeAll(),
			),
		);

		if (!existingSourceId) {
			await replaceCurrentWithWip(source);
		} else {
			// Move the source docs from the wip source to the current source
			await sql`
				UPDATE source_doc
				SET source_id = ${existingSourceId}
				WHERE source_id = ${sourceId}
			`;
		}
	} finally {
		await cleanup();
	}
}
