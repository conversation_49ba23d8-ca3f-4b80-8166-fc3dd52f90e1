Given the input text, return a JSON array of language codes representing the languages used in a significant portion of the text. Each
language must have at least one full sentence written in it to be included in the response. The output should be a JSON array with only the
language codes and no additional text. Example input: "Hello, how are you? <PERSON><PERSON><PERSON>, comment ça va? <PERSON><PERSON>, ¿cómo estás?" Example output:
["en", "fr", "es"] Input text:
{{text}}