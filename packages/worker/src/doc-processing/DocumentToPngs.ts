import * as fs from "fs-extra";
import * as path from "path";
import { Pop<PERSON> } from "node-poppler";
import { convert } from "libreoffice-convert";
import { promisify } from "util";

const convertAsync = promisify(convert);

// Function to convert PDF to PNG
const convertPDFToPNG = async (inputPath: string, outputDir: string, scalePageTo?: number): Promise<string[]> => {
	const poppler = new Poppler();
	const outputPrefix = path.join(outputDir, path.basename(inputPath, path.extname(inputPath)));

	await poppler.pdfToCairo(inputPath, outputPrefix, {
		pngFile: true,
		singleFile: false,
		scalePageTo: scalePageTo,
	});

	const files = await fs.readdir(outputDir);
	return files.filter((file) => file.endsWith(".png"));
};

// Function to convert Word to PDF
const convertWordToPDF = async (inputPath: string, outputPath: string): Promise<void> => {
	const docxBuf = await fs.readFile(inputPath);
	const pdfBuf = await convertAsync(docxBuf, ".pdf", undefined);
	await fs.writeFile(outputPath, pdfBuf);
};

// Main function to convert a file to PNG screenshots
export const convertFileToPNGScreenshots = async (inputPath: string, outputDir: string, scalePageTo?: number): Promise<string[]> => {
	await fs.ensureDir(outputDir);

	const ext = path.extname(inputPath).toLowerCase();

	if (ext === ".pdf") {
		return await convertPDFToPNG(inputPath, outputDir, scalePageTo);
	} else if (ext === ".docx" || ext === ".doc") {
		const tempPDFPath = path.join(outputDir, "temp.pdf");
		await convertWordToPDF(inputPath, tempPDFPath);
		const pngFiles = await convertPDFToPNG(tempPDFPath, outputDir, scalePageTo);
		await fs.remove(tempPDFPath);
		return pngFiles;
	} else {
		throw new Error("Unsupported file format");
	}
};
