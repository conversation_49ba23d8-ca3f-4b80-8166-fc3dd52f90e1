/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const webpack = require('webpack');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const dotenv = require('dotenv');

module.exports = (env, argv) => {
	const inDev = argv.mode !== 'production';

	const federationModuleName = process.env.FEDERATION_MODULE_NAME || 'virtualAssistant';

	const isServing = argv.env.SERVE;

	const envVariant = isServing ? 'development' : argv.mode;

	dotenv.config({
		path: [
			// Load env based on mode. Development is also loaded when serving prod files.
			path.resolve(__dirname, `.env.${envVariant}.local`),
			path.resolve(__dirname, `.env.${envVariant}`),
			path.resolve(__dirname, '.env'),
		],
	});
	const envVars = {
		GRAPHQL_URL: process.env.GRAPHQL_URL,
		BACKEND_BASE_URL: process.env.BACKEND_BASE_URL,
		ENABLE_WIP: process.env.ENABLE_WIP,
		BUILD_VERSION: argv.env.BUILD_VERSION,
		DEV_JWT_ENV: process.env.DEV_JWT_ENV,
		DEV_LANG: process.env.DEV_LANG,
	};

	return {
		entry: inDev ? ['./dev/main.tsx'] : ['./src/empty.ts'],
		module: {
			rules: [
				{
					// SWC loader for TypeScript and JavaScript
					test: /\.tsx?$/,
					exclude: /(node_modules|\.webpack)/,
					use: {
						loader: 'swc-loader',
						options: {
							jsc: {
								parser: {
									syntax: 'typescript',
									tsx: true,
								},
								transform: {
									react: {
										runtime: 'automatic',
										development: inDev,
										refresh: inDev,
									},
								},
							},
						},
					},
				},
				{
					// CSS Loader
					test: /\.css$/,
					use: [{ loader: 'style-loader' }, { loader: 'css-loader' }],
				},
				{
					// SCSS (SASS) Loader
					test: /\.s[ac]ss$/i,
					use: [{ loader: 'style-loader' }, { loader: 'css-loader' }, { loader: 'sass-loader' }],
				},
				{
					// Less loader
					test: /\.less$/,
					use: [
						{ loader: 'style-loader' },
						{ loader: 'css-loader' },
						{
							loader: 'less-loader',
							options: {
								lessOptions: {
									// antd compilation fails without this: https://github.com/ant-design/ant-motion/issues/44
									// TODO remove this when antd is updated
									javascriptEnabled: true,
									math: 'always',
								},
							},
						},
					],
				},
				{
					// Assets loader
					// More information here https://webpack.js.org/guides/asset-modules/
					test: /\.(gif|jpe?g|tiff|png|webp|bmp|svg|eot|ttf|woff|woff2)$/i,
					type: 'asset',
					generator: {
						filename: 'assets/[hash][ext][query]',
					},
				},
			],
		},
		output: {
			filename: inDev ? '[name].js' : '[name].[chunkhash].js',
			chunkFilename: inDev ? '[name].chunk.js' : '[name].[chunkhash].chunk.js',
			clean: !inDev,
		},
		plugins: [
			new ForkTsCheckerWebpackPlugin(),
			inDev && new ReactRefreshWebpackPlugin(),
			new webpack.DefinePlugin({
				'process.env': JSON.stringify(envVars),
			}),
			inDev &&
				new HtmlWebpackPlugin({
					template: 'dev/index.html',
					// favicon: 'assets/images/logo.png',
					inject: true,
				}),
			!inDev &&
				new ModuleFederationPlugin({
					name: federationModuleName,
					filename: 'remoteEntry.js',
					exposes: {
						'./VFAAdministration': './src/components/settings/SettingsRender.tsx',
						'./SupportChatbot': './src/components/supportbot/SupportChatbotExt.tsx',
						'./VFABeta': './src/components/vfamain/VFAMainRender.tsx',
					},
					shared: {
						antd: '3.26.18',
					},
				}),
		].filter(Boolean),
		resolve: {
			extensions: ['.js', '.ts', '.jsx', '.tsx', '.css'],
			alias: {
				'@assets': path.resolve(__dirname, 'assets/'),
				'@src': path.resolve(__dirname, 'src/'),
			},
		},
		stats: 'errors-warnings',
		devtool: inDev ? 'cheap-module-source-map' : false,
		devServer: {
			open: false,
			port: 3100,
			hot: inDev,
		},
		performance: {
			hints: inDev || isServing ? false : 'warning',
		},
	};
};
