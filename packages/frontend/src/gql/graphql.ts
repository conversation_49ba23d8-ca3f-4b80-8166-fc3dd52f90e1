/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  jsonb: { input: any; output: any; }
  timestamptz: { input: string; output: string; }
  uuid: { input: string; output: string; }
};

/** Boolean expression to compare columns of type "Int". All fields are combined with logical 'AND'. */
export type Int_Comparison_Exp = {
  _eq?: InputMaybe<Scalars['Int']['input']>;
  _gt?: InputMaybe<Scalars['Int']['input']>;
  _gte?: InputMaybe<Scalars['Int']['input']>;
  _in?: InputMaybe<Array<Scalars['Int']['input']>>;
  _is_null?: InputMaybe<Scalars['Boolean']['input']>;
  _lt?: InputMaybe<Scalars['Int']['input']>;
  _lte?: InputMaybe<Scalars['Int']['input']>;
  _neq?: InputMaybe<Scalars['Int']['input']>;
  _nin?: InputMaybe<Array<Scalars['Int']['input']>>;
};

/** Boolean expression to compare columns of type "String". All fields are combined with logical 'AND'. */
export type String_Comparison_Exp = {
  _eq?: InputMaybe<Scalars['String']['input']>;
  _gt?: InputMaybe<Scalars['String']['input']>;
  _gte?: InputMaybe<Scalars['String']['input']>;
  /** does the column match the given case-insensitive pattern */
  _ilike?: InputMaybe<Scalars['String']['input']>;
  _in?: InputMaybe<Array<Scalars['String']['input']>>;
  /** does the column match the given POSIX regular expression, case insensitive */
  _iregex?: InputMaybe<Scalars['String']['input']>;
  _is_null?: InputMaybe<Scalars['Boolean']['input']>;
  /** does the column match the given pattern */
  _like?: InputMaybe<Scalars['String']['input']>;
  _lt?: InputMaybe<Scalars['String']['input']>;
  _lte?: InputMaybe<Scalars['String']['input']>;
  _neq?: InputMaybe<Scalars['String']['input']>;
  /** does the column NOT match the given case-insensitive pattern */
  _nilike?: InputMaybe<Scalars['String']['input']>;
  _nin?: InputMaybe<Array<Scalars['String']['input']>>;
  /** does the column NOT match the given POSIX regular expression, case insensitive */
  _niregex?: InputMaybe<Scalars['String']['input']>;
  /** does the column NOT match the given pattern */
  _nlike?: InputMaybe<Scalars['String']['input']>;
  /** does the column NOT match the given POSIX regular expression, case sensitive */
  _nregex?: InputMaybe<Scalars['String']['input']>;
  /** does the column NOT match the given SQL regular expression */
  _nsimilar?: InputMaybe<Scalars['String']['input']>;
  /** does the column match the given POSIX regular expression, case sensitive */
  _regex?: InputMaybe<Scalars['String']['input']>;
  /** does the column match the given SQL regular expression */
  _similar?: InputMaybe<Scalars['String']['input']>;
};

/** columns and relationships of "active_template" */
export type Active_Template = {
  __typename?: 'active_template';
  /** An object relationship */
  prompt_template: Prompt_Template;
  root_id: Scalars['Int']['output'];
  template_id: Scalars['uuid']['output'];
  type: Scalars['String']['output'];
  update_by: Scalars['Int']['output'];
  updated_at: Scalars['timestamptz']['output'];
};

/** aggregated selection of "active_template" */
export type Active_Template_Aggregate = {
  __typename?: 'active_template_aggregate';
  aggregate?: Maybe<Active_Template_Aggregate_Fields>;
  nodes: Array<Active_Template>;
};

/** aggregate fields of "active_template" */
export type Active_Template_Aggregate_Fields = {
  __typename?: 'active_template_aggregate_fields';
  avg?: Maybe<Active_Template_Avg_Fields>;
  count: Scalars['Int']['output'];
  max?: Maybe<Active_Template_Max_Fields>;
  min?: Maybe<Active_Template_Min_Fields>;
  stddev?: Maybe<Active_Template_Stddev_Fields>;
  stddev_pop?: Maybe<Active_Template_Stddev_Pop_Fields>;
  stddev_samp?: Maybe<Active_Template_Stddev_Samp_Fields>;
  sum?: Maybe<Active_Template_Sum_Fields>;
  var_pop?: Maybe<Active_Template_Var_Pop_Fields>;
  var_samp?: Maybe<Active_Template_Var_Samp_Fields>;
  variance?: Maybe<Active_Template_Variance_Fields>;
};


/** aggregate fields of "active_template" */
export type Active_Template_Aggregate_FieldsCountArgs = {
  columns?: InputMaybe<Array<Active_Template_Select_Column>>;
  distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** aggregate avg on columns */
export type Active_Template_Avg_Fields = {
  __typename?: 'active_template_avg_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "active_template". All fields are combined with a logical 'AND'. */
export type Active_Template_Bool_Exp = {
  _and?: InputMaybe<Array<Active_Template_Bool_Exp>>;
  _not?: InputMaybe<Active_Template_Bool_Exp>;
  _or?: InputMaybe<Array<Active_Template_Bool_Exp>>;
  prompt_template?: InputMaybe<Prompt_Template_Bool_Exp>;
  root_id?: InputMaybe<Int_Comparison_Exp>;
  template_id?: InputMaybe<Uuid_Comparison_Exp>;
  type?: InputMaybe<String_Comparison_Exp>;
  update_by?: InputMaybe<Int_Comparison_Exp>;
  updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
};

/** unique or primary key constraints on table "active_template" */
export enum Active_Template_Constraint {
  /** unique or primary key constraint on columns "root_id", "type" */
  CurrentTemplatePkey = 'current_template_pkey'
}

/** input type for incrementing numeric columns in table "active_template" */
export type Active_Template_Inc_Input = {
  root_id?: InputMaybe<Scalars['Int']['input']>;
  update_by?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "active_template" */
export type Active_Template_Insert_Input = {
  prompt_template?: InputMaybe<Prompt_Template_Obj_Rel_Insert_Input>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template_id?: InputMaybe<Scalars['uuid']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  update_by?: InputMaybe<Scalars['Int']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** aggregate max on columns */
export type Active_Template_Max_Fields = {
  __typename?: 'active_template_max_fields';
  root_id?: Maybe<Scalars['Int']['output']>;
  template_id?: Maybe<Scalars['uuid']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  update_by?: Maybe<Scalars['Int']['output']>;
  updated_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** aggregate min on columns */
export type Active_Template_Min_Fields = {
  __typename?: 'active_template_min_fields';
  root_id?: Maybe<Scalars['Int']['output']>;
  template_id?: Maybe<Scalars['uuid']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  update_by?: Maybe<Scalars['Int']['output']>;
  updated_at?: Maybe<Scalars['timestamptz']['output']>;
};

/** response of any mutation on the table "active_template" */
export type Active_Template_Mutation_Response = {
  __typename?: 'active_template_mutation_response';
  /** number of rows affected by the mutation */
  affected_rows: Scalars['Int']['output'];
  /** data from the rows affected by the mutation */
  returning: Array<Active_Template>;
};

/** on_conflict condition type for table "active_template" */
export type Active_Template_On_Conflict = {
  constraint: Active_Template_Constraint;
  update_columns?: Array<Active_Template_Update_Column>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};

/** Ordering options when selecting data from "active_template". */
export type Active_Template_Order_By = {
  prompt_template?: InputMaybe<Prompt_Template_Order_By>;
  root_id?: InputMaybe<Order_By>;
  template_id?: InputMaybe<Order_By>;
  type?: InputMaybe<Order_By>;
  update_by?: InputMaybe<Order_By>;
  updated_at?: InputMaybe<Order_By>;
};

/** primary key columns input for table: active_template */
export type Active_Template_Pk_Columns_Input = {
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
};

/** select columns of table "active_template" */
export enum Active_Template_Select_Column {
  /** column name */
  RootId = 'root_id',
  /** column name */
  TemplateId = 'template_id',
  /** column name */
  Type = 'type',
  /** column name */
  UpdateBy = 'update_by',
  /** column name */
  UpdatedAt = 'updated_at'
}

/** input type for updating data in table "active_template" */
export type Active_Template_Set_Input = {
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template_id?: InputMaybe<Scalars['uuid']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  update_by?: InputMaybe<Scalars['Int']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** aggregate stddev on columns */
export type Active_Template_Stddev_Fields = {
  __typename?: 'active_template_stddev_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Active_Template_Stddev_Pop_Fields = {
  __typename?: 'active_template_stddev_pop_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Active_Template_Stddev_Samp_Fields = {
  __typename?: 'active_template_stddev_samp_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "active_template" */
export type Active_Template_Stream_Cursor_Input = {
  /** Stream column input with initial value */
  initial_value: Active_Template_Stream_Cursor_Value_Input;
  /** cursor ordering */
  ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Active_Template_Stream_Cursor_Value_Input = {
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template_id?: InputMaybe<Scalars['uuid']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  update_by?: InputMaybe<Scalars['Int']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
};

/** aggregate sum on columns */
export type Active_Template_Sum_Fields = {
  __typename?: 'active_template_sum_fields';
  root_id?: Maybe<Scalars['Int']['output']>;
  update_by?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "active_template" */
export enum Active_Template_Update_Column {
  /** column name */
  RootId = 'root_id',
  /** column name */
  TemplateId = 'template_id',
  /** column name */
  Type = 'type',
  /** column name */
  UpdateBy = 'update_by',
  /** column name */
  UpdatedAt = 'updated_at'
}

export type Active_Template_Updates = {
  /** increments the numeric columns with given value of the filtered values */
  _inc?: InputMaybe<Active_Template_Inc_Input>;
  /** sets the columns of the filtered rows to the given values */
  _set?: InputMaybe<Active_Template_Set_Input>;
  /** filter the rows which have to be updated */
  where: Active_Template_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Active_Template_Var_Pop_Fields = {
  __typename?: 'active_template_var_pop_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Active_Template_Var_Samp_Fields = {
  __typename?: 'active_template_var_samp_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Active_Template_Variance_Fields = {
  __typename?: 'active_template_variance_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
  update_by?: Maybe<Scalars['Float']['output']>;
};

/** ordering argument of a cursor */
export enum Cursor_Ordering {
  /** ascending ordering of the cursor */
  Asc = 'ASC',
  /** descending ordering of the cursor */
  Desc = 'DESC'
}

export type Jsonb_Cast_Exp = {
  String?: InputMaybe<String_Comparison_Exp>;
};

/** Boolean expression to compare columns of type "jsonb". All fields are combined with logical 'AND'. */
export type Jsonb_Comparison_Exp = {
  _cast?: InputMaybe<Jsonb_Cast_Exp>;
  /** is the column contained in the given json value */
  _contained_in?: InputMaybe<Scalars['jsonb']['input']>;
  /** does the column contain the given json value at the top level */
  _contains?: InputMaybe<Scalars['jsonb']['input']>;
  _eq?: InputMaybe<Scalars['jsonb']['input']>;
  _gt?: InputMaybe<Scalars['jsonb']['input']>;
  _gte?: InputMaybe<Scalars['jsonb']['input']>;
  /** does the string exist as a top-level key in the column */
  _has_key?: InputMaybe<Scalars['String']['input']>;
  /** do all of these strings exist as top-level keys in the column */
  _has_keys_all?: InputMaybe<Array<Scalars['String']['input']>>;
  /** do any of these strings exist as top-level keys in the column */
  _has_keys_any?: InputMaybe<Array<Scalars['String']['input']>>;
  _in?: InputMaybe<Array<Scalars['jsonb']['input']>>;
  _is_null?: InputMaybe<Scalars['Boolean']['input']>;
  _lt?: InputMaybe<Scalars['jsonb']['input']>;
  _lte?: InputMaybe<Scalars['jsonb']['input']>;
  _neq?: InputMaybe<Scalars['jsonb']['input']>;
  _nin?: InputMaybe<Array<Scalars['jsonb']['input']>>;
};

/** mutation root */
export type Mutation_Root = {
  __typename?: 'mutation_root';
  /** delete data from the table: "active_template" */
  delete_active_template?: Maybe<Active_Template_Mutation_Response>;
  /** delete single row from the table: "active_template" */
  delete_active_template_by_pk?: Maybe<Active_Template>;
  /** delete data from the table: "prompt_template" */
  delete_prompt_template?: Maybe<Prompt_Template_Mutation_Response>;
  /** delete single row from the table: "prompt_template" */
  delete_prompt_template_by_pk?: Maybe<Prompt_Template>;
  /** delete data from the table: "report_request" */
  delete_report_request?: Maybe<Report_Request_Mutation_Response>;
  /** delete data from the table: "response_feedback" */
  delete_response_feedback?: Maybe<Response_Feedback_Mutation_Response>;
  /** delete single row from the table: "response_feedback" */
  delete_response_feedback_by_pk?: Maybe<Response_Feedback>;
  /** delete data from the table: "setting" */
  delete_setting?: Maybe<Setting_Mutation_Response>;
  /** delete single row from the table: "setting" */
  delete_setting_by_pk?: Maybe<Setting>;
  /** insert data into the table: "active_template" */
  insert_active_template?: Maybe<Active_Template_Mutation_Response>;
  /** insert a single row into the table: "active_template" */
  insert_active_template_one?: Maybe<Active_Template>;
  /** insert data into the table: "prompt_template" */
  insert_prompt_template?: Maybe<Prompt_Template_Mutation_Response>;
  /** insert a single row into the table: "prompt_template" */
  insert_prompt_template_one?: Maybe<Prompt_Template>;
  /** insert data into the table: "report_request" */
  insert_report_request?: Maybe<Report_Request_Mutation_Response>;
  /** insert a single row into the table: "report_request" */
  insert_report_request_one?: Maybe<Report_Request>;
  /** insert data into the table: "response_feedback" */
  insert_response_feedback?: Maybe<Response_Feedback_Mutation_Response>;
  /** insert a single row into the table: "response_feedback" */
  insert_response_feedback_one?: Maybe<Response_Feedback>;
  /** insert data into the table: "setting" */
  insert_setting?: Maybe<Setting_Mutation_Response>;
  /** insert a single row into the table: "setting" */
  insert_setting_one?: Maybe<Setting>;
  /** update data of the table: "active_template" */
  update_active_template?: Maybe<Active_Template_Mutation_Response>;
  /** update single row of the table: "active_template" */
  update_active_template_by_pk?: Maybe<Active_Template>;
  /** update multiples rows of table: "active_template" */
  update_active_template_many?: Maybe<Array<Maybe<Active_Template_Mutation_Response>>>;
  /** update data of the table: "prompt_template" */
  update_prompt_template?: Maybe<Prompt_Template_Mutation_Response>;
  /** update single row of the table: "prompt_template" */
  update_prompt_template_by_pk?: Maybe<Prompt_Template>;
  /** update multiples rows of table: "prompt_template" */
  update_prompt_template_many?: Maybe<Array<Maybe<Prompt_Template_Mutation_Response>>>;
  /** update data of the table: "report_request" */
  update_report_request?: Maybe<Report_Request_Mutation_Response>;
  /** update multiples rows of table: "report_request" */
  update_report_request_many?: Maybe<Array<Maybe<Report_Request_Mutation_Response>>>;
  /** update data of the table: "response_feedback" */
  update_response_feedback?: Maybe<Response_Feedback_Mutation_Response>;
  /** update single row of the table: "response_feedback" */
  update_response_feedback_by_pk?: Maybe<Response_Feedback>;
  /** update multiples rows of table: "response_feedback" */
  update_response_feedback_many?: Maybe<Array<Maybe<Response_Feedback_Mutation_Response>>>;
  /** update data of the table: "setting" */
  update_setting?: Maybe<Setting_Mutation_Response>;
  /** update single row of the table: "setting" */
  update_setting_by_pk?: Maybe<Setting>;
  /** update multiples rows of table: "setting" */
  update_setting_many?: Maybe<Array<Maybe<Setting_Mutation_Response>>>;
};


/** mutation root */
export type Mutation_RootDelete_Active_TemplateArgs = {
  where: Active_Template_Bool_Exp;
};


/** mutation root */
export type Mutation_RootDelete_Active_Template_By_PkArgs = {
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
};


/** mutation root */
export type Mutation_RootDelete_Prompt_TemplateArgs = {
  where: Prompt_Template_Bool_Exp;
};


/** mutation root */
export type Mutation_RootDelete_Prompt_Template_By_PkArgs = {
  id: Scalars['uuid']['input'];
};


/** mutation root */
export type Mutation_RootDelete_Report_RequestArgs = {
  where: Report_Request_Bool_Exp;
};


/** mutation root */
export type Mutation_RootDelete_Response_FeedbackArgs = {
  where: Response_Feedback_Bool_Exp;
};


/** mutation root */
export type Mutation_RootDelete_Response_Feedback_By_PkArgs = {
  id: Scalars['String']['input'];
};


/** mutation root */
export type Mutation_RootDelete_SettingArgs = {
  where: Setting_Bool_Exp;
};


/** mutation root */
export type Mutation_RootDelete_Setting_By_PkArgs = {
  env: Scalars['String']['input'];
  root_id: Scalars['Int']['input'];
};


/** mutation root */
export type Mutation_RootInsert_Active_TemplateArgs = {
  objects: Array<Active_Template_Insert_Input>;
  on_conflict?: InputMaybe<Active_Template_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Active_Template_OneArgs = {
  object: Active_Template_Insert_Input;
  on_conflict?: InputMaybe<Active_Template_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Prompt_TemplateArgs = {
  objects: Array<Prompt_Template_Insert_Input>;
  on_conflict?: InputMaybe<Prompt_Template_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Prompt_Template_OneArgs = {
  object: Prompt_Template_Insert_Input;
  on_conflict?: InputMaybe<Prompt_Template_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Report_RequestArgs = {
  objects: Array<Report_Request_Insert_Input>;
};


/** mutation root */
export type Mutation_RootInsert_Report_Request_OneArgs = {
  object: Report_Request_Insert_Input;
};


/** mutation root */
export type Mutation_RootInsert_Response_FeedbackArgs = {
  objects: Array<Response_Feedback_Insert_Input>;
  on_conflict?: InputMaybe<Response_Feedback_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Response_Feedback_OneArgs = {
  object: Response_Feedback_Insert_Input;
  on_conflict?: InputMaybe<Response_Feedback_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_SettingArgs = {
  objects: Array<Setting_Insert_Input>;
  on_conflict?: InputMaybe<Setting_On_Conflict>;
};


/** mutation root */
export type Mutation_RootInsert_Setting_OneArgs = {
  object: Setting_Insert_Input;
  on_conflict?: InputMaybe<Setting_On_Conflict>;
};


/** mutation root */
export type Mutation_RootUpdate_Active_TemplateArgs = {
  _inc?: InputMaybe<Active_Template_Inc_Input>;
  _set?: InputMaybe<Active_Template_Set_Input>;
  where: Active_Template_Bool_Exp;
};


/** mutation root */
export type Mutation_RootUpdate_Active_Template_By_PkArgs = {
  _inc?: InputMaybe<Active_Template_Inc_Input>;
  _set?: InputMaybe<Active_Template_Set_Input>;
  pk_columns: Active_Template_Pk_Columns_Input;
};


/** mutation root */
export type Mutation_RootUpdate_Active_Template_ManyArgs = {
  updates: Array<Active_Template_Updates>;
};


/** mutation root */
export type Mutation_RootUpdate_Prompt_TemplateArgs = {
  _inc?: InputMaybe<Prompt_Template_Inc_Input>;
  _set?: InputMaybe<Prompt_Template_Set_Input>;
  where: Prompt_Template_Bool_Exp;
};


/** mutation root */
export type Mutation_RootUpdate_Prompt_Template_By_PkArgs = {
  _inc?: InputMaybe<Prompt_Template_Inc_Input>;
  _set?: InputMaybe<Prompt_Template_Set_Input>;
  pk_columns: Prompt_Template_Pk_Columns_Input;
};


/** mutation root */
export type Mutation_RootUpdate_Prompt_Template_ManyArgs = {
  updates: Array<Prompt_Template_Updates>;
};


/** mutation root */
export type Mutation_RootUpdate_Report_RequestArgs = {
  _append?: InputMaybe<Report_Request_Append_Input>;
  _delete_at_path?: InputMaybe<Report_Request_Delete_At_Path_Input>;
  _delete_elem?: InputMaybe<Report_Request_Delete_Elem_Input>;
  _delete_key?: InputMaybe<Report_Request_Delete_Key_Input>;
  _inc?: InputMaybe<Report_Request_Inc_Input>;
  _prepend?: InputMaybe<Report_Request_Prepend_Input>;
  _set?: InputMaybe<Report_Request_Set_Input>;
  where: Report_Request_Bool_Exp;
};


/** mutation root */
export type Mutation_RootUpdate_Report_Request_ManyArgs = {
  updates: Array<Report_Request_Updates>;
};


/** mutation root */
export type Mutation_RootUpdate_Response_FeedbackArgs = {
  _append?: InputMaybe<Response_Feedback_Append_Input>;
  _delete_at_path?: InputMaybe<Response_Feedback_Delete_At_Path_Input>;
  _delete_elem?: InputMaybe<Response_Feedback_Delete_Elem_Input>;
  _delete_key?: InputMaybe<Response_Feedback_Delete_Key_Input>;
  _inc?: InputMaybe<Response_Feedback_Inc_Input>;
  _prepend?: InputMaybe<Response_Feedback_Prepend_Input>;
  _set?: InputMaybe<Response_Feedback_Set_Input>;
  where: Response_Feedback_Bool_Exp;
};


/** mutation root */
export type Mutation_RootUpdate_Response_Feedback_By_PkArgs = {
  _append?: InputMaybe<Response_Feedback_Append_Input>;
  _delete_at_path?: InputMaybe<Response_Feedback_Delete_At_Path_Input>;
  _delete_elem?: InputMaybe<Response_Feedback_Delete_Elem_Input>;
  _delete_key?: InputMaybe<Response_Feedback_Delete_Key_Input>;
  _inc?: InputMaybe<Response_Feedback_Inc_Input>;
  _prepend?: InputMaybe<Response_Feedback_Prepend_Input>;
  _set?: InputMaybe<Response_Feedback_Set_Input>;
  pk_columns: Response_Feedback_Pk_Columns_Input;
};


/** mutation root */
export type Mutation_RootUpdate_Response_Feedback_ManyArgs = {
  updates: Array<Response_Feedback_Updates>;
};


/** mutation root */
export type Mutation_RootUpdate_SettingArgs = {
  _append?: InputMaybe<Setting_Append_Input>;
  _delete_at_path?: InputMaybe<Setting_Delete_At_Path_Input>;
  _delete_elem?: InputMaybe<Setting_Delete_Elem_Input>;
  _delete_key?: InputMaybe<Setting_Delete_Key_Input>;
  _inc?: InputMaybe<Setting_Inc_Input>;
  _prepend?: InputMaybe<Setting_Prepend_Input>;
  _set?: InputMaybe<Setting_Set_Input>;
  where: Setting_Bool_Exp;
};


/** mutation root */
export type Mutation_RootUpdate_Setting_By_PkArgs = {
  _append?: InputMaybe<Setting_Append_Input>;
  _delete_at_path?: InputMaybe<Setting_Delete_At_Path_Input>;
  _delete_elem?: InputMaybe<Setting_Delete_Elem_Input>;
  _delete_key?: InputMaybe<Setting_Delete_Key_Input>;
  _inc?: InputMaybe<Setting_Inc_Input>;
  _prepend?: InputMaybe<Setting_Prepend_Input>;
  _set?: InputMaybe<Setting_Set_Input>;
  pk_columns: Setting_Pk_Columns_Input;
};


/** mutation root */
export type Mutation_RootUpdate_Setting_ManyArgs = {
  updates: Array<Setting_Updates>;
};

/** column ordering options */
export enum Order_By {
  /** in ascending order, nulls last */
  Asc = 'asc',
  /** in ascending order, nulls first */
  AscNullsFirst = 'asc_nulls_first',
  /** in ascending order, nulls last */
  AscNullsLast = 'asc_nulls_last',
  /** in descending order, nulls first */
  Desc = 'desc',
  /** in descending order, nulls first */
  DescNullsFirst = 'desc_nulls_first',
  /** in descending order, nulls last */
  DescNullsLast = 'desc_nulls_last'
}

/** columns and relationships of "prompt_template" */
export type Prompt_Template = {
  __typename?: 'prompt_template';
  created_at: Scalars['timestamptz']['output'];
  created_by: Scalars['Int']['output'];
  id: Scalars['uuid']['output'];
  root_id: Scalars['Int']['output'];
  template: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updated_at: Scalars['timestamptz']['output'];
  updated_by: Scalars['Int']['output'];
};

/** aggregated selection of "prompt_template" */
export type Prompt_Template_Aggregate = {
  __typename?: 'prompt_template_aggregate';
  aggregate?: Maybe<Prompt_Template_Aggregate_Fields>;
  nodes: Array<Prompt_Template>;
};

/** aggregate fields of "prompt_template" */
export type Prompt_Template_Aggregate_Fields = {
  __typename?: 'prompt_template_aggregate_fields';
  avg?: Maybe<Prompt_Template_Avg_Fields>;
  count: Scalars['Int']['output'];
  max?: Maybe<Prompt_Template_Max_Fields>;
  min?: Maybe<Prompt_Template_Min_Fields>;
  stddev?: Maybe<Prompt_Template_Stddev_Fields>;
  stddev_pop?: Maybe<Prompt_Template_Stddev_Pop_Fields>;
  stddev_samp?: Maybe<Prompt_Template_Stddev_Samp_Fields>;
  sum?: Maybe<Prompt_Template_Sum_Fields>;
  var_pop?: Maybe<Prompt_Template_Var_Pop_Fields>;
  var_samp?: Maybe<Prompt_Template_Var_Samp_Fields>;
  variance?: Maybe<Prompt_Template_Variance_Fields>;
};


/** aggregate fields of "prompt_template" */
export type Prompt_Template_Aggregate_FieldsCountArgs = {
  columns?: InputMaybe<Array<Prompt_Template_Select_Column>>;
  distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** aggregate avg on columns */
export type Prompt_Template_Avg_Fields = {
  __typename?: 'prompt_template_avg_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "prompt_template". All fields are combined with a logical 'AND'. */
export type Prompt_Template_Bool_Exp = {
  _and?: InputMaybe<Array<Prompt_Template_Bool_Exp>>;
  _not?: InputMaybe<Prompt_Template_Bool_Exp>;
  _or?: InputMaybe<Array<Prompt_Template_Bool_Exp>>;
  created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
  created_by?: InputMaybe<Int_Comparison_Exp>;
  id?: InputMaybe<Uuid_Comparison_Exp>;
  root_id?: InputMaybe<Int_Comparison_Exp>;
  template?: InputMaybe<String_Comparison_Exp>;
  type?: InputMaybe<String_Comparison_Exp>;
  updated_at?: InputMaybe<Timestamptz_Comparison_Exp>;
  updated_by?: InputMaybe<Int_Comparison_Exp>;
};

/** unique or primary key constraints on table "prompt_template" */
export enum Prompt_Template_Constraint {
  /** unique or primary key constraint on columns "id" */
  PromptTemplatePkey = 'prompt_template_pkey'
}

/** input type for incrementing numeric columns in table "prompt_template" */
export type Prompt_Template_Inc_Input = {
  created_by?: InputMaybe<Scalars['Int']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  updated_by?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "prompt_template" */
export type Prompt_Template_Insert_Input = {
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['uuid']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
  updated_by?: InputMaybe<Scalars['Int']['input']>;
};

/** aggregate max on columns */
export type Prompt_Template_Max_Fields = {
  __typename?: 'prompt_template_max_fields';
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  created_by?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['uuid']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  template?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['timestamptz']['output']>;
  updated_by?: Maybe<Scalars['Int']['output']>;
};

/** aggregate min on columns */
export type Prompt_Template_Min_Fields = {
  __typename?: 'prompt_template_min_fields';
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  created_by?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['uuid']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  template?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updated_at?: Maybe<Scalars['timestamptz']['output']>;
  updated_by?: Maybe<Scalars['Int']['output']>;
};

/** response of any mutation on the table "prompt_template" */
export type Prompt_Template_Mutation_Response = {
  __typename?: 'prompt_template_mutation_response';
  /** number of rows affected by the mutation */
  affected_rows: Scalars['Int']['output'];
  /** data from the rows affected by the mutation */
  returning: Array<Prompt_Template>;
};

/** input type for inserting object relation for remote table "prompt_template" */
export type Prompt_Template_Obj_Rel_Insert_Input = {
  data: Prompt_Template_Insert_Input;
  /** upsert condition */
  on_conflict?: InputMaybe<Prompt_Template_On_Conflict>;
};

/** on_conflict condition type for table "prompt_template" */
export type Prompt_Template_On_Conflict = {
  constraint: Prompt_Template_Constraint;
  update_columns?: Array<Prompt_Template_Update_Column>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};

/** Ordering options when selecting data from "prompt_template". */
export type Prompt_Template_Order_By = {
  created_at?: InputMaybe<Order_By>;
  created_by?: InputMaybe<Order_By>;
  id?: InputMaybe<Order_By>;
  root_id?: InputMaybe<Order_By>;
  template?: InputMaybe<Order_By>;
  type?: InputMaybe<Order_By>;
  updated_at?: InputMaybe<Order_By>;
  updated_by?: InputMaybe<Order_By>;
};

/** primary key columns input for table: prompt_template */
export type Prompt_Template_Pk_Columns_Input = {
  id: Scalars['uuid']['input'];
};

/** select columns of table "prompt_template" */
export enum Prompt_Template_Select_Column {
  /** column name */
  CreatedAt = 'created_at',
  /** column name */
  CreatedBy = 'created_by',
  /** column name */
  Id = 'id',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Template = 'template',
  /** column name */
  Type = 'type',
  /** column name */
  UpdatedAt = 'updated_at',
  /** column name */
  UpdatedBy = 'updated_by'
}

/** input type for updating data in table "prompt_template" */
export type Prompt_Template_Set_Input = {
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['uuid']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
  updated_by?: InputMaybe<Scalars['Int']['input']>;
};

/** aggregate stddev on columns */
export type Prompt_Template_Stddev_Fields = {
  __typename?: 'prompt_template_stddev_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Prompt_Template_Stddev_Pop_Fields = {
  __typename?: 'prompt_template_stddev_pop_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Prompt_Template_Stddev_Samp_Fields = {
  __typename?: 'prompt_template_stddev_samp_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "prompt_template" */
export type Prompt_Template_Stream_Cursor_Input = {
  /** Stream column input with initial value */
  initial_value: Prompt_Template_Stream_Cursor_Value_Input;
  /** cursor ordering */
  ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Prompt_Template_Stream_Cursor_Value_Input = {
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['uuid']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  updated_at?: InputMaybe<Scalars['timestamptz']['input']>;
  updated_by?: InputMaybe<Scalars['Int']['input']>;
};

/** aggregate sum on columns */
export type Prompt_Template_Sum_Fields = {
  __typename?: 'prompt_template_sum_fields';
  created_by?: Maybe<Scalars['Int']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  updated_by?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "prompt_template" */
export enum Prompt_Template_Update_Column {
  /** column name */
  CreatedAt = 'created_at',
  /** column name */
  CreatedBy = 'created_by',
  /** column name */
  Id = 'id',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Template = 'template',
  /** column name */
  Type = 'type',
  /** column name */
  UpdatedAt = 'updated_at',
  /** column name */
  UpdatedBy = 'updated_by'
}

export type Prompt_Template_Updates = {
  /** increments the numeric columns with given value of the filtered values */
  _inc?: InputMaybe<Prompt_Template_Inc_Input>;
  /** sets the columns of the filtered rows to the given values */
  _set?: InputMaybe<Prompt_Template_Set_Input>;
  /** filter the rows which have to be updated */
  where: Prompt_Template_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Prompt_Template_Var_Pop_Fields = {
  __typename?: 'prompt_template_var_pop_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Prompt_Template_Var_Samp_Fields = {
  __typename?: 'prompt_template_var_samp_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Prompt_Template_Variance_Fields = {
  __typename?: 'prompt_template_variance_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
  updated_by?: Maybe<Scalars['Float']['output']>;
};

export type Query_Root = {
  __typename?: 'query_root';
  /** fetch data from the table: "active_template" */
  active_template: Array<Active_Template>;
  /** fetch aggregated fields from the table: "active_template" */
  active_template_aggregate: Active_Template_Aggregate;
  /** fetch data from the table: "active_template" using primary key columns */
  active_template_by_pk?: Maybe<Active_Template>;
  /** fetch data from the table: "prompt_template" */
  prompt_template: Array<Prompt_Template>;
  /** fetch aggregated fields from the table: "prompt_template" */
  prompt_template_aggregate: Prompt_Template_Aggregate;
  /** fetch data from the table: "prompt_template" using primary key columns */
  prompt_template_by_pk?: Maybe<Prompt_Template>;
  /** fetch data from the table: "report_request" */
  report_request: Array<Report_Request>;
  /** fetch aggregated fields from the table: "report_request" */
  report_request_aggregate: Report_Request_Aggregate;
  /** fetch data from the table: "response_feedback" */
  response_feedback: Array<Response_Feedback>;
  /** fetch aggregated fields from the table: "response_feedback" */
  response_feedback_aggregate: Response_Feedback_Aggregate;
  /** fetch data from the table: "response_feedback" using primary key columns */
  response_feedback_by_pk?: Maybe<Response_Feedback>;
  /** fetch data from the table: "setting" */
  setting: Array<Setting>;
  /** fetch aggregated fields from the table: "setting" */
  setting_aggregate: Setting_Aggregate;
  /** fetch data from the table: "setting" using primary key columns */
  setting_by_pk?: Maybe<Setting>;
};


export type Query_RootActive_TemplateArgs = {
  distinct_on?: InputMaybe<Array<Active_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Active_Template_Order_By>>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};


export type Query_RootActive_Template_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Active_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Active_Template_Order_By>>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};


export type Query_RootActive_Template_By_PkArgs = {
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
};


export type Query_RootPrompt_TemplateArgs = {
  distinct_on?: InputMaybe<Array<Prompt_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Prompt_Template_Order_By>>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};


export type Query_RootPrompt_Template_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Prompt_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Prompt_Template_Order_By>>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};


export type Query_RootPrompt_Template_By_PkArgs = {
  id: Scalars['uuid']['input'];
};


export type Query_RootReport_RequestArgs = {
  distinct_on?: InputMaybe<Array<Report_Request_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Report_Request_Order_By>>;
  where?: InputMaybe<Report_Request_Bool_Exp>;
};


export type Query_RootReport_Request_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Report_Request_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Report_Request_Order_By>>;
  where?: InputMaybe<Report_Request_Bool_Exp>;
};


export type Query_RootResponse_FeedbackArgs = {
  distinct_on?: InputMaybe<Array<Response_Feedback_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Response_Feedback_Order_By>>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};


export type Query_RootResponse_Feedback_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Response_Feedback_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Response_Feedback_Order_By>>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};


export type Query_RootResponse_Feedback_By_PkArgs = {
  id: Scalars['String']['input'];
};


export type Query_RootSettingArgs = {
  distinct_on?: InputMaybe<Array<Setting_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Setting_Order_By>>;
  where?: InputMaybe<Setting_Bool_Exp>;
};


export type Query_RootSetting_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Setting_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Setting_Order_By>>;
  where?: InputMaybe<Setting_Bool_Exp>;
};


export type Query_RootSetting_By_PkArgs = {
  env: Scalars['String']['input'];
  root_id: Scalars['Int']['input'];
};

/** columns and relationships of "report_request" */
export type Report_Request = {
  __typename?: 'report_request';
  country: Scalars['String']['output'];
  created_at: Scalars['timestamptz']['output'];
  created_by: Scalars['Int']['output'];
  farm_id: Scalars['Int']['output'];
  farm_name: Scalars['String']['output'];
  report_data: Scalars['jsonb']['output'];
  root_id: Scalars['Int']['output'];
  type: Scalars['String']['output'];
};


/** columns and relationships of "report_request" */
export type Report_RequestReport_DataArgs = {
  path?: InputMaybe<Scalars['String']['input']>;
};

/** aggregated selection of "report_request" */
export type Report_Request_Aggregate = {
  __typename?: 'report_request_aggregate';
  aggregate?: Maybe<Report_Request_Aggregate_Fields>;
  nodes: Array<Report_Request>;
};

/** aggregate fields of "report_request" */
export type Report_Request_Aggregate_Fields = {
  __typename?: 'report_request_aggregate_fields';
  avg?: Maybe<Report_Request_Avg_Fields>;
  count: Scalars['Int']['output'];
  max?: Maybe<Report_Request_Max_Fields>;
  min?: Maybe<Report_Request_Min_Fields>;
  stddev?: Maybe<Report_Request_Stddev_Fields>;
  stddev_pop?: Maybe<Report_Request_Stddev_Pop_Fields>;
  stddev_samp?: Maybe<Report_Request_Stddev_Samp_Fields>;
  sum?: Maybe<Report_Request_Sum_Fields>;
  var_pop?: Maybe<Report_Request_Var_Pop_Fields>;
  var_samp?: Maybe<Report_Request_Var_Samp_Fields>;
  variance?: Maybe<Report_Request_Variance_Fields>;
};


/** aggregate fields of "report_request" */
export type Report_Request_Aggregate_FieldsCountArgs = {
  columns?: InputMaybe<Array<Report_Request_Select_Column>>;
  distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Report_Request_Append_Input = {
  report_data?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate avg on columns */
export type Report_Request_Avg_Fields = {
  __typename?: 'report_request_avg_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "report_request". All fields are combined with a logical 'AND'. */
export type Report_Request_Bool_Exp = {
  _and?: InputMaybe<Array<Report_Request_Bool_Exp>>;
  _not?: InputMaybe<Report_Request_Bool_Exp>;
  _or?: InputMaybe<Array<Report_Request_Bool_Exp>>;
  country?: InputMaybe<String_Comparison_Exp>;
  created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
  created_by?: InputMaybe<Int_Comparison_Exp>;
  farm_id?: InputMaybe<Int_Comparison_Exp>;
  farm_name?: InputMaybe<String_Comparison_Exp>;
  report_data?: InputMaybe<Jsonb_Comparison_Exp>;
  root_id?: InputMaybe<Int_Comparison_Exp>;
  type?: InputMaybe<String_Comparison_Exp>;
};

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Report_Request_Delete_At_Path_Input = {
  report_data?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Report_Request_Delete_Elem_Input = {
  report_data?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Report_Request_Delete_Key_Input = {
  report_data?: InputMaybe<Scalars['String']['input']>;
};

/** input type for incrementing numeric columns in table "report_request" */
export type Report_Request_Inc_Input = {
  created_by?: InputMaybe<Scalars['Int']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "report_request" */
export type Report_Request_Insert_Input = {
  country?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  report_data?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Report_Request_Max_Fields = {
  __typename?: 'report_request_max_fields';
  country?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  created_by?: Maybe<Scalars['Int']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  farm_name?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Report_Request_Min_Fields = {
  __typename?: 'report_request_min_fields';
  country?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  created_by?: Maybe<Scalars['Int']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  farm_name?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "report_request" */
export type Report_Request_Mutation_Response = {
  __typename?: 'report_request_mutation_response';
  /** number of rows affected by the mutation */
  affected_rows: Scalars['Int']['output'];
  /** data from the rows affected by the mutation */
  returning: Array<Report_Request>;
};

/** Ordering options when selecting data from "report_request". */
export type Report_Request_Order_By = {
  country?: InputMaybe<Order_By>;
  created_at?: InputMaybe<Order_By>;
  created_by?: InputMaybe<Order_By>;
  farm_id?: InputMaybe<Order_By>;
  farm_name?: InputMaybe<Order_By>;
  report_data?: InputMaybe<Order_By>;
  root_id?: InputMaybe<Order_By>;
  type?: InputMaybe<Order_By>;
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Report_Request_Prepend_Input = {
  report_data?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "report_request" */
export enum Report_Request_Select_Column {
  /** column name */
  Country = 'country',
  /** column name */
  CreatedAt = 'created_at',
  /** column name */
  CreatedBy = 'created_by',
  /** column name */
  FarmId = 'farm_id',
  /** column name */
  FarmName = 'farm_name',
  /** column name */
  ReportData = 'report_data',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Type = 'type'
}

/** input type for updating data in table "report_request" */
export type Report_Request_Set_Input = {
  country?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  report_data?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate stddev on columns */
export type Report_Request_Stddev_Fields = {
  __typename?: 'report_request_stddev_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Report_Request_Stddev_Pop_Fields = {
  __typename?: 'report_request_stddev_pop_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Report_Request_Stddev_Samp_Fields = {
  __typename?: 'report_request_stddev_samp_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "report_request" */
export type Report_Request_Stream_Cursor_Input = {
  /** Stream column input with initial value */
  initial_value: Report_Request_Stream_Cursor_Value_Input;
  /** cursor ordering */
  ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Report_Request_Stream_Cursor_Value_Input = {
  country?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  created_by?: InputMaybe<Scalars['Int']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  report_data?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate sum on columns */
export type Report_Request_Sum_Fields = {
  __typename?: 'report_request_sum_fields';
  created_by?: Maybe<Scalars['Int']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
};

export type Report_Request_Updates = {
  /** append existing jsonb value of filtered columns with new jsonb value */
  _append?: InputMaybe<Report_Request_Append_Input>;
  /** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
  _delete_at_path?: InputMaybe<Report_Request_Delete_At_Path_Input>;
  /** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
  _delete_elem?: InputMaybe<Report_Request_Delete_Elem_Input>;
  /** delete key/value pair or string element. key/value pairs are matched based on their key value */
  _delete_key?: InputMaybe<Report_Request_Delete_Key_Input>;
  /** increments the numeric columns with given value of the filtered values */
  _inc?: InputMaybe<Report_Request_Inc_Input>;
  /** prepend existing jsonb value of filtered columns with new jsonb value */
  _prepend?: InputMaybe<Report_Request_Prepend_Input>;
  /** sets the columns of the filtered rows to the given values */
  _set?: InputMaybe<Report_Request_Set_Input>;
  /** filter the rows which have to be updated */
  where: Report_Request_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Report_Request_Var_Pop_Fields = {
  __typename?: 'report_request_var_pop_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Report_Request_Var_Samp_Fields = {
  __typename?: 'report_request_var_samp_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Report_Request_Variance_Fields = {
  __typename?: 'report_request_variance_fields';
  created_by?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** columns and relationships of "response_feedback" */
export type Response_Feedback = {
  __typename?: 'response_feedback';
  author_id?: Maybe<Scalars['Int']['output']>;
  author_name?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['timestamptz']['output'];
  farm_id: Scalars['Int']['output'];
  farm_name: Scalars['String']['output'];
  feedback: Scalars['jsonb']['output'];
  id: Scalars['String']['output'];
  response: Scalars['jsonb']['output'];
  root_id: Scalars['Int']['output'];
  summary: Scalars['String']['output'];
  type: Scalars['String']['output'];
};


/** columns and relationships of "response_feedback" */
export type Response_FeedbackFeedbackArgs = {
  path?: InputMaybe<Scalars['String']['input']>;
};


/** columns and relationships of "response_feedback" */
export type Response_FeedbackResponseArgs = {
  path?: InputMaybe<Scalars['String']['input']>;
};

/** aggregated selection of "response_feedback" */
export type Response_Feedback_Aggregate = {
  __typename?: 'response_feedback_aggregate';
  aggregate?: Maybe<Response_Feedback_Aggregate_Fields>;
  nodes: Array<Response_Feedback>;
};

/** aggregate fields of "response_feedback" */
export type Response_Feedback_Aggregate_Fields = {
  __typename?: 'response_feedback_aggregate_fields';
  avg?: Maybe<Response_Feedback_Avg_Fields>;
  count: Scalars['Int']['output'];
  max?: Maybe<Response_Feedback_Max_Fields>;
  min?: Maybe<Response_Feedback_Min_Fields>;
  stddev?: Maybe<Response_Feedback_Stddev_Fields>;
  stddev_pop?: Maybe<Response_Feedback_Stddev_Pop_Fields>;
  stddev_samp?: Maybe<Response_Feedback_Stddev_Samp_Fields>;
  sum?: Maybe<Response_Feedback_Sum_Fields>;
  var_pop?: Maybe<Response_Feedback_Var_Pop_Fields>;
  var_samp?: Maybe<Response_Feedback_Var_Samp_Fields>;
  variance?: Maybe<Response_Feedback_Variance_Fields>;
};


/** aggregate fields of "response_feedback" */
export type Response_Feedback_Aggregate_FieldsCountArgs = {
  columns?: InputMaybe<Array<Response_Feedback_Select_Column>>;
  distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Response_Feedback_Append_Input = {
  feedback?: InputMaybe<Scalars['jsonb']['input']>;
  response?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate avg on columns */
export type Response_Feedback_Avg_Fields = {
  __typename?: 'response_feedback_avg_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "response_feedback". All fields are combined with a logical 'AND'. */
export type Response_Feedback_Bool_Exp = {
  _and?: InputMaybe<Array<Response_Feedback_Bool_Exp>>;
  _not?: InputMaybe<Response_Feedback_Bool_Exp>;
  _or?: InputMaybe<Array<Response_Feedback_Bool_Exp>>;
  author_id?: InputMaybe<Int_Comparison_Exp>;
  author_name?: InputMaybe<String_Comparison_Exp>;
  created_at?: InputMaybe<Timestamptz_Comparison_Exp>;
  farm_id?: InputMaybe<Int_Comparison_Exp>;
  farm_name?: InputMaybe<String_Comparison_Exp>;
  feedback?: InputMaybe<Jsonb_Comparison_Exp>;
  id?: InputMaybe<String_Comparison_Exp>;
  response?: InputMaybe<Jsonb_Comparison_Exp>;
  root_id?: InputMaybe<Int_Comparison_Exp>;
  summary?: InputMaybe<String_Comparison_Exp>;
  type?: InputMaybe<String_Comparison_Exp>;
};

/** unique or primary key constraints on table "response_feedback" */
export enum Response_Feedback_Constraint {
  /** unique or primary key constraint on columns "id" */
  ResponseFeedbackPkey = 'response_feedback_pkey'
}

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Response_Feedback_Delete_At_Path_Input = {
  feedback?: InputMaybe<Array<Scalars['String']['input']>>;
  response?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Response_Feedback_Delete_Elem_Input = {
  feedback?: InputMaybe<Scalars['Int']['input']>;
  response?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Response_Feedback_Delete_Key_Input = {
  feedback?: InputMaybe<Scalars['String']['input']>;
  response?: InputMaybe<Scalars['String']['input']>;
};

/** input type for incrementing numeric columns in table "response_feedback" */
export type Response_Feedback_Inc_Input = {
  author_id?: InputMaybe<Scalars['Int']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "response_feedback" */
export type Response_Feedback_Insert_Input = {
  author_id?: InputMaybe<Scalars['Int']['input']>;
  author_name?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  feedback?: InputMaybe<Scalars['jsonb']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  response?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate max on columns */
export type Response_Feedback_Max_Fields = {
  __typename?: 'response_feedback_max_fields';
  author_id?: Maybe<Scalars['Int']['output']>;
  author_name?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  farm_name?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** aggregate min on columns */
export type Response_Feedback_Min_Fields = {
  __typename?: 'response_feedback_min_fields';
  author_id?: Maybe<Scalars['Int']['output']>;
  author_name?: Maybe<Scalars['String']['output']>;
  created_at?: Maybe<Scalars['timestamptz']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  farm_name?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

/** response of any mutation on the table "response_feedback" */
export type Response_Feedback_Mutation_Response = {
  __typename?: 'response_feedback_mutation_response';
  /** number of rows affected by the mutation */
  affected_rows: Scalars['Int']['output'];
  /** data from the rows affected by the mutation */
  returning: Array<Response_Feedback>;
};

/** on_conflict condition type for table "response_feedback" */
export type Response_Feedback_On_Conflict = {
  constraint: Response_Feedback_Constraint;
  update_columns?: Array<Response_Feedback_Update_Column>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};

/** Ordering options when selecting data from "response_feedback". */
export type Response_Feedback_Order_By = {
  author_id?: InputMaybe<Order_By>;
  author_name?: InputMaybe<Order_By>;
  created_at?: InputMaybe<Order_By>;
  farm_id?: InputMaybe<Order_By>;
  farm_name?: InputMaybe<Order_By>;
  feedback?: InputMaybe<Order_By>;
  id?: InputMaybe<Order_By>;
  response?: InputMaybe<Order_By>;
  root_id?: InputMaybe<Order_By>;
  summary?: InputMaybe<Order_By>;
  type?: InputMaybe<Order_By>;
};

/** primary key columns input for table: response_feedback */
export type Response_Feedback_Pk_Columns_Input = {
  id: Scalars['String']['input'];
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Response_Feedback_Prepend_Input = {
  feedback?: InputMaybe<Scalars['jsonb']['input']>;
  response?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "response_feedback" */
export enum Response_Feedback_Select_Column {
  /** column name */
  AuthorId = 'author_id',
  /** column name */
  AuthorName = 'author_name',
  /** column name */
  CreatedAt = 'created_at',
  /** column name */
  FarmId = 'farm_id',
  /** column name */
  FarmName = 'farm_name',
  /** column name */
  Feedback = 'feedback',
  /** column name */
  Id = 'id',
  /** column name */
  Response = 'response',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Summary = 'summary',
  /** column name */
  Type = 'type'
}

/** input type for updating data in table "response_feedback" */
export type Response_Feedback_Set_Input = {
  author_id?: InputMaybe<Scalars['Int']['input']>;
  author_name?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  feedback?: InputMaybe<Scalars['jsonb']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  response?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate stddev on columns */
export type Response_Feedback_Stddev_Fields = {
  __typename?: 'response_feedback_stddev_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Response_Feedback_Stddev_Pop_Fields = {
  __typename?: 'response_feedback_stddev_pop_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Response_Feedback_Stddev_Samp_Fields = {
  __typename?: 'response_feedback_stddev_samp_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "response_feedback" */
export type Response_Feedback_Stream_Cursor_Input = {
  /** Stream column input with initial value */
  initial_value: Response_Feedback_Stream_Cursor_Value_Input;
  /** cursor ordering */
  ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Response_Feedback_Stream_Cursor_Value_Input = {
  author_id?: InputMaybe<Scalars['Int']['input']>;
  author_name?: InputMaybe<Scalars['String']['input']>;
  created_at?: InputMaybe<Scalars['timestamptz']['input']>;
  farm_id?: InputMaybe<Scalars['Int']['input']>;
  farm_name?: InputMaybe<Scalars['String']['input']>;
  feedback?: InputMaybe<Scalars['jsonb']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  response?: InputMaybe<Scalars['jsonb']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  summary?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

/** aggregate sum on columns */
export type Response_Feedback_Sum_Fields = {
  __typename?: 'response_feedback_sum_fields';
  author_id?: Maybe<Scalars['Int']['output']>;
  farm_id?: Maybe<Scalars['Int']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "response_feedback" */
export enum Response_Feedback_Update_Column {
  /** column name */
  AuthorId = 'author_id',
  /** column name */
  AuthorName = 'author_name',
  /** column name */
  CreatedAt = 'created_at',
  /** column name */
  FarmId = 'farm_id',
  /** column name */
  FarmName = 'farm_name',
  /** column name */
  Feedback = 'feedback',
  /** column name */
  Id = 'id',
  /** column name */
  Response = 'response',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Summary = 'summary',
  /** column name */
  Type = 'type'
}

export type Response_Feedback_Updates = {
  /** append existing jsonb value of filtered columns with new jsonb value */
  _append?: InputMaybe<Response_Feedback_Append_Input>;
  /** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
  _delete_at_path?: InputMaybe<Response_Feedback_Delete_At_Path_Input>;
  /** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
  _delete_elem?: InputMaybe<Response_Feedback_Delete_Elem_Input>;
  /** delete key/value pair or string element. key/value pairs are matched based on their key value */
  _delete_key?: InputMaybe<Response_Feedback_Delete_Key_Input>;
  /** increments the numeric columns with given value of the filtered values */
  _inc?: InputMaybe<Response_Feedback_Inc_Input>;
  /** prepend existing jsonb value of filtered columns with new jsonb value */
  _prepend?: InputMaybe<Response_Feedback_Prepend_Input>;
  /** sets the columns of the filtered rows to the given values */
  _set?: InputMaybe<Response_Feedback_Set_Input>;
  /** filter the rows which have to be updated */
  where: Response_Feedback_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Response_Feedback_Var_Pop_Fields = {
  __typename?: 'response_feedback_var_pop_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Response_Feedback_Var_Samp_Fields = {
  __typename?: 'response_feedback_var_samp_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Response_Feedback_Variance_Fields = {
  __typename?: 'response_feedback_variance_fields';
  author_id?: Maybe<Scalars['Float']['output']>;
  farm_id?: Maybe<Scalars['Float']['output']>;
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** columns and relationships of "setting" */
export type Setting = {
  __typename?: 'setting';
  env: Scalars['String']['output'];
  root_id: Scalars['Int']['output'];
  setting: Scalars['jsonb']['output'];
};


/** columns and relationships of "setting" */
export type SettingSettingArgs = {
  path?: InputMaybe<Scalars['String']['input']>;
};

/** aggregated selection of "setting" */
export type Setting_Aggregate = {
  __typename?: 'setting_aggregate';
  aggregate?: Maybe<Setting_Aggregate_Fields>;
  nodes: Array<Setting>;
};

/** aggregate fields of "setting" */
export type Setting_Aggregate_Fields = {
  __typename?: 'setting_aggregate_fields';
  avg?: Maybe<Setting_Avg_Fields>;
  count: Scalars['Int']['output'];
  max?: Maybe<Setting_Max_Fields>;
  min?: Maybe<Setting_Min_Fields>;
  stddev?: Maybe<Setting_Stddev_Fields>;
  stddev_pop?: Maybe<Setting_Stddev_Pop_Fields>;
  stddev_samp?: Maybe<Setting_Stddev_Samp_Fields>;
  sum?: Maybe<Setting_Sum_Fields>;
  var_pop?: Maybe<Setting_Var_Pop_Fields>;
  var_samp?: Maybe<Setting_Var_Samp_Fields>;
  variance?: Maybe<Setting_Variance_Fields>;
};


/** aggregate fields of "setting" */
export type Setting_Aggregate_FieldsCountArgs = {
  columns?: InputMaybe<Array<Setting_Select_Column>>;
  distinct?: InputMaybe<Scalars['Boolean']['input']>;
};

/** append existing jsonb value of filtered columns with new jsonb value */
export type Setting_Append_Input = {
  setting?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate avg on columns */
export type Setting_Avg_Fields = {
  __typename?: 'setting_avg_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Boolean expression to filter rows from the table "setting". All fields are combined with a logical 'AND'. */
export type Setting_Bool_Exp = {
  _and?: InputMaybe<Array<Setting_Bool_Exp>>;
  _not?: InputMaybe<Setting_Bool_Exp>;
  _or?: InputMaybe<Array<Setting_Bool_Exp>>;
  env?: InputMaybe<String_Comparison_Exp>;
  root_id?: InputMaybe<Int_Comparison_Exp>;
  setting?: InputMaybe<Jsonb_Comparison_Exp>;
};

/** unique or primary key constraints on table "setting" */
export enum Setting_Constraint {
  /** unique or primary key constraint on columns "env", "root_id" */
  SettingPkey = 'setting_pkey'
}

/** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
export type Setting_Delete_At_Path_Input = {
  setting?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
export type Setting_Delete_Elem_Input = {
  setting?: InputMaybe<Scalars['Int']['input']>;
};

/** delete key/value pair or string element. key/value pairs are matched based on their key value */
export type Setting_Delete_Key_Input = {
  setting?: InputMaybe<Scalars['String']['input']>;
};

/** input type for incrementing numeric columns in table "setting" */
export type Setting_Inc_Input = {
  root_id?: InputMaybe<Scalars['Int']['input']>;
};

/** input type for inserting data into table "setting" */
export type Setting_Insert_Input = {
  env?: InputMaybe<Scalars['String']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  setting?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate max on columns */
export type Setting_Max_Fields = {
  __typename?: 'setting_max_fields';
  env?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
};

/** aggregate min on columns */
export type Setting_Min_Fields = {
  __typename?: 'setting_min_fields';
  env?: Maybe<Scalars['String']['output']>;
  root_id?: Maybe<Scalars['Int']['output']>;
};

/** response of any mutation on the table "setting" */
export type Setting_Mutation_Response = {
  __typename?: 'setting_mutation_response';
  /** number of rows affected by the mutation */
  affected_rows: Scalars['Int']['output'];
  /** data from the rows affected by the mutation */
  returning: Array<Setting>;
};

/** on_conflict condition type for table "setting" */
export type Setting_On_Conflict = {
  constraint: Setting_Constraint;
  update_columns?: Array<Setting_Update_Column>;
  where?: InputMaybe<Setting_Bool_Exp>;
};

/** Ordering options when selecting data from "setting". */
export type Setting_Order_By = {
  env?: InputMaybe<Order_By>;
  root_id?: InputMaybe<Order_By>;
  setting?: InputMaybe<Order_By>;
};

/** primary key columns input for table: setting */
export type Setting_Pk_Columns_Input = {
  env: Scalars['String']['input'];
  root_id: Scalars['Int']['input'];
};

/** prepend existing jsonb value of filtered columns with new jsonb value */
export type Setting_Prepend_Input = {
  setting?: InputMaybe<Scalars['jsonb']['input']>;
};

/** select columns of table "setting" */
export enum Setting_Select_Column {
  /** column name */
  Env = 'env',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Setting = 'setting'
}

/** input type for updating data in table "setting" */
export type Setting_Set_Input = {
  env?: InputMaybe<Scalars['String']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  setting?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate stddev on columns */
export type Setting_Stddev_Fields = {
  __typename?: 'setting_stddev_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_pop on columns */
export type Setting_Stddev_Pop_Fields = {
  __typename?: 'setting_stddev_pop_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate stddev_samp on columns */
export type Setting_Stddev_Samp_Fields = {
  __typename?: 'setting_stddev_samp_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** Streaming cursor of the table "setting" */
export type Setting_Stream_Cursor_Input = {
  /** Stream column input with initial value */
  initial_value: Setting_Stream_Cursor_Value_Input;
  /** cursor ordering */
  ordering?: InputMaybe<Cursor_Ordering>;
};

/** Initial value of the column from where the streaming should start */
export type Setting_Stream_Cursor_Value_Input = {
  env?: InputMaybe<Scalars['String']['input']>;
  root_id?: InputMaybe<Scalars['Int']['input']>;
  setting?: InputMaybe<Scalars['jsonb']['input']>;
};

/** aggregate sum on columns */
export type Setting_Sum_Fields = {
  __typename?: 'setting_sum_fields';
  root_id?: Maybe<Scalars['Int']['output']>;
};

/** update columns of table "setting" */
export enum Setting_Update_Column {
  /** column name */
  Env = 'env',
  /** column name */
  RootId = 'root_id',
  /** column name */
  Setting = 'setting'
}

export type Setting_Updates = {
  /** append existing jsonb value of filtered columns with new jsonb value */
  _append?: InputMaybe<Setting_Append_Input>;
  /** delete the field or element with specified path (for JSON arrays, negative integers count from the end) */
  _delete_at_path?: InputMaybe<Setting_Delete_At_Path_Input>;
  /** delete the array element with specified index (negative integers count from the end). throws an error if top level container is not an array */
  _delete_elem?: InputMaybe<Setting_Delete_Elem_Input>;
  /** delete key/value pair or string element. key/value pairs are matched based on their key value */
  _delete_key?: InputMaybe<Setting_Delete_Key_Input>;
  /** increments the numeric columns with given value of the filtered values */
  _inc?: InputMaybe<Setting_Inc_Input>;
  /** prepend existing jsonb value of filtered columns with new jsonb value */
  _prepend?: InputMaybe<Setting_Prepend_Input>;
  /** sets the columns of the filtered rows to the given values */
  _set?: InputMaybe<Setting_Set_Input>;
  /** filter the rows which have to be updated */
  where: Setting_Bool_Exp;
};

/** aggregate var_pop on columns */
export type Setting_Var_Pop_Fields = {
  __typename?: 'setting_var_pop_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate var_samp on columns */
export type Setting_Var_Samp_Fields = {
  __typename?: 'setting_var_samp_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

/** aggregate variance on columns */
export type Setting_Variance_Fields = {
  __typename?: 'setting_variance_fields';
  root_id?: Maybe<Scalars['Float']['output']>;
};

export type Subscription_Root = {
  __typename?: 'subscription_root';
  /** fetch data from the table: "active_template" */
  active_template: Array<Active_Template>;
  /** fetch aggregated fields from the table: "active_template" */
  active_template_aggregate: Active_Template_Aggregate;
  /** fetch data from the table: "active_template" using primary key columns */
  active_template_by_pk?: Maybe<Active_Template>;
  /** fetch data from the table in a streaming manner: "active_template" */
  active_template_stream: Array<Active_Template>;
  /** fetch data from the table: "prompt_template" */
  prompt_template: Array<Prompt_Template>;
  /** fetch aggregated fields from the table: "prompt_template" */
  prompt_template_aggregate: Prompt_Template_Aggregate;
  /** fetch data from the table: "prompt_template" using primary key columns */
  prompt_template_by_pk?: Maybe<Prompt_Template>;
  /** fetch data from the table in a streaming manner: "prompt_template" */
  prompt_template_stream: Array<Prompt_Template>;
  /** fetch data from the table: "report_request" */
  report_request: Array<Report_Request>;
  /** fetch aggregated fields from the table: "report_request" */
  report_request_aggregate: Report_Request_Aggregate;
  /** fetch data from the table in a streaming manner: "report_request" */
  report_request_stream: Array<Report_Request>;
  /** fetch data from the table: "response_feedback" */
  response_feedback: Array<Response_Feedback>;
  /** fetch aggregated fields from the table: "response_feedback" */
  response_feedback_aggregate: Response_Feedback_Aggregate;
  /** fetch data from the table: "response_feedback" using primary key columns */
  response_feedback_by_pk?: Maybe<Response_Feedback>;
  /** fetch data from the table in a streaming manner: "response_feedback" */
  response_feedback_stream: Array<Response_Feedback>;
  /** fetch data from the table: "setting" */
  setting: Array<Setting>;
  /** fetch aggregated fields from the table: "setting" */
  setting_aggregate: Setting_Aggregate;
  /** fetch data from the table: "setting" using primary key columns */
  setting_by_pk?: Maybe<Setting>;
  /** fetch data from the table in a streaming manner: "setting" */
  setting_stream: Array<Setting>;
};


export type Subscription_RootActive_TemplateArgs = {
  distinct_on?: InputMaybe<Array<Active_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Active_Template_Order_By>>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};


export type Subscription_RootActive_Template_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Active_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Active_Template_Order_By>>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};


export type Subscription_RootActive_Template_By_PkArgs = {
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
};


export type Subscription_RootActive_Template_StreamArgs = {
  batch_size: Scalars['Int']['input'];
  cursor: Array<InputMaybe<Active_Template_Stream_Cursor_Input>>;
  where?: InputMaybe<Active_Template_Bool_Exp>;
};


export type Subscription_RootPrompt_TemplateArgs = {
  distinct_on?: InputMaybe<Array<Prompt_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Prompt_Template_Order_By>>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};


export type Subscription_RootPrompt_Template_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Prompt_Template_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Prompt_Template_Order_By>>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};


export type Subscription_RootPrompt_Template_By_PkArgs = {
  id: Scalars['uuid']['input'];
};


export type Subscription_RootPrompt_Template_StreamArgs = {
  batch_size: Scalars['Int']['input'];
  cursor: Array<InputMaybe<Prompt_Template_Stream_Cursor_Input>>;
  where?: InputMaybe<Prompt_Template_Bool_Exp>;
};


export type Subscription_RootReport_RequestArgs = {
  distinct_on?: InputMaybe<Array<Report_Request_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Report_Request_Order_By>>;
  where?: InputMaybe<Report_Request_Bool_Exp>;
};


export type Subscription_RootReport_Request_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Report_Request_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Report_Request_Order_By>>;
  where?: InputMaybe<Report_Request_Bool_Exp>;
};


export type Subscription_RootReport_Request_StreamArgs = {
  batch_size: Scalars['Int']['input'];
  cursor: Array<InputMaybe<Report_Request_Stream_Cursor_Input>>;
  where?: InputMaybe<Report_Request_Bool_Exp>;
};


export type Subscription_RootResponse_FeedbackArgs = {
  distinct_on?: InputMaybe<Array<Response_Feedback_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Response_Feedback_Order_By>>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};


export type Subscription_RootResponse_Feedback_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Response_Feedback_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Response_Feedback_Order_By>>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};


export type Subscription_RootResponse_Feedback_By_PkArgs = {
  id: Scalars['String']['input'];
};


export type Subscription_RootResponse_Feedback_StreamArgs = {
  batch_size: Scalars['Int']['input'];
  cursor: Array<InputMaybe<Response_Feedback_Stream_Cursor_Input>>;
  where?: InputMaybe<Response_Feedback_Bool_Exp>;
};


export type Subscription_RootSettingArgs = {
  distinct_on?: InputMaybe<Array<Setting_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Setting_Order_By>>;
  where?: InputMaybe<Setting_Bool_Exp>;
};


export type Subscription_RootSetting_AggregateArgs = {
  distinct_on?: InputMaybe<Array<Setting_Select_Column>>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  offset?: InputMaybe<Scalars['Int']['input']>;
  order_by?: InputMaybe<Array<Setting_Order_By>>;
  where?: InputMaybe<Setting_Bool_Exp>;
};


export type Subscription_RootSetting_By_PkArgs = {
  env: Scalars['String']['input'];
  root_id: Scalars['Int']['input'];
};


export type Subscription_RootSetting_StreamArgs = {
  batch_size: Scalars['Int']['input'];
  cursor: Array<InputMaybe<Setting_Stream_Cursor_Input>>;
  where?: InputMaybe<Setting_Bool_Exp>;
};

/** Boolean expression to compare columns of type "timestamptz". All fields are combined with logical 'AND'. */
export type Timestamptz_Comparison_Exp = {
  _eq?: InputMaybe<Scalars['timestamptz']['input']>;
  _gt?: InputMaybe<Scalars['timestamptz']['input']>;
  _gte?: InputMaybe<Scalars['timestamptz']['input']>;
  _in?: InputMaybe<Array<Scalars['timestamptz']['input']>>;
  _is_null?: InputMaybe<Scalars['Boolean']['input']>;
  _lt?: InputMaybe<Scalars['timestamptz']['input']>;
  _lte?: InputMaybe<Scalars['timestamptz']['input']>;
  _neq?: InputMaybe<Scalars['timestamptz']['input']>;
  _nin?: InputMaybe<Array<Scalars['timestamptz']['input']>>;
};

/** Boolean expression to compare columns of type "uuid". All fields are combined with logical 'AND'. */
export type Uuid_Comparison_Exp = {
  _eq?: InputMaybe<Scalars['uuid']['input']>;
  _gt?: InputMaybe<Scalars['uuid']['input']>;
  _gte?: InputMaybe<Scalars['uuid']['input']>;
  _in?: InputMaybe<Array<Scalars['uuid']['input']>>;
  _is_null?: InputMaybe<Scalars['Boolean']['input']>;
  _lt?: InputMaybe<Scalars['uuid']['input']>;
  _lte?: InputMaybe<Scalars['uuid']['input']>;
  _neq?: InputMaybe<Scalars['uuid']['input']>;
  _nin?: InputMaybe<Array<Scalars['uuid']['input']>>;
};

export type GetFeedbackSubscriptionVariables = Exact<{
  responseId: Scalars['String']['input'];
}>;


export type GetFeedbackSubscription = { __typename?: 'subscription_root', response_feedback: Array<{ __typename?: 'response_feedback', feedback: any }> };

export type GetPromptTemplatesQueryVariables = Exact<{
  rootId: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type GetPromptTemplatesQuery = { __typename?: 'query_root', prompt_template: Array<{ __typename?: 'prompt_template', id: string, root_id: number, template: string }> };

export type GetPromptTemplatesSubSubscriptionVariables = Exact<{
  rootId: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type GetPromptTemplatesSubSubscription = { __typename?: 'subscription_root', prompt_template: Array<{ __typename?: 'prompt_template', id: string, root_id: number, template: string }> };

export type GetDefaultTemplateSubscriptionVariables = Exact<{
  type: Scalars['String']['input'];
}>;


export type GetDefaultTemplateSubscription = { __typename?: 'subscription_root', active_template: Array<{ __typename?: 'active_template', prompt_template: { __typename?: 'prompt_template', id: string, root_id: number, template: string } }> };

export type AddPromptTemplateMutationVariables = Exact<{
  type: Scalars['String']['input'];
  root_id?: InputMaybe<Scalars['Int']['input']>;
  template: Scalars['String']['input'];
}>;


export type AddPromptTemplateMutation = { __typename?: 'mutation_root', insert_prompt_template_one?: { __typename?: 'prompt_template', id: string, root_id: number, template: string } | null };

export type GetActiveTemplateQueryVariables = Exact<{
  rootId: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type GetActiveTemplateQuery = { __typename?: 'query_root', active_template: Array<{ __typename?: 'active_template', root_id: number, type: string, template_id: string }> };

export type GetActiveTemplateSubSubscriptionVariables = Exact<{
  rootId: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type GetActiveTemplateSubSubscription = { __typename?: 'subscription_root', active_template: Array<{ __typename?: 'active_template', root_id: number, type: string, template_id: string }> };

export type ActivateTemplateMutationVariables = Exact<{
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
  update_by: Scalars['Int']['input'];
  templateId: Scalars['uuid']['input'];
}>;


export type ActivateTemplateMutation = { __typename?: 'mutation_root', insert_active_template_one?: { __typename?: 'active_template', root_id: number, template_id: string } | null };

export type DeleteCurrentActiveTemplateMutationVariables = Exact<{
  root_id: Scalars['Int']['input'];
  type: Scalars['String']['input'];
}>;


export type DeleteCurrentActiveTemplateMutation = { __typename?: 'mutation_root', delete_active_template_by_pk?: { __typename?: 'active_template', root_id: number, template_id: string } | null };

export type GetFeedbackFrontendSubscriptionVariables = Exact<{
  type: Scalars['String']['input'];
  rootId: Int_Comparison_Exp;
}>;


export type GetFeedbackFrontendSubscription = { __typename?: 'subscription_root', response_feedback: Array<{ __typename?: 'response_feedback', id: string, farm_id: number, root_id: number, response: any, feedback: any, summary: string, farm_name: string, created_at: string, author_name?: string | null }> };

export type GetReportRequestSubscriptionVariables = Exact<{
  type: Scalars['String']['input'];
  rootId: Int_Comparison_Exp;
}>;


export type GetReportRequestSubscription = { __typename?: 'subscription_root', report_request: Array<{ __typename?: 'report_request', farm_name: string, farm_id: number, root_id: number, created_at: string, report_data: any, country: string }> };

export type GetSettingsQueryVariables = Exact<{
  env: Scalars['String']['input'];
  rootId: Scalars['Int']['input'];
}>;


export type GetSettingsQuery = { __typename?: 'query_root', setting: Array<{ __typename?: 'setting', setting: any }> };

export type UpsertSettingMutationVariables = Exact<{
  env: Scalars['String']['input'];
  rootId: Scalars['Int']['input'];
  setting: Scalars['jsonb']['input'];
}>;


export type UpsertSettingMutation = { __typename?: 'mutation_root', insert_setting_one?: { __typename?: 'setting', setting: any } | null };


export const GetFeedbackDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetFeedback"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"responseId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"response_feedback"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"responseId"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"feedback"}}]}}]}}]} as unknown as DocumentNode<GetFeedbackSubscription, GetFeedbackSubscriptionVariables>;
export const GetPromptTemplatesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPromptTemplates"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"prompt_template"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order_by"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"created_at"},"value":{"kind":"EnumValue","value":"asc"}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template"}}]}}]}}]} as unknown as DocumentNode<GetPromptTemplatesQuery, GetPromptTemplatesQueryVariables>;
export const GetPromptTemplatesSubDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetPromptTemplatesSub"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"prompt_template"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order_by"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"created_at"},"value":{"kind":"EnumValue","value":"asc"}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template"}}]}}]}}]} as unknown as DocumentNode<GetPromptTemplatesSubSubscription, GetPromptTemplatesSubSubscriptionVariables>;
export const GetDefaultTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetDefaultTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"active_template"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"IntValue","value":"-1"}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"prompt_template"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template"}}]}}]}}]}}]} as unknown as DocumentNode<GetDefaultTemplateSubscription, GetDefaultTemplateSubscriptionVariables>;
export const AddPromptTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddPromptTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"template"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"insert_prompt_template_one"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"object"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"template"},"value":{"kind":"Variable","name":{"kind":"Name","value":"template"}}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template"}}]}}]}}]} as unknown as DocumentNode<AddPromptTemplateMutation, AddPromptTemplateMutationVariables>;
export const GetActiveTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetActiveTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"active_template"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"template_id"}}]}}]}}]} as unknown as DocumentNode<GetActiveTemplateQuery, GetActiveTemplateQueryVariables>;
export const GetActiveTemplateSubDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetActiveTemplateSub"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"active_template"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"template_id"}}]}}]}}]} as unknown as DocumentNode<GetActiveTemplateSubSubscription, GetActiveTemplateSubSubscriptionVariables>;
export const ActivateTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"ActivateTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"update_by"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"templateId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"uuid"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"insert_active_template_one"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"object"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"template_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"templateId"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"update_by"},"value":{"kind":"Variable","name":{"kind":"Name","value":"update_by"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"on_conflict"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"constraint"},"value":{"kind":"EnumValue","value":"current_template_pkey"}},{"kind":"ObjectField","name":{"kind":"Name","value":"update_columns"},"value":{"kind":"ListValue","values":[{"kind":"EnumValue","value":"template_id"}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template_id"}}]}}]}}]} as unknown as DocumentNode<ActivateTemplateMutation, ActivateTemplateMutationVariables>;
export const DeleteCurrentActiveTemplateDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteCurrentActiveTemplate"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"delete_active_template_by_pk"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"root_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"template_id"}}]}}]}}]} as unknown as DocumentNode<DeleteCurrentActiveTemplateMutation, DeleteCurrentActiveTemplateMutationVariables>;
export const GetFeedbackFrontendDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetFeedbackFrontend"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int_comparison_exp"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"response_feedback"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order_by"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"created_at"},"value":{"kind":"EnumValue","value":"desc"}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"farm_id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"response"}},{"kind":"Field","name":{"kind":"Name","value":"feedback"}},{"kind":"Field","name":{"kind":"Name","value":"summary"}},{"kind":"Field","name":{"kind":"Name","value":"farm_name"}},{"kind":"Field","name":{"kind":"Name","value":"created_at"}},{"kind":"Field","name":{"kind":"Name","value":"author_name"}}]}}]}}]} as unknown as DocumentNode<GetFeedbackFrontendSubscription, GetFeedbackFrontendSubscriptionVariables>;
export const GetReportRequestDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"subscription","name":{"kind":"Name","value":"GetReportRequest"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int_comparison_exp"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"report_request"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"type"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"order_by"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"created_at"},"value":{"kind":"EnumValue","value":"desc"}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"farm_name"}},{"kind":"Field","name":{"kind":"Name","value":"farm_id"}},{"kind":"Field","name":{"kind":"Name","value":"root_id"}},{"kind":"Field","name":{"kind":"Name","value":"created_at"}},{"kind":"Field","name":{"kind":"Name","value":"report_data"}},{"kind":"Field","name":{"kind":"Name","value":"country"}}]}}]}}]} as unknown as DocumentNode<GetReportRequestSubscription, GetReportRequestSubscriptionVariables>;
export const GetSettingsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSettings"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"env"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"setting"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"where"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_and"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}}]}}]}},{"kind":"ObjectField","name":{"kind":"Name","value":"env"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"_eq"},"value":{"kind":"Variable","name":{"kind":"Name","value":"env"}}}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"setting"}}]}}]}}]} as unknown as DocumentNode<GetSettingsQuery, GetSettingsQueryVariables>;
export const UpsertSettingDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"upsertSetting"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"env"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"setting"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"jsonb"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"insert_setting_one"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"object"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"root_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"rootId"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"env"},"value":{"kind":"Variable","name":{"kind":"Name","value":"env"}}},{"kind":"ObjectField","name":{"kind":"Name","value":"setting"},"value":{"kind":"Variable","name":{"kind":"Name","value":"setting"}}}]}},{"kind":"Argument","name":{"kind":"Name","value":"on_conflict"},"value":{"kind":"ObjectValue","fields":[{"kind":"ObjectField","name":{"kind":"Name","value":"constraint"},"value":{"kind":"EnumValue","value":"setting_pkey"}},{"kind":"ObjectField","name":{"kind":"Name","value":"update_columns"},"value":{"kind":"ListValue","values":[{"kind":"EnumValue","value":"setting"}]}}]}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"setting"}}]}}]}}]} as unknown as DocumentNode<UpsertSettingMutation, UpsertSettingMutationVariables>;