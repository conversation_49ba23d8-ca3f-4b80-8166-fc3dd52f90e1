/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
    "\n\tsubscription GetFeedback($responseId: String!) {\n\t\tresponse_feedback(where: { id: { _eq: $responseId } }) {\n\t\t\tfeedback\n\t\t}\n\t}\n": types.GetFeedbackDocument,
    "\n\tquery GetPromptTemplates($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n": types.GetPromptTemplatesDocument,
    "\n\tsubscription GetPromptTemplatesSub($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n": types.GetPromptTemplatesSubDocument,
    "\n\tsubscription GetDefaultTemplate($type: String!) {\n\t\tactive_template(where: { root_id: { _eq: -1 }, type: { _eq: $type } }) {\n\t\t\tprompt_template {\n\t\t\t\tid\n\t\t\t\troot_id\n\t\t\t\ttemplate\n\t\t\t}\n\t\t}\n\t}\n": types.GetDefaultTemplateDocument,
    "\n\tmutation AddPromptTemplate($type: String!, $root_id: Int, $template: String!) {\n\t\tinsert_prompt_template_one(object: { type: $type, root_id: $root_id, template: $template }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n": types.AddPromptTemplateDocument,
    "\n\tquery GetActiveTemplate($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n": types.GetActiveTemplateDocument,
    "\n\tsubscription GetActiveTemplateSub($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n": types.GetActiveTemplateSubDocument,
    "\n\tmutation ActivateTemplate($root_id: Int!, $type: String!, $update_by: Int!, $templateId: uuid!) {\n\t\tinsert_active_template_one(\n\t\t\tobject: { root_id: $root_id, type: $type, template_id: $templateId, update_by: $update_by }\n\t\t\ton_conflict: { constraint: current_template_pkey, update_columns: [template_id] }\n\t\t) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n": types.ActivateTemplateDocument,
    "\n\tmutation DeleteCurrentActiveTemplate($root_id: Int!, $type: String!) {\n\t\tdelete_active_template_by_pk(root_id: $root_id, type: $type) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n": types.DeleteCurrentActiveTemplateDocument,
    "\n\tsubscription GetFeedbackFrontend($type: String!, $rootId: Int_comparison_exp!) {\n\t\tresponse_feedback(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tid\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tresponse\n\t\t\tfeedback\n\t\t\tsummary\n\t\t\tfarm_name\n\t\t\tcreated_at\n\t\t\tauthor_name\n\t\t}\n\t}\n": types.GetFeedbackFrontendDocument,
    "\n\tsubscription GetReportRequest($type: String!, $rootId: Int_comparison_exp!) {\n\t\treport_request(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tfarm_name\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tcreated_at\n\t\t\treport_data\n\t\t\tcountry\n\t\t}\n\t}\n": types.GetReportRequestDocument,
    "\n\tquery GetSettings($env: String!, $rootId: Int!) {\n\t\tsetting(where: { _and: { root_id: { _eq: $rootId } }, env: { _eq: $env } }) {\n\t\t\tsetting\n\t\t}\n\t}\n": types.GetSettingsDocument,
    "\n\tmutation upsertSetting($env: String!, $rootId: Int!, $setting: jsonb!) {\n\t\tinsert_setting_one(\n\t\t\tobject: { root_id: $rootId, env: $env, setting: $setting }\n\t\t\ton_conflict: { constraint: setting_pkey, update_columns: [setting] }\n\t\t) {\n\t\t\tsetting\n\t\t}\n\t}\n": types.UpsertSettingDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetFeedback($responseId: String!) {\n\t\tresponse_feedback(where: { id: { _eq: $responseId } }) {\n\t\t\tfeedback\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetFeedback($responseId: String!) {\n\t\tresponse_feedback(where: { id: { _eq: $responseId } }) {\n\t\t\tfeedback\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tquery GetPromptTemplates($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"): (typeof documents)["\n\tquery GetPromptTemplates($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetPromptTemplatesSub($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetPromptTemplatesSub($rootId: Int!, $type: String!) {\n\t\tprompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetDefaultTemplate($type: String!) {\n\t\tactive_template(where: { root_id: { _eq: -1 }, type: { _eq: $type } }) {\n\t\t\tprompt_template {\n\t\t\t\tid\n\t\t\t\troot_id\n\t\t\t\ttemplate\n\t\t\t}\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetDefaultTemplate($type: String!) {\n\t\tactive_template(where: { root_id: { _eq: -1 }, type: { _eq: $type } }) {\n\t\t\tprompt_template {\n\t\t\t\tid\n\t\t\t\troot_id\n\t\t\t\ttemplate\n\t\t\t}\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tmutation AddPromptTemplate($type: String!, $root_id: Int, $template: String!) {\n\t\tinsert_prompt_template_one(object: { type: $type, root_id: $root_id, template: $template }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"): (typeof documents)["\n\tmutation AddPromptTemplate($type: String!, $root_id: Int, $template: String!) {\n\t\tinsert_prompt_template_one(object: { type: $type, root_id: $root_id, template: $template }) {\n\t\t\tid\n\t\t\troot_id\n\t\t\ttemplate\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tquery GetActiveTemplate($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n"): (typeof documents)["\n\tquery GetActiveTemplate($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetActiveTemplateSub($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetActiveTemplateSub($rootId: Int!, $type: String!) {\n\t\tactive_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {\n\t\t\troot_id\n\t\t\ttype\n\t\t\ttemplate_id\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tmutation ActivateTemplate($root_id: Int!, $type: String!, $update_by: Int!, $templateId: uuid!) {\n\t\tinsert_active_template_one(\n\t\t\tobject: { root_id: $root_id, type: $type, template_id: $templateId, update_by: $update_by }\n\t\t\ton_conflict: { constraint: current_template_pkey, update_columns: [template_id] }\n\t\t) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n"): (typeof documents)["\n\tmutation ActivateTemplate($root_id: Int!, $type: String!, $update_by: Int!, $templateId: uuid!) {\n\t\tinsert_active_template_one(\n\t\t\tobject: { root_id: $root_id, type: $type, template_id: $templateId, update_by: $update_by }\n\t\t\ton_conflict: { constraint: current_template_pkey, update_columns: [template_id] }\n\t\t) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tmutation DeleteCurrentActiveTemplate($root_id: Int!, $type: String!) {\n\t\tdelete_active_template_by_pk(root_id: $root_id, type: $type) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n"): (typeof documents)["\n\tmutation DeleteCurrentActiveTemplate($root_id: Int!, $type: String!) {\n\t\tdelete_active_template_by_pk(root_id: $root_id, type: $type) {\n\t\t\troot_id\n\t\t\ttemplate_id\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetFeedbackFrontend($type: String!, $rootId: Int_comparison_exp!) {\n\t\tresponse_feedback(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tid\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tresponse\n\t\t\tfeedback\n\t\t\tsummary\n\t\t\tfarm_name\n\t\t\tcreated_at\n\t\t\tauthor_name\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetFeedbackFrontend($type: String!, $rootId: Int_comparison_exp!) {\n\t\tresponse_feedback(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tid\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tresponse\n\t\t\tfeedback\n\t\t\tsummary\n\t\t\tfarm_name\n\t\t\tcreated_at\n\t\t\tauthor_name\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tsubscription GetReportRequest($type: String!, $rootId: Int_comparison_exp!) {\n\t\treport_request(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tfarm_name\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tcreated_at\n\t\t\treport_data\n\t\t\tcountry\n\t\t}\n\t}\n"): (typeof documents)["\n\tsubscription GetReportRequest($type: String!, $rootId: Int_comparison_exp!) {\n\t\treport_request(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {\n\t\t\tfarm_name\n\t\t\tfarm_id\n\t\t\troot_id\n\t\t\tcreated_at\n\t\t\treport_data\n\t\t\tcountry\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tquery GetSettings($env: String!, $rootId: Int!) {\n\t\tsetting(where: { _and: { root_id: { _eq: $rootId } }, env: { _eq: $env } }) {\n\t\t\tsetting\n\t\t}\n\t}\n"): (typeof documents)["\n\tquery GetSettings($env: String!, $rootId: Int!) {\n\t\tsetting(where: { _and: { root_id: { _eq: $rootId } }, env: { _eq: $env } }) {\n\t\t\tsetting\n\t\t}\n\t}\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n\tmutation upsertSetting($env: String!, $rootId: Int!, $setting: jsonb!) {\n\t\tinsert_setting_one(\n\t\t\tobject: { root_id: $rootId, env: $env, setting: $setting }\n\t\t\ton_conflict: { constraint: setting_pkey, update_columns: [setting] }\n\t\t) {\n\t\t\tsetting\n\t\t}\n\t}\n"): (typeof documents)["\n\tmutation upsertSetting($env: String!, $rootId: Int!, $setting: jsonb!) {\n\t\tinsert_setting_one(\n\t\t\tobject: { root_id: $rootId, env: $env, setting: $setting }\n\t\t\ton_conflict: { constraint: setting_pkey, update_columns: [setting] }\n\t\t) {\n\t\t\tsetting\n\t\t}\n\t}\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;