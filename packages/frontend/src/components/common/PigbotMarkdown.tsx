/** @jsxImportSource @emotion/react */
import React, { useMemo } from 'react';
import ReactMarkdown, { Options } from 'react-markdown';
import remarkGfm from 'remark-gfm';
import MarkdownStyle from '@src/components/common/MarkdownStyle';
import EChartRenderer from '@src/components/common/EchartRenderer';
import { Components } from 'react-markdown/lib';
import { convertJsonToGraphOpt } from 'pigbot-core/src/eff-report/GraphConverters';
import logger from 'pigbot-core/src/logger';
import { Theme, Interpolation } from '@emotion/react';

interface PigbotMarkdownProps extends Options {
	additionalCss?: Interpolation<Theme>;
}

/**
 * Renders data from code block as an ECharts chart.
 */
const EChartsCodeBlock: React.FC<{ type: string; text: string }> = ({ type, text }) => {
	// Cache the graph to avoid re-rendering it when other parts of the markdown changes.
	return useMemo(() => {
		const { title, convertedOptions, explanation, height } = convertJsonToGraphOpt(type, text);

		try {
			const chartData = {
				title: title,
				option: convertedOptions,
				explanation: explanation,
				height,
			};
			return <EChartRenderer value={chartData} />;
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('Error parsing graph data:', error);
			return <div className='echart-error'>Invalid graph configuration</div>;
		}
	}, [type, text]);
};

const PigbotMarkdown: React.FC<PigbotMarkdownProps> = (props) => {
	const { children, remarkPlugins = [], rehypePlugins = [], components = {}, ...rest } = props;

	// Merge custom components with our default components
	const mergedComponents: Components = {
		// Default component for handling graph code blocks
		pre({ children }) {
			// Check if this pre element contains a code element with language 'graph'
			const codeChild = React.Children.toArray(children).find((child) => {
				if (!React.isValidElement(child)) return false;
				// Check if it's a code element by examining its properties
				return child.props && child.props.className && /language-\w+/.test(child.props.className);
			});

			if (codeChild && React.isValidElement(codeChild)) {
				const className = codeChild.props.className || '';
				const match = /language-(\w+)/.exec(className);
				const language = match && match[1];

				if (!language || !codeChild.props.children) {
					return `<pre><code class="${language ? `language-${language}` : ''}">${codeChild.props.children}</code></pre>`;
				}

				return <EChartsCodeBlock type={language} text={codeChild.props.children} />;
			}

			logger.warn('Incorrect usage of code block in markdown', React.Children.toArray(children));
			// Hide unknown <pre> elements as they breaks page layout.
			return <></>;
		},
		// Add any other custom components here
		...components,
	};

	// Cache the rendered markdown to avoid re-rendering it when other parts of the page change.
	return useMemo(
		() => (
			<ReactMarkdown
				css={[MarkdownStyle, props.additionalCss]}
				remarkPlugins={[remarkGfm, ...(remarkPlugins ?? [])]}
				rehypePlugins={rehypePlugins}
				components={mergedComponents}
				{...rest}
			>
				{children}
			</ReactMarkdown>
		),
		[children],
	);
};

export default PigbotMarkdown;
