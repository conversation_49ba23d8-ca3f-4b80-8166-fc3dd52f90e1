/* eslint-disable react/prop-types */
import React from 'react';
import { Components } from 'react-markdown';
import logger from 'pigbot-core/src/logger';

export const SOPLinkOverride = (handleClick: (documentName: string, pageNumber: string) => void): Components['a'] => {
	// noinspection UnnecessaryLocalVariableJS
	const SOPLink: Components['a'] = ({ children, href, ...rest }) => {
		if (href?.startsWith('https://sops.cloudfarms.com/view')) {
			try {
				const url = new URL(href);
				const documentName = url.searchParams.get('document');
				const pageNumber = url.searchParams.get('page');

				if (documentName && pageNumber) {
					return (
						<a
							href='#'
							onClick={(e) => {
								e.preventDefault();
								handleClick(documentName, pageNumber);
							}}
							{...rest}
						>
							{children}
						</a>
					);
				}
			} catch (err) {
				logger.warn('Invalid URL in markdown link:', href);
			}
		}

		// Default link fallback
		return (
			<a href={href} {...rest}>
				{children}
			</a>
		);
	};

	return SOPLink;
};
