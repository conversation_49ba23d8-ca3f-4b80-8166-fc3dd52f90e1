import React, { useState } from 'react';
import { hash } from 'ohash';

export const ExpandableText: React.FC<{ text: string; maxLength: number }> = ({ text, maxLength }) => {
	const [isExpanded, setIsExpanded] = useState(false);

	if (text.length <= maxLength) {
		return <>{text}</>;
	}

	return (
		<>
			{(isExpanded ? text : text.substring(0, maxLength) + '...') + ' '}
			<a
				href={`show-more-${hash(text)}`}
				onClick={(e) => {
					e.preventDefault();
					setIsExpanded(!isExpanded);
				}}
			>
				{isExpanded ? ' Show less' : ' Show more'}
			</a>
		</>
	);
};
