/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import Card from 'antd/lib/card';
import Statistic from 'antd/lib/statistic';
import 'antd/lib/statistic/style/index.less';

import React from 'react';
import { Usage } from 'pigbot-core/src/llm/LlmCommon';

const RequestsCostCount = 100;

export function UsageStatistics({ usage }: { usage: Usage }) {
	return (
		<div
			css={css`
				display: flex;
				flex-direction: row;
				gap: 10px;
			`}
		>
			<Card title={<b>{`Cost per ${RequestsCostCount} requests`}</b>} size='small'>
				<div
					css={css`
						display: flex;
						flex-direction: row;
						gap: 10px;
					`}
				>
					<Statistic title='Total' suffix='$' value={Math.round((usage.inputCost + usage.outputCost) * RequestsCostCount * 10) / 10} />
					<Statistic title='Input' suffix='$' value={Math.round(usage.inputCost * RequestsCostCount * 10) / 10} />
					<Statistic title='Output' suffix='$' value={Math.round(usage.outputCost * RequestsCostCount * 10) / 10} />
				</div>
			</Card>
			<Card title={<b>{`Tokens usage`}</b>} size='small'>
				<div
					css={css`
						display: flex;
						flex-direction: row;
						gap: 10px;
					`}
				>
					<Statistic title='Input' value={usage.inputTokens} />
					<Statistic title='Cached' value={usage.cachedTokens} />
					<Statistic title='Output' value={usage.outputTokens} />
				</div>
			</Card>
		</div>
	);
}
