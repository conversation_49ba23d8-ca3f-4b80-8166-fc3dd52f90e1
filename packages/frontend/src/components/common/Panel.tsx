import React from 'react';
import { CollapsePanelProps } from 'antd/lib/collapse/CollapsePanel';
import Collapse from 'antd/lib/collapse';

/**
 * This Panel component is a wrapper around the antd Collapse.Panel component
 * that doesn't cause error.
 */
export const Panel: React.FC<CollapsePanelProps & { children: React.ReactNode }> = (props) => {
	const { Panel } = Collapse;
	return <Panel {...props} />;
};
