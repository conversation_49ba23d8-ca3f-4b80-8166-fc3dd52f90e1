import { TRPCClientErrorLike } from '@trpc/client';
import Alert from 'antd/lib/alert';
import React from 'react';
import { ApolloError } from '@apollo/client';

/**
 * Display an error message when loading data fails.
 */
export function LoadingError(props: {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	error: TRPCClientErrorLike<any> | ApolloError | string;
}) {
	return (
		<Alert
			type='error'
			showIcon
			message={
				<div>
					{typeof props.error === 'string' ? props.error : props.error.message}
					<br />
					Please try again later or contact support.
				</div>
			}
		/>
	);
}
