/** @jsxImportSource @emotion/react */
import React, { ReactNode } from 'react';
import { observer } from 'mobx-react-lite';
import { css } from '@emotion/react';
import { FixedIcon } from '@src/components/common/FixedIcon';

type AnalysisComponentProps = {
	analysisComponent: ReactNode;
	isOpened: () => boolean;
	toggleOpened: () => void;
};

const tableStyle = css`
	table {
		width: 100%;
		border-collapse: collapse;
		font-family: Arial, sans-serif;
		font-size: 14px;
		text-align: left;
	}

	th,
	td {
		border: 1px solid #ddd;
		padding: 4px;
		vertical-align: middle;
	}

	th {
		background-color: #f0f0f0ff;
		font-weight: bold;
	}

	tr:nth-of-type(even) {
		background-color: #f0f0f0;
	}

	tr:hover {
		background-color: #f1f1f1;
	}

	thead {
		background-color: #e9e9e9;
		border-bottom: 2px solid #ddd;
	}
`;

export const AnalysisComponent: React.FC<AnalysisComponentProps> = observer(({ analysisComponent, isOpened, toggleOpened }) => {
	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				cursor: pointer;
				background-color: #eee;
				padding: 8px;
				border-radius: 15px;
			`}
			onClick={() => toggleOpened()}
		>
			<div css={css``}>
				{isOpened() ? (
					<div>
						<FixedIcon
							type='down'
							css={css`
								padding-right: 5px;
							`}
						/>
						Hide Analysis
					</div>
				) : (
					<div>
						<FixedIcon
							type='right'
							css={css`
								padding-right: 5px;
							`}
						/>
						Show Analysis
					</div>
				)}
			</div>
			{isOpened() && (
				<div
					css={[
						css`
							margin-top: 10px;
							background-color: #fafafa;
							border-radius: 15px;
							padding-left: 10px;
							padding-right: 10px;
							cursor: default;
						`,
						tableStyle,
					]}
					onClick={(e) => e.stopPropagation()}
				>
					{analysisComponent}
				</div>
			)}
		</div>
	);
});
