/** @jsxImportSource @emotion/react */
import React, { useState } from 'react';
import { match, P } from 'ts-pattern';
import { Pending } from '@src/components/Pending';
import shadow from 'react-shadow';
import PigbotMarkdown from '@src/components/common/PigbotMarkdown';
import Timeline from 'antd/lib/timeline/Timeline';
import 'antd/lib/timeline/style/index.less';
import { UsageStatistics } from '@src/components/common/UsageStatistics';
import { css } from '@emotion/react';
import { EffReportSummaryStep, EffReportSummaryStepValues } from 'pigbot-core/src/eff-report/EffReportSummaryResponseChunk';
import { AnalysisComponent } from '@src/components/report_assistant/AnalysisComponent';
import Alert from 'antd/lib/alert';
import 'antd/lib/popover/style/index.less';
import { observer } from 'mobx-react-lite';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import { FixedIcon } from '@src/components/common/FixedIcon';
import { splitByHorizontalRule } from 'pigbot-core/src/utils/markdownUtils';
import { EffReportSummaryViewerState } from '@src/components/report_assistant/EffReportSummaryViewerState';
import { SOPLinkOverride } from '../common/MarkdownOverrides';
import { AnalysisOpenedStates } from '@src/components/report_assistant/AnalysisOpenedStates';
import { ExpandableText } from '@src/components/common/ExpandableText';

/**
 * Converts step to text
 */
function step2Text(step: EffReportSummaryStep): string {
	switch (step) {
		case 'IdentifyingIssues':
			return 'Identifying issues';
		case 'Summarizing':
			return 'Preparing summary';
		case 'getRelevantData':
			return 'Collecting additional data';
		case 'AnalyzeCauses':
			return 'Analyzing';
	}
}

type EffReportSummaryViewData = ReturnType<typeof extractDataFromEffReportAnalysisArray>;

/**
 * Render info text in an unobtrusive way.
 */
function SimpleInfo({ children }: { children: string }) {
	return (
		<div
			css={css`
				padding: 10px;
			`}
		>
			<FixedIcon type='info-circle' theme='twoTone' /> {children}
		</div>
	);
}

/**
 * Renders eff report summary response stream with a timeline to show the progress
 */
export const EffReportSummaryView: React.FC<{
	data: EffReportSummaryViewData;
	state: EffReportSummaryViewerState;
	internalToolsVisible: boolean;
	analysisOpenedStates: AnalysisOpenedStates;
	handleSOPLinkClick: ((documentName: string, pageNumber: string) => void) | null;
	handleIssueMouseOver: ((index: number, title: string, relevantData: string) => void) | null;
}> = observer((props) => {
	// Extract data from the stream
	const data = props.data;
	const responsesSplit = data.response ? splitByHorizontalRule(data.response) : undefined;
	const responseStreamComplete = data.responseId != null;

	const timelinePageLink = <a href='/#/farmTimeline'>here</a>;

	const farmTimelineInfo = match(data.farmTimeline)
		.with(Pending, () => null)
		.with(P.array(), (farmTimeline) => {
			const nonEmptyTimeline = farmTimeline.length > 0;

			return (
				<Alert
					message={
						nonEmptyTimeline ? (
							<shadow.div>
								<strong>Farm timeline</strong>
								<ul>
									{farmTimeline.map((entry) => (
										<li key={entry.description}>
											<strong>
												{entry.to ?? 'Ongoing'} - {entry.from}
											</strong>
											: <ExpandableText maxLength={500} text={entry.description} />
										</li>
									))}
								</ul>
								The timeline can be updated {timelinePageLink}.
							</shadow.div>
						) : (
							<span>
								This farm has no timeline records. The timeline can be used to provide the assistant with more context about the farm, such
								as disease outbreaks, labor shortages, and other important information. The timeline can be updated {timelinePageLink}.
							</span>
						)
					}
				/>
			);
		})
		.exhaustive();

	const [showConflictingGoals, setShowConflictingGoals] = useState(false);

	const dataIssuesWarning = data.conflictingGoals && (
		<Alert
			type='warning'
			closable={true}
			message={
				<>
					<p>
						<strong>Conflicting goals</strong>
					</p>
					{showConflictingGoals ? (
						<>
							<PigbotMarkdown>{data.conflictingGoals.conflictingGoals.map((str) => `- ${str}`).join('\n')}</PigbotMarkdown>
							<a
								href={`hide-goals-${data.conflictingGoals.hash}`}
								onClick={(e) => {
									e.preventDefault();
									setShowConflictingGoals(false);
								}}
							>
								Hide ▲
							</a>
						</>
					) : (
						<>
							{data.conflictingGoals.conflictingGoals.length} potentially conflicting goals detected.{' '}
							<a
								href={`show-goals-${data.conflictingGoals.hash}`}
								onClick={(e) => {
									e.preventDefault();
									setShowConflictingGoals(true);
								}}
							>
								Show ▼
							</a>
						</>
					)}
				</>
			}
		/>
	);

	// (data as any).response = null; // Using this to test loading UI

	const ButtonRowCss = css`
		display: flex;
		gap: 1rem;
		flex-direction: row;
	`;
	const content =
		data.response && data.response.length > 100 ? (
			<>
				{responsesSplit?.map((response, index) => {
					const isFullyRendered = index < responsesSplit.length - 1 || responseStreamComplete;

					return (
						<div
							key={index}
							id={`issue-${data.responseId}-${index}`}
							onMouseEnter={() => {
								if (data.causeAnalyses?.[index] && data.issues?.issues?.[index] && props.handleIssueMouseOver != null) {
									const title = data.issues.issues[index].title;
									props.handleIssueMouseOver(index, title, data.causeAnalyses[index].relevantData);
								}
							}}
						>
							<PigbotMarkdown components={props.handleSOPLinkClick ? { a: SOPLinkOverride(props.handleSOPLinkClick) } : undefined}>
								{response}
							</PigbotMarkdown>
							{isFullyRendered && data.causeAnalyses?.[index] && (
								<AnalysisComponent
									analysisComponent={
										<PigbotMarkdown components={props.handleSOPLinkClick ? { a: SOPLinkOverride(props.handleSOPLinkClick) } : undefined}>
											{data.causeAnalyses[index].fullAnalysis}
										</PigbotMarkdown>
									}
									isOpened={() => props.analysisOpenedStates.isAnalysisOpen(index)}
									toggleOpened={() => props.analysisOpenedStates.toggleAnalysis(index)}
								/>
							)}
							{isFullyRendered && <hr />}
						</div>
					);
				})}
				{data.responseId && data.farmTimeline?.length == 0 && (
					<Alert
						closable
						message={
							<div>
								If you notice inaccuracies in the analysis that could be addressed by providing additional farm context (e.g., disease
								outbreaks or labor shortages), please visit the Farm Timeline page {timelinePageLink} to add relevant information. This
								helps improve the accuracy of Virtual Farm Assistant.
							</div>
						}
					/>
				)}
				{data.responseId && (
					<div css={ButtonRowCss}>
						<div
							css={css`
								flex: 1;
							`}
						></div>

						{props.state.completionMax > 0 ? (
							<span>
								{props.state.completionIdx + 1}/{props.state.completionMax + 1}
							</span>
						) : null}
					</div>
				)}

				{props.internalToolsVisible && data.usage && <UsageStatistics usage={data.usage} />}
			</>
		) : (
			<>
				<Timeline
					pending={`${step2Text(data.currentStep)} ...`}
					css={css`
						padding: 10px;
					`}
				>
					{EffReportSummaryStepValues.filter((step, stepIdx) => stepIdx < EffReportSummaryStepValues.indexOf(data.currentStep)).map(
						(step) => (
							<Timeline.Item key={step}>{step2Text(step)}</Timeline.Item>
						),
					)}
				</Timeline>
				<SimpleInfo>Complete analysis of the report takes around 5 minutes.</SimpleInfo>
			</>
		);

	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				gap: 10px;
			`}
		>
			{farmTimelineInfo}
			{dataIssuesWarning}
			{data.issues && (
				<shadow.div>
					<h3>Summary{!data.causeAnalyses && ' (WIP)'}</h3>
					<ul>
						{data.issues.issues.map((issue, index) => {
							const sectionId = `issue-${data.responseId}-${index}`;
							const summary =
								data.causeAnalyses && data.causeAnalyses[index]?.singleSentenceSummary
									? `${issue.summary} ${data.causeAnalyses[index].singleSentenceSummary}`
									: `${issue.summary} ...`;

							return (
								<li key={index}>
									<strong>{String.fromCharCode(65 + index) + '. ' + issue.title}</strong>: {summary}{' '}
									{responseStreamComplete && (
										<a
											href={`#${sectionId}`}
											onClick={(e) => {
												// Prevent default anchor click behavior because it doesn't work correctly in pigman webapp
												// and replace it with JavaScript implementation
												e.preventDefault();
												const element = document.getElementById(sectionId);
												element?.scrollIntoView({ behavior: 'smooth' });
											}}
										>
											Details ↓
										</a>
									)}
								</li>
							);
						})}
					</ul>
				</shadow.div>
			)}
			<shadow.div>
				<hr />
			</shadow.div>
			{content}
		</div>
	);
});
