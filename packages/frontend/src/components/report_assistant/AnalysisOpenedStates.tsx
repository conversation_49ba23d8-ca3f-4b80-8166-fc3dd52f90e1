import { makeAutoObservable } from 'mobx';

export class AnalysisOpenedStates {
	// If the state is not initialized, and user goes to feedback page, the page will initialize the state and open only analyses with feedback. See ResponseWithFeedback.tsx
	openedAnalysis?: Set<number>;

	constructor() {
		makeAutoObservable(this);
	}

	isInitialized() {
		return this.openedAnalysis !== undefined;
	}

	initializeAnalysis() {
		this.openedAnalysis = new Set();
	}

	openAnalysis(index: number) {
		if (!this.openedAnalysis) this.initializeAnalysis();
		this.openedAnalysis!.add(index);
	}

	closeAnalysis(index: number) {
		if (!this.openedAnalysis) this.initializeAnalysis();
		this.openedAnalysis!.delete(index);
	}

	isAnalysisOpen(index: number) {
		return this.openedAnalysis?.has(index) || false;
	}

	toggleAnalysis(index: number) {
		if (this.isAnalysisOpen(index)) this.closeAnalysis(index);
		else this.openAnalysis(index);
	}
}