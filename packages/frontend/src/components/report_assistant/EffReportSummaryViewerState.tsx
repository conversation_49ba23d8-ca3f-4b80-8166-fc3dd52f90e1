import { makeAutoObservable } from 'mobx';

export class EffReportSummaryViewerState {
	completionIdx: number = 0;
	completionMax: number = 0;
	/**
	 * Sections hidden from the PDF
	 */
	hiddenSections = new Set<string>();

	constructor() {
		makeAutoObservable(this);
	}

	regenerate() {
		this.completionIdx = this.completionMax + 1;
		this.completionMax = this.completionMax + 1;
		this.hiddenSections.clear();
	}

	toggleSection(sectionId: string) {
		if (this.hiddenSections.has(sectionId)) {
			this.hiddenSections.delete(sectionId);
		} else {
			this.hiddenSections.add(sectionId);
		}
	}
}
