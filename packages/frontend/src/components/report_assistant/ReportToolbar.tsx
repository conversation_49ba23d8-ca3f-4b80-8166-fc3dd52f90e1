/** @jsxImportSource @emotion/react */
import React from 'react';
import { css } from '@emotion/react';
import FrontendConfig from '@src/common/FrontendConfig';
import 'antd/lib/alert/style/index.less';
import Button from 'antd/lib/button';
import Popover from 'antd/lib/popover';
import Checkbox from 'antd/lib/checkbox';
import { FeedbackState } from './FeedbackMode';
import { observer } from 'mobx-react-lite';
import { EffReportSummaryViewerState } from './EffReportSummaryViewerState';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import { useUserContext } from '@src/components/Providers';

type EffReportSummaryViewData = ReturnType<typeof extractDataFromEffReportAnalysisArray>;

interface ReportToolbarProps {
	data: EffReportSummaryViewData;
	editFeedback: ((feedbackState: FeedbackState) => void) | null;
	viewState: EffReportSummaryViewerState;
	disableButtons: boolean;
	displayRegenerateButton: boolean;
}

export const ReportToolbar: React.FC<ReportToolbarProps> = observer((props) => {
	const userContext = useUserContext();

	const downloadPdfButton = (
		<Popover
			title='Include in PDF'
			content={
				<div
					css={css`
						display: flex;
						flex-direction: column;
						gap: 1rem;
					`}
				>
					<div>
						<Checkbox checked={!props.viewState.hiddenSections.has('summary')} onChange={() => props.viewState.toggleSection('summary')}>
							Summary
						</Checkbox>
						<hr />
						{props.data.issues?.issues.map((issue, index) => {
							const sectionId = `issue-${index}`;
							return (
								<div key={index}>
									<Checkbox
										checked={!props.viewState.hiddenSections.has(sectionId)}
										onChange={() => props.viewState.toggleSection(sectionId)}
									>
										{String.fromCharCode(65 + index) + '. ' + issue.title}
									</Checkbox>
								</div>
							);
						})}
						<hr />
						<Checkbox checked={!props.viewState.hiddenSections.has('analyses')} onChange={() => props.viewState.toggleSection('analyses')}>
							In-depth analyses
						</Checkbox>
					</div>

					<Button
						onClick={() => {
							const url = `${FrontendConfig.BACKEND_BASE_URL}/efficiency-report-pdf/${props.data.responseId}?hiddenSections=${Array.from(
								props.viewState.hiddenSections,
							).join(',')}`;
							window.open(url, '_blank');
						}}
					>
						Download
					</Button>
				</div>
			}
			trigger='click'
		>
			<Button icon='download' size='default' type='default' disabled={props.disableButtons}>
				Download PDF
			</Button>
		</Popover>
	);
	const feedbackButton = props.editFeedback !== null && (
		<Button
			disabled={props.disableButtons}
			icon='notification'
			type='primary'
			onClick={() =>
				props.editFeedback!(
					new FeedbackState(
						props.data.response!,
						props.data.causeAnalyses!.map((a) => a.fullAnalysis),
						props.data.responseId!,
					),
				)
			}
		>
			Give feedback
		</Button>
	);

	const regenerateButton = props.displayRegenerateButton && userContext.role === 'backoffice' && (
		<Button icon='reload' onClick={() => props.viewState.regenerate()}>
			Regenerate
		</Button>
	);

	return (
		<div
			css={css`
				display: flex;
				gap: 0rem 1rem;
				flex-wrap: wrap;
				justify-content: center;
			`}
		>
			{downloadPdfButton}
			{feedbackButton}
			{regenerateButton}
		</div>
	);
});
