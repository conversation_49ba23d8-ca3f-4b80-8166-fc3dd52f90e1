/* @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useEffect, useState } from 'react';
import { observer, useLocalObservable } from 'mobx-react-lite';

import Button from 'antd/lib/button';
import 'antd/lib/button/style/index.less';
import 'antd/lib/alert/style/index.less';
import { match } from 'ts-pattern';
import TrpcReact from '@src/common/TrpcReact';
import Alert from 'antd/lib/alert';
import FrontendConfig from '@src/common/FrontendConfig';
import { SupportChatbot } from '@src/components/supportbot/SupportChatbot';
import PromptEditor from '@src/components/settings/prompt/PromptEditor';
import { PromptsState } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import { makeAutoObservable } from 'mobx';

class State {
	promptState = new PromptsState();

	constructor() {
		makeAutoObservable(this);
	}
}

export const SupportChatbotSettings: React.FC = observer(() => {
	const runDixaReloadQuery = TrpcReact.reloadDixaArticles.useMutation();
	const numberOfChatbotFiles = TrpcReact.numberOfSupportChatbotFiles.useQuery();
	const [reloadIntervalState, setReloadIntervalState] = useState<ReturnType<typeof setInterval> | undefined>(undefined);
	const state = useLocalObservable(() => new State());

	function setReloadInterval() {
		const intervalID = setInterval(() => {
			numberOfChatbotFiles.refetch();
		}, 10 * 1000);
		setReloadIntervalState(intervalID);
	}

	useEffect(() => {
		if (numberOfChatbotFiles.data !== undefined) {
			match(numberOfChatbotFiles.data)
				.with({ type: 'filesNotReady' }, () => {
					if (reloadIntervalState === undefined) {
						runDixaReloadQuery.mutate();
						setReloadInterval();
					}
				})
				.with({ type: 'filesReady' }, () => {
					if (reloadIntervalState !== undefined) {
						clearInterval(reloadIntervalState);
						setReloadIntervalState(undefined);
					}
				})
				.exhaustive();
		}
	}, [numberOfChatbotFiles.data]);

	return (
		<div
			css={css`
				width: 100%;
			`}
		>
			<div
				css={css`
					display: flex;
					flex-direction: column;
					width: 100%;
					height: 100%;
				`}
			>
				<div
					css={css`
						flex-grow: 1;
						padding: 15px;
						justify-content: center;
						overflow: scroll;
					`}
				>
					<PromptEditor
						title='Prompt'
						promptsState={state.promptState}
						templateType='support-chatbot-template'
						isHoldingLevel={false}
						requirement={{
							type: 'mandatory',
						}}
					/>
					<div
						css={css`
							display: flex;
							justify-content: center;
						`}
					>
						<Button type='primary' onClick={() => state.promptState.activate()} disabled={!state.promptState.canActivate}>
							Activate
						</Button>
					</div>
				</div>
				<div
					css={css`
						padding: 15px;
						display: flex;
						flex-direction: row;
					`}
				>
					<div>
						<Button
							type='primary'
							size='large'
							loading={runDixaReloadQuery.isPending || !numberOfChatbotFiles.data}
							onClick={() => {
								runDixaReloadQuery.mutate();
								setReloadInterval();
							}}
						>
							Reload dixa documentation
						</Button>
						{runDixaReloadQuery.error && (
							<Alert
								message='Error'
								description={runDixaReloadQuery.error.message}
								type='error'
								showIcon
								closable
								style={{ marginTop: '10px' }}
							/>
						)}
					</div>
					<div
						css={css`
							flex-grow: 1;
						`}
					/>
					<div
						css={css`
							padding-right: 90px; //To clear button on production in bottom right corner
							font-size: 12px;
							color: gray;
							text-align: right;
						`}
					>
						{numberOfChatbotFiles.data?.type === 'filesReady' && !runDixaReloadQuery.isPending && (
							<div>Number of support chatbot files:{numberOfChatbotFiles.data.numberOfFiles}</div>
						)}
						<div>{FrontendConfig.BUILD_VERSION ?? '20251111111111-no-commit-exists-this-is-for-development'}</div>{' '}
						{/* It is beneficial to see the "commit" message during development */}
					</div>
				</div>
			</div>
			<SupportChatbot
				isChatbotReady={numberOfChatbotFiles.data?.type === 'filesReady' && !runDixaReloadQuery.isPending}
				cfPath='/'
				helpUrl='https://guide.cloudfarms.com/en/articles/13'
			/>
		</div>
	);
});
