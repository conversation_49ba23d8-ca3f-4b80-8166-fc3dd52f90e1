/** @jsxImportSource @emotion/react */
import '../../common/reset.less';
import { css } from '@emotion/react';
import 'antd/lib/alert/style/index.less';
import React, { useState } from 'react';
import Providers from '@src/components/Providers';
import { VFAMainMenu } from './VFAMainMenu';
import { MenuCode } from './MenuItems';
import { FarmAnalysisPage } from './FarmAnalysisPage';

interface Props {
	jwtToken: string;
	languageCode: string;
	dateFormat: string;
}

const VFAMain: React.FC<Props> = (props) => {
	const [activeMenuView, setActiveMenuView] = useState<MenuCode>(MenuCode.FarmAnalysis);

	return (
		<div
			css={css`
				display: flex;
				flex-direction: row;
				flex: 1;
				overflow: hidden;
			`}
			className='antd-reset'
		>
			<VFAMainMenu activeMenuView={activeMenuView} onActiveMenuView={setActiveMenuView} />
			{activeMenuView === MenuCode.FarmAnalysis ? <FarmAnalysisPage dateFormat={props.dateFormat} jwtToken={props.jwtToken} /> : <div />}
		</div>
	);
};

export const ReactComponent = (props: Props) => {
	return (
		<Providers jwtToken={props.jwtToken} languageCode={props.languageCode}>
			<VFAMain {...props} />
		</Providers>
	);
};
