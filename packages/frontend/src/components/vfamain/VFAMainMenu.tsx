/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import 'antd/lib/alert/style/index.less';
import React from 'react';
import Menu, { ClickParam } from 'antd/lib/menu';
import { MenuCode, menuSections } from './MenuItems';

const { SubMenu } = Menu;

interface Props {
	activeMenuView: MenuCode;
	onActiveMenuView: (code: MenuCode) => void;
}

export const VFAMainMenu: React.FC<Props> = (props) => {
	// SubMenu is collapsible unfortunately
	return (
		<>
			<div>
				{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
				<Menu
					mode='inline'
					defaultOpenKeys={menuSections.map((section) => section.key)}
					onClick={(e: ClickParam) => props.onActiveMenuView(e.key as MenuCode)}
					selectedKeys={[props.activeMenuView]}
					css={css`
						width: 160px;
						overflow: hidden;
					`}
				>
					{menuSections.map((section) => (
						// @ts-expect-error antd 3x reports incorrect props with React 18
						<SubMenu key={section.key} mode='inline' title={section.title}>
							{section.items.map((item) => (
								<Menu.Item key={item.code}>{item.name}</Menu.Item>
							))}
						</SubMenu>
					))}
				</Menu>
			</div>
		</>
	);
};
