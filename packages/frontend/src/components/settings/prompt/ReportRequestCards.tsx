/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { EfficiencyReportData } from 'pigbot-core/src/EfficiencyReportData';
import 'antd/lib/collapse/style/index.less';
import Skeleton from 'antd/lib/skeleton';
import 'antd/lib/skeleton/style/index.less';
import Card from 'antd/lib/card';
import 'antd/lib/card/style/index.less';

export interface ReportRequestData {
	id: string;
	farmName: string;
	farm_id: number;
	root_id: number;
	createdAt: string;
	reportData: EfficiencyReportData;
}

const ReportRequestViewer: React.FC<{
	setReportData: (data: ReportRequestData) => void;
	reportRequests: ReportRequestData[];
}> = observer((props) => {
	function isoDateToLocalDateTime(isoDate: string) {
		const date = new Date(isoDate);
		return date.toLocaleString();
	}

	return (
		<>
			<div
				css={css`
					display: flex;
					flex-direction: column;
					overflow: auto;
				`}
			>
				{props.reportRequests.map((d) => {
					const reportId = d.id;

					return (
						<Card
							onClick={() => props.setReportData(d)}
							key={reportId}
							size='small'
							css={css`
								cursor: pointer;

								:hover {
									color: #1890ff;
								}
							`}
						>
							<strong>{d.farmName}</strong>
							<div>At {isoDateToLocalDateTime(d.createdAt)}</div>
							<div>Language: {d.reportData.language}</div>
						</Card>
					);
				}) ?? <Skeleton />}
			</div>
		</>
	);
});

export default ReportRequestViewer;
