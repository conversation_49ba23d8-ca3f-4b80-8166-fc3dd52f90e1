/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import 'antd/lib/input/style/index.less';
import Button from 'antd/lib/button';
import 'antd/lib/button/style/index.less';
import React, { ReactNode, useEffect, useRef } from 'react';
import TextareaAutosize from 'react-textarea-autosize';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { graphql } from '@src/gql';
import { useMutation, useQuery, useSubscription } from '@apollo/client';
import { SYSTEM_ID } from 'pigbot-core/src/Constants';
import { useUserContext } from '@src/components/Providers';
import Skeleton from 'antd/lib/skeleton';
import 'antd/lib/skeleton/style/index.less';
import { PromptsState } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import TrpcReact from '@src/common/TrpcReact';
import Alert from 'antd/lib/alert';
import { LoadingError } from '@src/components/common/LoadingError';
import { Pending } from '@src/components/Pending';

// MobX state class for managing the state of the prompt editor
export class PromptEditorState {
	templateEdit: string | null = null;
	/*
	 * null - no custom template selected. Component may show fallback template if available.
	 * Pending - selectedTemplateId and selectedTemplate will be set via effect
	 * 					when the necessary things are loading via graphql and trpc.
	 */
	selectedTemplateId: string | null | Pending = Pending;
	selectedTemplate: string | null | Pending = Pending;

	isActive: boolean | Pending = Pending;

	// Function to activate the selected template.
	// This has to be in the state because it needs to be passed to the parent component which has the "Activate" button.
	activate: () => void = () => {};
	activateLoading: boolean = false;

	constructor() {
		makeAutoObservable(this);
	}

	// Method to set the template being edited
	setTemplateEdit(template: string) {
		this.templateEdit = template;
	}

	// Method to stop editing the template
	stopEdit() {
		this.templateEdit = null;
	}

	// Method to set the selected template ID
	setSelectedTemplateId(id: string | null) {
		this.selectedTemplateId = id;
	}

	setSelectedTemplate(template: string | null | Pending) {
		this.selectedTemplate = template;
	}

	// Method to set the active state
	setActive(active: boolean) {
		this.isActive = active;
	}

	// Method to set the activation function
	setActivate(activate: () => void) {
		this.activate = activate;
	}

	// Method to set the loading state for activation
	setActivateLoading(loading: boolean) {
		this.activateLoading = loading;
	}

	// Computed property to check if the editor is in edit mode
	get isEditing() {
		return this.templateEdit !== null;
	}
}

// GraphQL query to get prompt templates
const GetPromptTemplates = graphql(`
	query GetPromptTemplates($rootId: Int!, $type: String!) {
		prompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {
			id
			root_id
			template
		}
	}
`);

const GetPromptTemplatesSub = graphql(`
	subscription GetPromptTemplatesSub($rootId: Int!, $type: String!) {
		prompt_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }, order_by: { created_at: asc }) {
			id
			root_id
			template
		}
	}
`);

// Default template is the active system template.
const GetDefaultTemplate = graphql(`
	subscription GetDefaultTemplate($type: String!) {
		active_template(where: { root_id: { _eq: -1 }, type: { _eq: $type } }) {
			prompt_template {
				id
				root_id
				template
			}
		}
	}
`);

// GraphQL mutation to add a new prompt template
const AddPromptTemplate = graphql(`
	mutation AddPromptTemplate($type: String!, $root_id: Int, $template: String!) {
		insert_prompt_template_one(object: { type: $type, root_id: $root_id, template: $template }) {
			id
			root_id
			template
		}
	}
`);

// GraphQL query to get the active template
const GetActiveTemplate = graphql(`
	query GetActiveTemplate($rootId: Int!, $type: String!) {
		active_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {
			root_id
			type
			template_id
		}
	}
`);

const GetActiveTemplateSub = graphql(`
	subscription GetActiveTemplateSub($rootId: Int!, $type: String!) {
		active_template(where: { root_id: { _eq: $rootId }, type: { _eq: $type } }) {
			root_id
			type
			template_id
		}
	}
`);

// GraphQL mutation to activate a template
const ActivateTemplate = graphql(`
	mutation ActivateTemplate($root_id: Int!, $type: String!, $update_by: Int!, $templateId: uuid!) {
		insert_active_template_one(
			object: { root_id: $root_id, type: $type, template_id: $templateId, update_by: $update_by }
			on_conflict: { constraint: current_template_pkey, update_columns: [template_id] }
		) {
			root_id
			template_id
		}
	}
`);

// GraphQL mutation to delete the current active template
const DeleteCurrentActiveTemplate = graphql(`
	mutation DeleteCurrentActiveTemplate($root_id: Int!, $type: String!) {
		delete_active_template_by_pk(root_id: $root_id, type: $type) {
			root_id
			template_id
		}
	}
`);

// PromptEditor component for editing and managing prompt templates
const PromptEditor: React.FC<{
	promptsState: PromptsState;
	title?: ReactNode;
	templateType: string;
	isHoldingLevel: boolean;
	requirement:
		| {
				// Prompt template can't be set to "Not used".
				// If no custom template exists, then the fallback template is loaded from a file.
				type: 'mandatory';
		  }
		| {
				// Prompt template can be set to "Not used".
				// If no custom template exists, then it is possible to specify fallback template.
				type: 'optional';

				// If fallback template is set to null and no custom template exists, then the default is "Not set"
				fallbackTemplate: string | null;
		  };
}> = observer(({ promptsState, title, templateType, isHoldingLevel, requirement }) => {
	const state = promptsState.getPromptEditorState(templateType);

	const userContext = useUserContext();

	const rootId: number = isHoldingLevel ? userContext.rootId : SYSTEM_ID;

	// Fallback prompt is used if the prompt is mandatory.
	// It loaded from a .hbs file in core project.
	const getFallbackPrompt = TrpcReact.getFallbackPrompt.useQuery(templateType, {
		enabled: requirement.type === 'mandatory',
	});

	// Fetch the default template if in holding level.
	// Default template is the active system template.
	const getDefaultTemplate = useSubscription(GetDefaultTemplate, {
		variables: {
			type: templateType,
		},
		skip: !isHoldingLevel, // Skip if at system level.
		fetchPolicy: 'cache-first',
	});
	const { data: defaultTemplateData } = getDefaultTemplate;

	// Fetch the prompt templates based on root ID and template type
	const getPromptTemplates = useQuery(GetPromptTemplates, {
		variables: {
			rootId: rootId,
			type: templateType,
		},
	});
	const { data: templateData, subscribeToMore: subscribeToMoreTemplates } = getPromptTemplates;

	// Cache is necessary for mutations to work properly.
	// Subscriptions don't use cache, so we need to use a query and then update the data via subscription.
	useEffect(() => {
		subscribeToMoreTemplates({
			document: GetPromptTemplatesSub,
			variables: {
				rootId: rootId,
				type: templateType,
			},
			updateQuery: (prev, { subscriptionData }) => {
				if (!subscriptionData.data) return prev;
				return {
					...prev,
					prompt_template: subscriptionData.data.prompt_template,
				};
			},
		});
	}, []);

	const fallbackPrompt = (() => {
		if (requirement.type === 'mandatory') {
			if (getFallbackPrompt.data === Pending) {
				return Pending;
			} else {
				// Fallback prompt must exist if prompt is mandatory, and it is loaded from a file via trpc.
				return [
					{
						id: null,
						template: getFallbackPrompt.data,
					},
				];
			}
		} else {
			// Returns optional fallback prompt from props.
			return [
				{
					id: null,
					template: requirement.fallbackTemplate,
				},
			];
		}
	})();

	// Get prompt templates for holding level.
	function getPromptTemplatesForHolding() {
		if (defaultTemplateData !== Pending && templateData !== Pending && fallbackPrompt !== Pending) {
			const currentActiveSystemTemplate = defaultTemplateData.active_template.map((v) => v.prompt_template);

			// If there is no active system template, then use fallback prompt
			const currentSystemTemplate = currentActiveSystemTemplate.length > 0 ? currentActiveSystemTemplate : fallbackPrompt;

			// First template is always system template (if it exists), then holding templates
			return [...currentSystemTemplate, ...templateData.prompt_template];
		} else {
			return Pending;
		}
	}

	// Get prompt templates for system level
	function getPromptTemplatesForSystem() {
		if (templateData && fallbackPrompt !== Pending) {
			// First template is always fallback template if it exists, then system templates
			return [...fallbackPrompt, ...templateData.prompt_template];
		} else {
			return Pending;
		}
	}

	// Determine which prompt templates to use based on the level
	const promptTemplates:
		| {
				id: string | null;
				template: string | null;
		  }[]
		| undefined = isHoldingLevel ? getPromptTemplatesForHolding() : getPromptTemplatesForSystem();

	// Fetch the active template based on root ID and template type
	const getActiveTemplate = useQuery(GetActiveTemplate, {
		variables: {
			type: templateType,
			rootId: rootId,
		},
	});
	const { data: activeTemplates, subscribeToMore: subscribeToMoreActiveTemplates } = getActiveTemplate;

	useEffect(() => {
		subscribeToMoreActiveTemplates({
			document: GetActiveTemplateSub,
			variables: {
				rootId: rootId,
				type: templateType,
			},
			updateQuery: (prev, { subscriptionData }) => {
				if (!subscriptionData.data) return prev;
				return {
					...prev,
					active_template: subscriptionData.data.active_template,
				};
			},
		});
	}, []);

	// Mutation to add a new prompt template
	const [addPromptTemplate, { loading: saving, error: savingError }] = useMutation(AddPromptTemplate);

	// Mutation to activate a template
	const [activateTemplate, { loading: activateSaving, error: activateSavingError }] = useMutation(ActivateTemplate);

	// Mutation to delete the current active template. This resets the active template to the default system template.
	const [deleteCurrentActiveTemplate, { loading: activateSystemTemplateSaving, error: activateSystemTemplateSavingError }] =
		useMutation(DeleteCurrentActiveTemplate);

	// Update the loading state for activation
	useEffect(() => {
		state.setActivateLoading(activateSaving || activateSystemTemplateSaving);
	}, [activateSaving, activateSystemTemplateSaving]);

	// Determine the active template ID using self-exec function
	const activeId: string | null | Pending = (() => {
		if (activeTemplates === Pending) {
			return Pending;
		} else if (activeTemplates.active_template.length === 1) {
			// If there is an active template in the DB, then that is the activeId
			return activeTemplates.active_template[0].template_id;
		} else if (promptTemplates === Pending) {
			return Pending;
		} else if (promptTemplates.length > 0) {
			// If there is no active template in the DB, then the system template (first one in the array) is the activeId
			return promptTemplates[0].id;
		} else {
			// If there are no templates, then the activeId is null (nothing is active)
			return null;
		}
	})();

	// Set the selected template ID and active state based on the active ID
	useEffect(() => {
		if (activeId !== Pending && state.selectedTemplateId === Pending) {
			state.setSelectedTemplateId(activeId);
		}
		if (activeId !== Pending) {
			state.setActive(state.selectedTemplateId === activeId);
		}
	}, [activeId, state.selectedTemplateId]);

	const isDefaultTemplateSelected = promptTemplates?.[0]?.id === state.selectedTemplateId;

	// Set the activation function based on the selected template ID
	useEffect(() => {
		if (state.selectedTemplateId === Pending) {
			state.setActivate(() => {}); // Function does nothing if the selected template is not yet loaded.
		} else {
			// Check if the default template is selected. It is the first one. Default templates are only available at holding level.

			state.setActivate(() => {
				if (isDefaultTemplateSelected) {
					// Delete the current active template if the default template is selected
					// This resets the active template to the default system template.
					deleteCurrentActiveTemplate({
						variables: {
							root_id: rootId,
							type: templateType,
						},
						update: (cache) => {
							cache.writeQuery({
								query: GetActiveTemplate,
								variables: {
									rootId: rootId,
									type: templateType,
								},
								data: {
									active_template: [],
								},
							});
							state.setActive(true);
						},
					});
				} else {
					// Activate the selected template
					activateTemplate({
						variables: {
							root_id: rootId,
							type: templateType,
							templateId: state.selectedTemplateId!,
							update_by: userContext.userId,
						},
						update: (cache, { data }) => {
							cache.writeQuery({
								query: GetActiveTemplate,
								variables: {
									rootId: rootId,
									type: templateType,
								},
								data: {
									active_template: [
										{
											root_id: rootId,
											type: templateType,
											template_id: data!.insert_active_template_one!.template_id,
										},
									],
								},
							});
							state.setActive(true);
						},
					});
				}
			});
		}
	}, [state.selectedTemplateId]);

	// Find the index of the selected template
	const selectedTemplateIdx = promptTemplates?.findIndex((template) => template.id === state.selectedTemplateId);

	const selectedTemplate = selectedTemplateIdx === Pending ? Pending : promptTemplates?.[selectedTemplateIdx];

	// Set the selected template based on the selected template ID
	useEffect(() => {
		state.setSelectedTemplate(selectedTemplate?.template);
	}, [selectedTemplate?.template]);

	// If the default template changes via subscription, and it is currently selected,
	// then set the selected template ID to the new default template ID.
	useEffect(() => {
		// selectedTemplateIdx === -1 means
		// the selected template is not found in loaded templates which can only happen if the default template is selected,
		// and it is changed via subscription.
		if (isHoldingLevel && selectedTemplateIdx === -1 && defaultTemplateData?.active_template[0]?.prompt_template) {
			state.setSelectedTemplateId(defaultTemplateData.active_template[0].prompt_template.id);
		}
	}, [defaultTemplateData?.active_template[0]?.prompt_template]);

	// Handle saving the edited template
	const handleSave = (template: string) => {
		addPromptTemplate({
			variables: {
				type: templateType,
				template: template,
				root_id: rootId,
			},
			update: (cache, { data }) => {
				const getPromptTemplateOptions = {
					query: GetPromptTemplates,
					variables: { rootId: rootId, type: templateType },
				};

				const existingTemplates = cache.readQuery(getPromptTemplateOptions);

				cache.writeQuery({
					...getPromptTemplateOptions,
					data: {
						prompt_template: [...existingTemplates!.prompt_template, data!.insert_prompt_template_one!],
					},
				});

				state.stopEdit();
				state.setSelectedTemplateId(data!.insert_prompt_template_one!.id);
			},
		});
	};

	const templateEdit = state.templateEdit;

	const isEditMode = templateEdit !== null;

	// Reference for the text area used in edit mode
	const textAreaRef = useRef<HTMLTextAreaElement>(null);

	// Focus on the text area when in edit mode and set the cursor to the end
	useEffect(() => {
		if (textAreaRef.current) {
			textAreaRef.current.focus();
			const length = textAreaRef.current.value.length;
			textAreaRef.current.setSelectionRange(length, length);
		}
	}, [isEditMode]);

	// If any of the queries or mutations have an error, show the error message
	const loadingError =
		getActiveTemplate.error?.message ||
		getPromptTemplates.error?.message ||
		getFallbackPrompt.error?.message ||
		savingError?.message ||
		activateSavingError?.message ||
		activateSystemTemplateSavingError?.message;

	const DisabledLinkButtonCss = css`
		color: #d9d9d9;
		user-select: none;
	`;
	const LinkButtonCss = css`
		cursor: pointer;
		user-select: none;
	`;
	return (
		<div
			css={css`
				padding-bottom: 10px;
			`}
		>
			<div
				css={css`
					color: #000000;
					font-size: 17px;
					padding-bottom: 5px;
				`}
			>
				{title}
			</div>
			{selectedTemplate === Pending ? (
				<Skeleton />
			) : !isEditMode ? (
				selectedTemplate.template === null ? (
					<Alert showIcon message='Not used' />
				) : (
					<div
						css={css`
							white-space: pre-wrap;
							border: 1px solid #d9d9d9;
							padding: 10px;
							border-radius: 4px;
							background: #f4f4f4;
						`}
					>
						{<div>{selectedTemplate.template}</div>}
					</div>
				)
			) : (
				<TextareaAutosize
					ref={textAreaRef}
					disabled={saving}
					rows={1}
					css={css`
						width: 100%;
						padding: 10px;
					`}
					value={templateEdit ?? undefined}
					onChange={(e) => {
						state.setTemplateEdit(e.target.value);
					}}
				/>
			)}
			<div
				css={css`
					margin-top: 10px;
					margin-left: 5px;
					display: flex;
					align-items: center;
					gap: 5px;
				`}
			>
				{!isEditMode ? (
					<>
						{selectedTemplate !== Pending ? (
							<Button onClick={() => state.setTemplateEdit(selectedTemplate?.template ?? '')}>Edit</Button>
						) : null}
						<div
							css={css`
								flex: 1;
							`}
						></div>

						{/*						{selectedTemplateIdx !== undefined && selectedTemplateIdx > 0 && !state.isActive && (
							<Button type='link' onClick={() => {}}>
								Delete
							</Button>
						)}*/}

						{
							// Creates a string that shows whether it is a system prompt, active, both or empty string.
							[isDefaultTemplateSelected && isHoldingLevel && 'Current system prompt', state.isActive && 'Active']
								.filter(Boolean)
								.join(' · ')
						}
						{selectedTemplateIdx != null && selectedTemplateIdx >= 0 && promptTemplates != undefined ? (
							<>
								{selectedTemplateIdx > 0 ? (
									<>
										<span onClick={() => state.setSelectedTemplateId(promptTemplates[0].id)} css={LinkButtonCss}>
											&lt;&lt;
										</span>
										<span onClick={() => state.setSelectedTemplateId(promptTemplates[selectedTemplateIdx - 1].id)} css={LinkButtonCss}>
											&lt;
										</span>
									</>
								) : (
									<>
										<span css={DisabledLinkButtonCss}>&lt;&lt;</span>
										<span css={DisabledLinkButtonCss}>&lt;</span>
									</>
								)}

								<div
									css={css`
										margin: 0 3px;
									`}
								>
									{selectedTemplateIdx + 1} / {promptTemplates.length}
								</div>
								{selectedTemplateIdx < promptTemplates.length - 1 ? (
									<>
										<span onClick={() => state.setSelectedTemplateId(promptTemplates[selectedTemplateIdx + 1].id)} css={LinkButtonCss}>
											&gt;
										</span>
										<span onClick={() => state.setSelectedTemplateId(promptTemplates[promptTemplates.length - 1].id)} css={LinkButtonCss}>
											&gt;&gt;
										</span>
									</>
								) : (
									<>
										<span css={DisabledLinkButtonCss}>&gt;</span>
										<span css={DisabledLinkButtonCss}>&gt;&gt;</span>
									</>
								)}
							</>
						) : null}
					</>
				) : (
					<span
						css={css`
							display: flex;
							gap: 8px;
						`}
					>
						<Button disabled={saving && templateEdit === ''} onClick={() => handleSave(templateEdit!)} type='primary'>
							Save
						</Button>
						<Button disabled={saving} onClick={() => state.stopEdit()}>
							Cancel
						</Button>
					</span>
				)}
				{loadingError && <LoadingError error={loadingError} />}
			</div>
		</div>
	);
});

export default PromptEditor;
