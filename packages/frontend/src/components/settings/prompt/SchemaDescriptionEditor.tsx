/** @jsxImportSource @emotion/react */
import React from 'react';
import { z, ZodTypeAny } from 'zod';
import { css } from '@emotion/react';
import { PromptsState } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import PromptEditor from '@src/components/settings/prompt/PromptEditor';
import { observer } from 'mobx-react-lite';

// Drafted by ChatGPT
interface SchemaDescriptionEditorProps {
	schema: ZodTypeAny;
	promptsState: PromptsState;
	templateType: string;
	isHoldingLevel: boolean;
}

const fieldContainer = css``;

/**
 * Editor for zod schema descriptions.
 * It uses PromptEditor to edit the descriptions.
 */
const SchemaDescriptionEditor: React.FC<SchemaDescriptionEditorProps> = observer(
	({ schema, templateType, promptsState, isHoldingLevel }) => {
		function renderSchema(schema: ZodTypeAny, name: string | undefined, parentPath: string) {
			const path = parentPath ? `${parentPath}.${name}` : (name ?? '');
			const fieldTemplateType = `${templateType}/response/${path}`;

			const description = promptsState.getPromptEditorState(fieldTemplateType).selectedTemplate ?? schema.description;

			function renderField(typeLabel?: string) {
				if (!name) return null;
				return (
					<>
						<PromptEditor
							promptsState={promptsState}
							templateType={fieldTemplateType}
							requirement={{
								type: 'optional',
								fallbackTemplate: description ?? null,
							}}
							isHoldingLevel={isHoldingLevel}
							title={
								<div>
									<strong>{name}</strong>: {typeLabel}
								</div>
							}
						/>
					</>
				);
			}

			if (schema instanceof z.ZodObject) {
				const shape = schema.shape;
				return (
					<div css={fieldContainer}>
						{renderField('object')}
						<ul>
							{Object.keys(shape).map((key) => (
								<li key={key}>{renderSchema(shape[key], key, path)}</li>
							))}
						</ul>
					</div>
				);
			} else if (schema instanceof z.ZodArray) {
				const elementType = schema.element;
				return (
					<div css={fieldContainer}>
						{renderField('array')}
						{renderSchema(elementType, '[element]', path)}
					</div>
				);
			} else if (schema instanceof z.ZodString) {
				return renderField('string');
			} else if (schema instanceof z.ZodNumber) {
				return renderField('number');
			} else if (schema instanceof z.ZodBoolean) {
				return renderField('boolean');
			} else if (schema instanceof z.ZodDate) {
				return renderField('date');
			} else if (schema instanceof z.ZodOptional || schema instanceof z.ZodNullable) {
				const innerType = schema.unwrap();
				const typeLabel = schema instanceof z.ZodOptional ? 'optional' : 'nullable';
				return (
					<div css={fieldContainer}>
						{renderField(typeLabel)}
						{renderSchema(innerType, name, path)}
					</div>
				);
			} else if (schema instanceof z.ZodEnum) {
				return (
					<div css={fieldContainer}>
						{renderField('enum')}
						<div>Options: {schema.options.join(', ')}</div>
					</div>
				);
			} else if (schema instanceof z.ZodLiteral) {
				return (
					<div css={fieldContainer}>
						{renderField('literal')}
						<div>Value: {JSON.stringify(schema.value)}</div>
					</div>
				);
			} else if (schema instanceof z.ZodAny) {
				return renderField('any');
			} else {
				return renderField('unknown type');
			}
		}

		return (
			<>
				<div
					css={css`
						li {
							padding-inline-start: 20px;
						}
					`}
				>
					{renderSchema(schema, undefined, '')}
				</div>
			</>
		);
	},
);

export default SchemaDescriptionEditor;
