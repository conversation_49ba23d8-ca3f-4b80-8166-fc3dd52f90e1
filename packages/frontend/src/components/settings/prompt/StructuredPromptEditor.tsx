/** @jsxImportSource @emotion/react */
import React from 'react';
import { PromptsState } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import PromptEditor from '@src/components/settings/prompt/PromptEditor';
import SchemaDescriptionEditor from '@src/components/settings/prompt/SchemaDescriptionEditor';
import { ZodTypeAny } from 'zod';

export const StructuredPromptEditor: React.FC<{
	promptsState: PromptsState;
	templateType: string;
	isHoldingLevel: boolean;
	schema: ZodTypeAny;
	title?: string;
}> = ({ title, promptsState, templateType, isHoldingLevel, schema }) => {
	return (
		<>
			<PromptEditor
				title={title ?? 'Prompt'}
				promptsState={promptsState}
				templateType={templateType}
				isHoldingLevel={isHoldingLevel}
				requirement={{
					type: 'mandatory',
				}}
			/>
			<h3>Response structure</h3>
			<SchemaDescriptionEditor schema={schema} promptsState={promptsState} templateType={templateType} isHoldingLevel={isHoldingLevel} />
		</>
	);
};
