/* @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { observer } from 'mobx-react-lite';
import React from 'react';
import 'antd/lib/table/style/index.less';
import Skeleton from 'antd/lib/skeleton';
import 'antd/lib/skeleton/style/index.less';
import Card from 'antd/lib/card';
import 'antd/lib/card/style/index.less';
import { FeedbackData } from '@src/components/settings/prompt/ReportOrFeedbackSelector';

const FeedbackCards: React.FC<{
	setFeedbackData: (data: FeedbackData) => void;
	feedbacks: FeedbackData[];
}> = observer((props) => {
	function isoDateToLocalDateTime(isoDate: string) {
		const date = new Date(isoDate);
		return date.toLocaleString();
	}

	return (
		<>
			<div
				css={css`
					display: flex;
					flex-direction: column;
					overflow: auto;
					flex: 1;
				`}
			>
				{props.feedbacks.map((f) => {
					const reportId = f.id;

					return (
						<Card
							onClick={() => props.setFeedbackData(f)}
							key={reportId}
							size='small'
							css={css`
								cursor: pointer;

								:hover {
									color: #1890ff;
								}
							`}
						>
							<div>{f.summary}</div>
							<div>Farm: {f.farm_name}</div>
							<div>At: {isoDateToLocalDateTime(f.created_at)}</div>
							{f.author_name && <div>By: {f.author_name}</div>}
						</Card>
					);
				}) ?? <Skeleton />}
			</div>
		</>
	);
});

export default FeedbackCards;
