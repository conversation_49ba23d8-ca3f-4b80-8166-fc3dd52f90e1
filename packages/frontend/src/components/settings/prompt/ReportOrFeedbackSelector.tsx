/* @jsxImportSource @emotion/react */
import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useUserContext } from '@src/components/Providers';
import { useSubscription } from '@apollo/client';
import Skeleton from 'antd/lib/skeleton';
import Tabs from 'antd/lib/tabs';
import ReportRequestCards, { ReportRequestData } from '@src/components/settings/prompt/ReportRequestCards';
import FeedbackCards from '@src/components/settings/prompt/FeedbackCards';
import { State } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import { graphql } from '@src/gql';
import { LoadingError } from '@src/components/common/LoadingError';
import { TypedDocumentNode } from '@graphql-typed-document-node/core';
import { css } from '@emotion/react';

const { TabPane } = Tabs;

const GetFeedback = graphql(`
	subscription GetFeedbackFrontend($type: String!, $rootId: Int_comparison_exp!) {
		response_feedback(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {
			id
			farm_id
			root_id
			response
			feedback
			summary
			farm_name
			created_at
			author_name
		}
	}
`);

/**
 * Type of GetFeedback graphql subscription response
 */
// The magic to extract the type was suggested by ChatGPT
export type FeedbackData = (typeof GetFeedback extends TypedDocumentNode<infer TData, never> ? TData : never)['response_feedback'][number];

const GetReportRequests = graphql(`
	subscription GetReportRequest($type: String!, $rootId: Int_comparison_exp!) {
		report_request(where: { type: { _eq: $type }, root_id: $rootId }, order_by: { created_at: desc }) {
			farm_name
			farm_id
			root_id
			created_at
			report_data
			country
		}
	}
`);

/**
 * Renders the feedback or reports selector.
 */
export const ReportOrFeedbackSelector: React.FC<{ state: State; global: boolean }> = observer(({ state, global }) => {
	const userContext = useUserContext();

	const getFeedback = useSubscription(GetFeedback, {
		variables: {
			type: 'eff-report',
			rootId: global ? {} : { _eq: userContext.rootId },
		},
	});

	const getReportRequests = useSubscription(GetReportRequests, {
		variables: {
			type: 'eff-report',
			rootId: global ? {} : { _eq: userContext.rootId },
		},
	});

	const reportRequests = getReportRequests.data?.report_request.map(
		(rr) =>
			({
				id: `${rr.farm_name}_${rr.report_data.language}`,
				farmName: rr.farm_name,
				farm_id: rr.farm_id,
				root_id: rr.root_id,
				createdAt: rr.created_at,
				reportData: rr.report_data,
			}) as ReportRequestData,
	);

	useEffect(() => {
		if (state.feedbackOrReports === undefined && getFeedback.data) {
			state.setFeedbackOrReports(getFeedback.data.response_feedback.length > 0 ? 'feedback' : 'reports');
		}
	}, [getFeedback.data?.response_feedback, getReportRequests.data?.report_request]);

	return (
		<div
			css={css`
				display: flex;
				flex: 1;
				flex-direction: column;
			`}
		>
			{state.feedbackOrReports === undefined ? (
				<Skeleton />
			) : (
				<>
					{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
					<Tabs activeKey={state.feedbackOrReports} onChange={(key) => state.setFeedbackOrReports(key)}>
						<TabPane tab='Feedback' key='feedback'></TabPane>
						<TabPane tab='Reports' key='reports'></TabPane>
					</Tabs>

					{state.feedbackOrReports === 'reports' &&
						(reportRequests ? <ReportRequestCards setReportData={state.select} reportRequests={reportRequests} /> : <Skeleton />)}

					{state.feedbackOrReports === 'feedback' &&
						(getFeedback.data ? (
							<FeedbackCards setFeedbackData={state.select} feedbacks={getFeedback.data.response_feedback} />
						) : (
							<Skeleton />
						))}
				</>
			)}
			{getFeedback.error && <LoadingError error={getFeedback.error} />}
			{getReportRequests.error && <LoadingError error={getReportRequests.error} />}
		</div>
	);
});
