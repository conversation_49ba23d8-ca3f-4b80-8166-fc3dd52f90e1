/* @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import pDefer from 'p-defer';

import React, { useEffect, useState } from 'react';

import { observer } from 'mobx-react-lite';
import { vfaSettingsSchema } from 'pigbot-core/src/VFASettingsSchema';
import { graphql } from '@src/gql';
import { useMutation, useQuery } from '@apollo/client';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Checkbox from 'antd/lib/checkbox';
import 'antd/lib/checkbox/style/index.less';
import Button from 'antd/lib/button';
import 'antd/lib/button/style/index.less';
import { LoadingError } from '@src/components/common/LoadingError';
import Skeleton from 'antd/lib/skeleton';
import { useUserContext } from '@src/components/Providers';

const GetSetting = graphql(`
	query GetSettings($env: String!, $rootId: Int!) {
		setting(where: { _and: { root_id: { _eq: $rootId } }, env: { _eq: $env } }) {
			setting
		}
	}
`);

const UpsertSetting = graphql(`
	mutation upsertSetting($env: String!, $rootId: Int!, $setting: jsonb!) {
		insert_setting_one(
			object: { root_id: $rootId, env: $env, setting: $setting }
			on_conflict: { constraint: setting_pkey, update_columns: [setting] }
		) {
			setting
		}
	}
`);

export const VFASettingsSection: React.FC = observer(() => {
	const userContext = useUserContext();

	const getSettings = useQuery(GetSetting, {
		variables: {
			env: userContext.env,
			rootId: userContext.rootId,
		},
		fetchPolicy: 'network-only',
	});

	const [dbSettingsDefer] = useState(() => pDefer());

	// Load settings data into a promise, because react-hook-form uses Promise for default values.
	useEffect(() => {
		if (getSettings.data) {
			// Use an empty object if settings don't exist in the db yet.
			// Zod will fill in the default values.
			const dbSettings = getSettings.data.setting[0]?.setting ?? {};

			dbSettingsDefer.resolve(vfaSettingsSchema.parse(dbSettings));
		}
	}, [getSettings.data]);

	const [upsertSettings, upsertSettingsState] = useMutation(UpsertSetting);

	const form = useForm({
		resolver: zodResolver(vfaSettingsSchema),
		defaultValues: () => dbSettingsDefer.promise,
		disabled: upsertSettingsState.loading,
	});

	return (
		<Skeleton loading={getSettings.loading}>
			<form
				onSubmit={form.handleSubmit(async (d) => {
					await upsertSettings({
						variables: {
							env: userContext.env,
							setting: d,
							rootId: userContext.rootId,
						},
					});
				})}
				css={css`
					display: flex;
					flex-direction: column;
					margin: 0 10px;
					gap: 15px;
				`}
			>
				<h1>{userContext.rootName} settings</h1>
				{/*TODO When we have more fields, wrap Controller in something or upgrade antd and use integration*/}
				<Controller
					name='useSegesManual'
					control={form.control}
					render={({ field }) => (
						<Checkbox checked={field.value} {...field}>
							Use SEGES manual
						</Checkbox>
					)}
				/>
				<Controller
					name='useSouthWestVetsSOPs'
					control={form.control}
					render={({ field }) => (
						<Checkbox checked={field.value} {...field}>
							Use South West Vets SOPs
						</Checkbox>
					)}
				/>
				{upsertSettingsState.error && <LoadingError error={upsertSettingsState.error} />}
				<Button type='primary' htmlType='submit'>
					Apply settings
				</Button>
				{getSettings.error && <LoadingError error={getSettings.error} />}
			</form>
		</Skeleton>
	);
});
