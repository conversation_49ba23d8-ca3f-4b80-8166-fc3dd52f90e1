/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import 'antd/lib/alert/style/index.less';
import { VFASettingsSection } from '@src/components/settings/vfasettings/VFASettingsSection';
import React, { useState } from 'react';
import { SUPPORT_CHATBOT, VFA_SETTINGS } from 'pigbot-core/src/Constants';
import Menu, { ClickParam } from 'antd/lib/menu';
import Providers from '@src/components/Providers';
import '../../common/reset.less';
import { EfficiencyPromptManagement as VFAPromptManagement } from '@src/components/settings/prompt/EfficiencyPromptManagement';
import { SupportChatbotSettings } from '@src/components/supportbot/SupportChatbotSettings';

interface MenuItem {
	name: string;
	code: string;
}

interface Props {
	jwtToken: string;
	languageCode: string;
}

const leftMenu: MenuItem[] = [
	{ name: 'VFA (holding)', code: 'vfa-holding' },
	{ name: 'VFA (global)', code: 'vfa-global' },
	{ name: 'Support chatbot', code: SUPPORT_CHATBOT },
	{ name: 'Settings', code: VFA_SETTINGS },
];

const Settings: React.FC = () => {
	const [activeType, setActiveType] = useState('vfa-holding');

	return (
		<div
			className='antd-reset'
			css={css`
				display: flex;
				flex-direction: row;
				flex: 1;
				overflow: hidden;
			`}
		>
			<div>
				{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
				<Menu
					mode='vertical'
					onClick={(e: ClickParam) => setActiveType(e.key)}
					selectedKeys={[activeType]}
					css={css`
						width: 160px;
					`}
				>
					{leftMenu.map((menuItem) => (
						<Menu.Item key={menuItem.code}>{menuItem.name}</Menu.Item>
					))}
				</Menu>
			</div>
			{activeType === 'vfa-holding' && <VFAPromptManagement global={false} />}
			{activeType === 'vfa-global' && <VFAPromptManagement global={true} />}
			{activeType === SUPPORT_CHATBOT && <SupportChatbotSettings />}
			{activeType === VFA_SETTINGS && <VFASettingsSection />}
		</div>
	);
};

export const ReactComponent = (props: Props) => {
	return (
		<Providers jwtToken={props.jwtToken} languageCode={props.languageCode}>
			<Settings />
		</Providers>
	);
};
