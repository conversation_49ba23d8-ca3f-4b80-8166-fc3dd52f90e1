import { ApolloClient, HttpLink, InMemoryCache, split } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';
import { getMainDefinition } from '@apollo/client/utilities';
import FrontendConfig from '@src/common/FrontendConfig';

export default function (token: string) {
	const { protocol, hostname, port } = window.location;

	const getUrlWithoutProtocol = (): string => {
		if (FrontendConfig.GRAPHQL_URL!.startsWith('/')) {
			return `${hostname}${port === '' ? '' : `:${port}`}${FrontendConfig.GRAPHQL_URL}`;
		} else {
			return FrontendConfig.GRAPHQL_URL!;
		}
	};

	const secureExtension = protocol.substring(4, 5) === 's' ? 's' : '';
	const urlWithoutProtocol = getUrlWithoutProtocol();

	const httpLink = new HttpLink({
		uri: `http${secureExtension}://${urlWithoutProtocol}`,
	});

	const wsLink = new GraphQLWsLink(
		createClient({
			url: `ws${secureExtension}://${urlWithoutProtocol}`,
			shouldRetry: () => true,
			connectionParams: {
				headers: {
					Authorization: `Bearer ${token}`,
				},
			},
			// }
		}),
	);

	const authLink = setContext((_, { headers }) => {
		return {
			headers: {
				...headers,
				Authorization: `Bearer ${token}`,
			},
		};
	});

	const splitLink = split(
		({ query }) => {
			const definition = getMainDefinition(query);
			return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
		},
		wsLink,
		authLink.concat(httpLink),
	);

	return new ApolloClient({
		link: splitLink,
		cache: new InMemoryCache(),
	});
}
