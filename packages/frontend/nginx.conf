worker_processes 1;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  sendfile on;
  keepalive_timeout 65;

  # Gzip Settings
  gzip on;
  gzip_vary on;
  gzip_proxied any;
  gzip_comp_level 6;
  gzip_buffers 16 8k;
  gzip_http_version 1.1;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

  # Caching Settings
  open_file_cache max=1000 inactive=20s;
  open_file_cache_valid 30s;
  open_file_cache_min_uses 2;
  open_file_cache_errors on;

  # Enable ETags
  etag on;

  server {
    listen 80;

    location / {
      root /usr/share/nginx/html;
      index index.html index.htm;

      # Cache Control Headers
      add_header Cache-Control "no-cache";

      try_files $uri $uri/ /index.html;
    }
  }
}