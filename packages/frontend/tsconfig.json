{"extends": "../../tsconfig.json", "compilerOptions": {"jsx": "react", "allowJs": true, "target": "ES2022", "module": "ESNext", "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "useDefineForClassFields": true, "strict": true, "paths": {"*": ["node_modules/*"], "@assets/*": ["./assets/*"], "@src/*": ["./src/*"]}}, "include": ["src/**/*", "dev/**/*"]}