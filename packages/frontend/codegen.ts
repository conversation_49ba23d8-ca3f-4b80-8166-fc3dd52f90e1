import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
	overwrite: true,
	schema: [
		{
			'http://localhost:8080/v1/graphql': {
				headers: {
					'x-hasura-admin-secret': 'myadminsecretkey',
				},
			},
		},
	],
	documents: 'src/**/*.tsx',
	generates: {
		'src/gql/': {
			preset: 'client',
			config: {
				scalars: {
					uuid: 'string',
					timestamptz: 'string',
				},
			},
		},
	},
};

export default config;
