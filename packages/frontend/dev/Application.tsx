/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { efficiencyReportData } from 'pigbot-core/test/EfficiencyReportData';
import DevJwtToken from './DevJwtToken';
import * as Settings from '@src/components/settings/Settings';
import Tabs from 'antd/lib/tabs';
import 'antd/lib/tabs/style/index.less';
import ExtReportAssistant from './ExtReportAssistant';
import ExtPromptManagement from './ExtPromptManagement';
import * as VFAMain from '../src/components/vfamain/VFAMain';
import './index.less';
import '../src/common/reset.less';
import FrontendConfig from '@src/common/FrontendConfig';

// import 'antd/lib/style';

const { TabPane } = Tabs;

export const DevLanguageCode = process.env.DEV_LANG!;

function App() {
	const location = useLocation();
	const navigate = useNavigate();

	// Get the tab key from the URL, default to "1"
	const activeKey = new URLSearchParams(location.search).get('tab') || 'pm';

	const handleTabChange = (key: string) => {
		// Update the URL when the tab changes
		navigate(`?tab=${key}`);
	};

	useEffect(() => {
		// Ensure the tab key in the URL matches the tab state
		if (location.search === '') {
			navigate(`?tab=pm`, { replace: true });
		}
	}, [location, navigate]);

	const renderContent = () => {
		switch (activeKey) {
			case 'pm':
				return <Settings.ReactComponent jwtToken={DevJwtToken} languageCode={DevLanguageCode} />;
			case 'ext-ta':
				return <ExtReportAssistant />;
			case 'ext-pm':
				return <ExtPromptManagement />;
			case 'vfa-main':
				return <VFAMain.ReactComponent jwtToken={DevJwtToken} languageCode={DevLanguageCode} dateFormat={'DD.MM.YYYY'} />;
			default:
				return null;
		}
	};

	return (
		<div
			css={css`
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;
			`}
			className='antd-reset'
		>
			{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
			<Tabs activeKey={activeKey} onChange={handleTabChange} className='BBBBB'>
				<TabPane tab='Prompt management' key='pm'></TabPane>
				<TabPane tab='EXT Prompt management' key='ext-pm'></TabPane>
				<TabPane tab='VFA main' key='vfa-main'></TabPane>
			</Tabs>
			{renderContent()}
		</div>
	);
}

export default App;
