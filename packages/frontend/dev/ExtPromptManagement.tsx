/** @jsxImportSource @emotion/react */
import React, { useEffect, useRef } from 'react';
import DevJwtToken from './DevJwtToken';
import { DevLanguageCode } from './Application';
import { render } from '@src/components/settings/SettingsRender';

export default function ExtPromptManagement() {
	const divRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (divRef.current) {
			render(divRef.current, DevJwtToken, DevLanguageCode);
		}
	}, []);

	return (
		<>
			<div ref={divRef}></div>
		</>
	);
}
