import { <PERSON>J<PERSON> } from 'jsrsasign';

const generateJwt = (secret: string, payload: object, header: object = { alg: 'HS256', typ: 'JWT' }) => {
	const sHeader = JSON.stringify(header);
	const sPayload = JSON.stringify(payload);

	const sJWT = KJUR.jws.JWS.sign('HS256', sHeader, sPayload, secret);

	return sJWT;
};

// Get environment from process.env.DEV_JWT_ENV or default to 'dev'
const jwtEnv = process.env.DEV_JWT_ENV || 'dev';

const secret = '3EK6FD+o0+c7tzBNVfjpMkNDi2yARAAKzQlk8O2IKoxQu4nF7EdAh8s3TwpHwrdWT6R';
const payload = {
	sub: '1234567890',
	name: '<PERSON>',
	iat: Math.floor(Date.now() / 1000),
	exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 1 day expiration
	rootId: '100412',
	userId: '999',
	role: 'backoffice',
	farmId: '100430',
	farmName: 'testFarm1',
	countryCode: 'USA',
	rootName: 'My Holding',
	env: jwtEnv,
};

export default generateJwt(secret, payload);
