import React from 'react';
import { createRoot } from 'react-dom/client';
import Application from './Application';
import { BrowserRouter as Router } from 'react-router-dom';

// Say something
console.info('[ERWT] : Renderer execution started');

// Application to Render
const app = (
	<Router>
		<Application />
	</Router>
);

// Render application in DOM
// ReactDOM.render(app, document.getElementById('app'));
createRoot(document.getElementById('app')!).render(app);
