# Base image for building Node.js applications
FROM node:20 AS base

RUN npm i -g corepack@latest
RUN corepack enable pnpm

# TODO puppeteer shouldn't be in base image that is used everywhere. Need to figure out how to make it run once in `server-builder`
# Installing dependencies for puppeteer - library we are using to generate pdf on server side
RUN apt-get update && apt-get install -y --no-install-recommends \
  fonts-liberation \
  libasound2 \
  libatk-bridge2.0-0 \
  libatk1.0-0 \
  libcups2 \
  libdrm2 \
  libgbm1 \
  libgtk-3-0 \
  libnspr4 \
  libnss3 \
  libx11-xcb1 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  xdg-utils \
  libu2f-udev \
  libxshmfence1 \
  libglu1-mesa \
  chromium \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app

# Copy pnpm configuration and lockfile
COPY pnpm-workspace.yaml ./
COPY package.json pnpm-lock.yaml ./

# Copy all package.json files for workspaces
COPY /package.json ./
COPY /packages/core/package.json ./packages/core/
COPY /packages/backend/package.json ./packages/backend/
COPY /packages/frontend/package.json ./packages/frontend/
COPY /packages/worker/package.json ./packages/worker/

# Install dependencies for all packages
RUN pnpm install

COPY . .

# Build frontend
FROM base AS frontend-builder
WORKDIR /app/packages/frontend
ARG BUILD_VERSION
ARG FEDERATION_MODULE_NAME
ARG BACKEND_BASE_URL

RUN pnpm run build --env BUILD_VERSION=$BUILD_VERSION --env FEDERATION_MODULE_NAME=$FEDERATION_MODULE_NAME --env BACKEND_BASE_URL=$BACKEND_BASE_URL

# Final image for frontend
FROM docker.io/library/nginx:alpine AS frontend
COPY --from=frontend-builder /app/packages/frontend/dist /usr/share/nginx/html
COPY --from=frontend-builder /app/packages/frontend/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

# Build server side code
FROM base AS server-builder
RUN pnpm run build-server

# Final image for backend
FROM server-builder AS backend
WORKDIR /app/packages/backend
EXPOSE 9100
ENV NODE_ENV=production
CMD ["node", "src/server.js"]

# Final image for worker
FROM server-builder AS worker
WORKDIR /app/packages/worker
ENV NODE_ENV=production
ENTRYPOINT ["node", "src/worker.js"]