table:
  name: report_request
  schema: public
insert_permissions:
  - role: backoffice
    permission:
      check: {}
      columns:
        - country
        - created_at
        - created_by
        - farm_id
        - farm_name
        - report_data
        - root_id
        - type
    comment: ""
select_permissions:
  - role: backoffice
    permission:
      columns:
        - country
        - created_at
        - created_by
        - farm_id
        - farm_name
        - report_data
        - root_id
        - type
      filter: {}
    comment: ""
update_permissions:
  - role: backoffice
    permission:
      columns:
        - country
        - created_at
        - created_by
        - farm_id
        - farm_name
        - report_data
        - root_id
        - type
      filter: {}
      check: null
    comment: ""
delete_permissions:
  - role: backoffice
    permission:
      filter: {}
    comment: ""
