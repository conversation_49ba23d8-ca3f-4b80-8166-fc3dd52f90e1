table:
  name: prompt_template
  schema: public
insert_permissions:
  - role: backoffice
    permission:
      check: {}
      set:
        created_by: x-hasura-user-id
        updated_by: x-hasura-user-id
      columns:
        - created_by
        - root_id
        - updated_by
        - template
        - type
        - created_at
        - updated_at
        - id
    comment: ""
select_permissions:
  - role: backoffice
    permission:
      columns:
        - created_by
        - root_id
        - updated_by
        - template
        - type
        - created_at
        - updated_at
        - id
      filter: {}
      allow_aggregations: true
    comment: ""
update_permissions:
  - role: backoffice
    permission:
      columns:
        - created_by
        - root_id
        - updated_by
        - template
        - type
        - created_at
        - updated_at
        - id
      filter: {}
      check: {}
    comment: ""
