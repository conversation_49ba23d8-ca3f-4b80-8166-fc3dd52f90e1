table:
  name: active_template
  schema: public
object_relationships:
  - name: prompt_template
    using:
      foreign_key_constraint_on: template_id
insert_permissions:
  - role: backoffice
    permission:
      check: {}
      columns:
        - root_id
        - update_by
        - type
        - updated_at
        - template_id
    comment: ""
select_permissions:
  - role: backoffice
    permission:
      columns:
        - root_id
        - update_by
        - type
        - updated_at
        - template_id
      filter: {}
      allow_aggregations: true
    comment: ""
update_permissions:
  - role: backoffice
    permission:
      columns:
        - root_id
        - update_by
        - type
        - updated_at
        - template_id
      filter: {}
      check: {}
    comment: ""
delete_permissions:
  - role: backoffice
    permission:
      filter: {}
    comment: ""
