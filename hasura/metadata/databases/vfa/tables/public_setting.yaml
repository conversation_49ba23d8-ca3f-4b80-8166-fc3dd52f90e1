table:
  name: setting
  schema: public
insert_permissions:
  - role: backoffice
    permission:
      check: {}
      columns:
        - root_id
        - setting
        - env
    comment: ""
select_permissions:
  - role: backoffice
    permission:
      columns:
        - root_id
        - setting
        - env
      filter: {}
    comment: ""
update_permissions:
  - role: backoffice
    permission:
      columns:
        - root_id
        - setting
        - env
      filter: {}
      check: {}
    comment: ""
