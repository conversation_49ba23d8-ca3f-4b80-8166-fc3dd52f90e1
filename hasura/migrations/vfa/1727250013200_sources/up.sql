CREATE TABLE "public"."source"
(
	"id"      serial NOT NULL,
	"name"    text   NOT NULL,
	"root_id" bigint NOT NULL,
	"env"     text   NOT NULL,
	PRIMARY KEY ("id"),
	UNIQUE ("name", "root_id", "env")
);

CREATE TABLE "public"."source_doc"
(
	"id"        serial NOT NULL,
	"source_id" integer   NOT NULL,
	"file_name" text   NOT NULL,
	"file_data" bytea  NOT NULL,
	PRIMARY KEY ("id"),
	FOREIGN KEY ("source_id") REFERENCES "public"."source" ("id") on update restrict on delete cascade,
	UNIQUE ("source_id", "file_name")
);

CREATE TABLE "public"."source_doc_page"
(
	"source_doc_id" integer NOT NULL,
	"idx"           integer NOT NULL,
	"languages"     text[]  NOT NULL,
	"content"       text    NOT NULL,
	"compressed"    text    NOT NULL,
	"relevant_for"  text[]  NOT NULL,
	PRIMARY KEY ("source_doc_id", "idx"),
	FOREIG<PERSON> KEY ("source_doc_id") REFERENCES "public"."source_doc" ("id") on update restrict on delete cascade
);
