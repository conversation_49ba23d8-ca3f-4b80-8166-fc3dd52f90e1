CREATE TABLE "public"."prompt_template" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "type" text NOT NULL, "root_id" integer NOT NULL, "template" text NOT NULL, "created_at" timestamptz NOT NULL DEFAULT now(), "created_by" integer NOT NULL, "updated_at" timestamptz NOT NULL DEFAULT now(), "updated_by" integer NOT NULL, PRIMARY KEY ("id") );
CREATE OR REPLACE FUNCTION "public"."set_current_timestamp_updated_at"()
RETURNS TRIGGER AS $$
DECLARE
  _new record;
BEGIN
  _new := NEW;
  _new."updated_at" = NOW();
  RETURN _new;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER "set_public_prompt_template_updated_at"
BEFORE UPDATE ON "public"."prompt_template"
FOR EACH ROW
EXECUTE PROCEDURE "public"."set_current_timestamp_updated_at"();
COMMENT ON TRIGGER "set_public_prompt_template_updated_at" ON "public"."prompt_template"
IS 'trigger to set value of column "updated_at" to current timestamp on row update';
CREATE EXTENSION IF NOT EXISTS pgcrypto;
