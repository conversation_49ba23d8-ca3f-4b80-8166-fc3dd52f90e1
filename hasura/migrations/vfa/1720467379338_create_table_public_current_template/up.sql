CREATE TABLE "public"."current_template" ("type" text NOT NULL, "root_id" integer NOT NULL, "template_id" uuid NOT NULL, "updated_at" timestamptz NOT NULL DEFAULT now(), "update_by" integer NOT NULL, PRIMARY KEY ("type","root_id") , FOREIGN KEY ("template_id") REFERENCES "public"."prompt_template"("id") ON UPDATE restrict ON DELETE restrict);
CREATE OR REPLACE FUNCTION "public"."set_current_timestamp_updated_at"()
RETURNS TRIGGER AS $$
DECLARE
  _new record;
BEGIN
  _new := NEW;
  _new."updated_at" = NOW();
  RETURN _new;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER "set_public_current_template_updated_at"
BEFORE UPDATE ON "public"."current_template"
FOR EACH ROW
EXECUTE PROCEDURE "public"."set_current_timestamp_updated_at"();
COMMENT ON TRIGGER "set_public_current_template_updated_at" ON "public"."current_template"
IS 'trigger to set value of column "updated_at" to current timestamp on row update';
