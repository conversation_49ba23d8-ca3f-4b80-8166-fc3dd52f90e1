{"name": "pigbot", "private": true, "peerDependencies": {"typescript": "^5.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/jest": "^29.5.13", "@types/ms": "^0.7.34", "concurrently": "^8.2.2", "eslint": "~9.8.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "husky": "^9.1.6", "jest": "^29.7.0", "lint-staged": "^15.2.10", "prettier": "3.3.3", "ts-jest": "^29.2.5", "typescript": "^5.6.2", "typescript-eslint": "^7.18.0"}, "scripts": {"docker-compose-up": "cd dependencies && docker compose up -d --wait && pnpm run temporal-register-pigbot", "temporal-register-pigbot": "docker exec temporal-admin-tools tctl --ns pigbot namespace register || true", "hasura": "pnpm run hasura-apply & cd hasura && hasura console --no-browser", "hasura-apply": "cd hasura && hasura metadata apply && hasura migrate apply --all-databases && hasura metadata apply", "deps": "pnpm run docker-compose-up && pnpm run hasura", "stop-deps": "cd dependencies && docker compose down", "build-server": "pnpm --filter 'pigbot-core...' run build && pnpm --filter 'pigbot-worker...' run build && pnpm --filter 'pigbot-backend...' run build", "codegen": "pnpm run hasura-apply && pnpm --filter 'pigbot-frontend...' run codegen", "prestart": "pnpm install", "start": "pnpm run docker-compose-up && concurrently --raw --names \"hasura,backend,worker,frontend\" --no-prefix --kill-others-on-fail \"pnpm run hasura\" \"pnpm --filter 'pigbot-backend' run start\" \"pnpm --filter 'pigbot-worker' run start\" \"pnpm --filter 'pigbot-frontend' run start\"", "serve": "pnpm run docker-compose-up && concurrently --raw --names \"hasura,backend,worker,frontend\" --no-prefix --kill-others-on-fail \"pnpm run hasura\" \"pnpm --filter 'pigbot-backend' run start\" \"pnpm --filter 'pigbot-worker' run start\" \"pnpm --filter 'pigbot-frontend' run serve\"", "lint:fix": "eslint --fix 'packages/**/*.{js,ts,tsx}'", "lint": "eslint 'packages/**/*.{js,ts,tsx}' --max-warnings=0 --fix", "prepare": "husky", "test": "NODE_OPTIONS='--max-old-space-size=4092' jest", "check-all": "pnpm install && pnpm run lint && pnpm run docker-build-all && pnpm run test", "docker-build-all": "docker build -f Dockerfile-packages --target server-builder . && concurrently --kill-others-on-fail \"docker build -f Dockerfile-packages --target frontend .\" \"docker build -f Dockerfile-packages --target backend .\" \"docker build -f Dockerfile-packages --target worker .\" \"docker build -f Dockerfile-hasura .\""}, "packageManager": "pnpm@9.15.5", "lint-staged": {"*": ["eslint --fix", "prettier --ignore-unknown --write"]}}